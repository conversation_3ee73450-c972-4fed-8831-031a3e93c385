import 'package:json_annotation/json_annotation.dart';

part 'user_contribution.g.dart';

/// User contribution profile for community system
@JsonSerializable()
class UserContribution {
  final String userId;
  final String? displayName;
  final String? avatarUrl;
  final DateTime joinDate;
  final DateTime? lastContribution;
  final int totalContributions;
  final int verifiedContributions;
  final int reputationScore;
  final List<String> badges;
  final List<String> languagesContributed;
  final int contributionStreak;
  final String rank;
  final Map<String, int> contributionsByType;
  final Map<String, double> languagePairExpertise;
  final bool isVerifiedContributor;
  final bool isModerator;
  final ContributorLevel level;

  UserContribution({
    required this.userId,
    this.displayName,
    this.avatarUrl,
    required this.joinDate,
    this.lastContribution,
    this.totalContributions = 0,
    this.verifiedContributions = 0,
    this.reputationScore = 0,
    this.badges = const [],
    this.languagesContributed = const [],
    this.contributionStreak = 0,
    this.rank = 'Novato',
    this.contributionsByType = const {},
    this.languagePairExpertise = const {},
    this.isVerifiedContributor = false,
    this.isModerator = false,
    this.level = ContributorLevel.beginner,
  });

  /// Create from JSON
  factory UserContribution.fromJson(Map<String, dynamic> json) =>
      _$UserContributionFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserContributionToJson(this);

  /// Create a copy with modified fields
  UserContribution copyWith({
    String? userId,
    String? displayName,
    String? avatarUrl,
    DateTime? joinDate,
    DateTime? lastContribution,
    int? totalContributions,
    int? verifiedContributions,
    int? reputationScore,
    List<String>? badges,
    List<String>? languagesContributed,
    int? contributionStreak,
    String? rank,
    Map<String, int>? contributionsByType,
    Map<String, double>? languagePairExpertise,
    bool? isVerifiedContributor,
    bool? isModerator,
    ContributorLevel? level,
  }) {
    return UserContribution(
      userId: userId ?? this.userId,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      joinDate: joinDate ?? this.joinDate,
      lastContribution: lastContribution ?? this.lastContribution,
      totalContributions: totalContributions ?? this.totalContributions,
      verifiedContributions: verifiedContributions ?? this.verifiedContributions,
      reputationScore: reputationScore ?? this.reputationScore,
      badges: badges ?? this.badges,
      languagesContributed: languagesContributed ?? this.languagesContributed,
      contributionStreak: contributionStreak ?? this.contributionStreak,
      rank: rank ?? this.rank,
      contributionsByType: contributionsByType ?? this.contributionsByType,
      languagePairExpertise: languagePairExpertise ?? this.languagePairExpertise,
      isVerifiedContributor: isVerifiedContributor ?? this.isVerifiedContributor,
      isModerator: isModerator ?? this.isModerator,
      level: level ?? this.level,
    );
  }

  /// Create default user profile
  factory UserContribution.createDefault(String userId) {
    return UserContribution(
      userId: userId,
      joinDate: DateTime.now(),
      rank: 'Novato',
      level: ContributorLevel.beginner,
    );
  }

  /// Get level display name
  String get levelDisplay {
    switch (level) {
      case ContributorLevel.beginner:
        return 'Iniciante';
      case ContributorLevel.intermediate:
        return 'Intermediário';
      case ContributorLevel.advanced:
        return 'Avançado';
      case ContributorLevel.expert:
        return 'Especialista';
      case ContributorLevel.master:
        return 'Mestre';
    }
  }

  /// Get verification rate
  double get verificationRate {
    if (totalContributions == 0) return 0.0;
    return verifiedContributions / totalContributions;
  }

  /// Get days since joining
  int get daysSinceJoining {
    return DateTime.now().difference(joinDate).inDays;
  }

  /// Get average contributions per day
  double get averageContributionsPerDay {
    final days = daysSinceJoining;
    if (days == 0) return 0.0;
    return totalContributions / days;
  }

  /// Check if user is active (contributed in last 30 days)
  bool get isActive {
    if (lastContribution == null) return false;
    return DateTime.now().difference(lastContribution!).inDays <= 30;
  }

  /// Get next level requirements
  Map<String, dynamic> get nextLevelRequirements {
    switch (level) {
      case ContributorLevel.beginner:
        return {
          'level': 'Intermediário',
          'required_contributions': 10,
          'required_reputation': 50,
          'required_verification_rate': 0.7,
        };
      case ContributorLevel.intermediate:
        return {
          'level': 'Avançado',
          'required_contributions': 50,
          'required_reputation': 200,
          'required_verification_rate': 0.8,
        };
      case ContributorLevel.advanced:
        return {
          'level': 'Especialista',
          'required_contributions': 150,
          'required_reputation': 500,
          'required_verification_rate': 0.85,
        };
      case ContributorLevel.expert:
        return {
          'level': 'Mestre',
          'required_contributions': 500,
          'required_reputation': 1000,
          'required_verification_rate': 0.9,
        };
      case ContributorLevel.master:
        return {
          'level': 'Máximo',
          'message': 'Você já atingiu o nível máximo!',
        };
    }
  }

  /// Get progress to next level
  double get progressToNextLevel {
    final requirements = nextLevelRequirements;
    if (requirements.containsKey('message')) return 1.0;

    final reqContributions = requirements['required_contributions'] as int;
    final reqReputation = requirements['required_reputation'] as int;
    final reqVerificationRate = requirements['required_verification_rate'] as double;

    final contributionProgress = (totalContributions / reqContributions).clamp(0.0, 1.0);
    final reputationProgress = (reputationScore / reqReputation).clamp(0.0, 1.0);
    final verificationProgress = (verificationRate / reqVerificationRate).clamp(0.0, 1.0);

    return (contributionProgress + reputationProgress + verificationProgress) / 3;
  }

  /// Get available badges that can be earned
  List<String> get availableBadges {
    final available = <String>[];
    
    // Contribution badges
    if (totalContributions >= 10 && !badges.contains('Contribuidor')) {
      available.add('Contribuidor');
    }
    if (totalContributions >= 100 && !badges.contains('Veterano')) {
      available.add('Veterano');
    }
    if (totalContributions >= 500 && !badges.contains('Lenda')) {
      available.add('Lenda');
    }
    
    // Language badges
    if (languagesContributed.length >= 5 && !badges.contains('Poliglota')) {
      available.add('Poliglota');
    }
    if (languagesContributed.length >= 10 && !badges.contains('Linguista')) {
      available.add('Linguista');
    }
    
    // Quality badges
    if (verificationRate >= 0.9 && !badges.contains('Preciso')) {
      available.add('Preciso');
    }
    if (reputationScore >= 1000 && !badges.contains('Respeitado')) {
      available.add('Respeitado');
    }
    
    // Streak badges
    if (contributionStreak >= 7 && !badges.contains('Consistente')) {
      available.add('Consistente');
    }
    if (contributionStreak >= 30 && !badges.contains('Dedicado')) {
      available.add('Dedicado');
    }
    
    return available;
  }

  /// Get expertise level for a language pair
  double getLanguagePairExpertise(String sourceLanguage, String targetLanguage) {
    final key = '${sourceLanguage}_to_$targetLanguage';
    return languagePairExpertise[key] ?? 0.0;
  }

  /// Get top language pairs by expertise
  List<MapEntry<String, double>> get topLanguagePairs {
    final entries = languagePairExpertise.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  /// Get contribution summary
  Map<String, dynamic> get contributionSummary {
    return {
      'total': totalContributions,
      'verified': verifiedContributions,
      'verification_rate': verificationRate,
      'reputation': reputationScore,
      'streak': contributionStreak,
      'languages': languagesContributed.length,
      'level': levelDisplay,
      'badges': badges.length,
      'is_active': isActive,
    };
  }

  @override
  String toString() {
    return 'UserContribution(userId: $userId, level: $level, contributions: $totalContributions)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserContribution && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;
}

/// Contributor experience levels
enum ContributorLevel {
  beginner,     // 0-9 contributions
  intermediate, // 10-49 contributions
  advanced,     // 50-149 contributions
  expert,       // 150-499 contributions
  master,       // 500+ contributions
}

/// Badge categories
enum BadgeCategory {
  contribution, // Based on number of contributions
  quality,      // Based on quality metrics
  language,     // Based on language diversity
  streak,       // Based on consistency
  special,      // Special achievements
}
