import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/services/gemma_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/models/translation_result.dart';
import '../widgets/translation_input_field.dart';
import '../widgets/translation_result_card.dart';
import '../widgets/translation_actions.dart';

/// Text translation page with advanced input and result display
class TextTranslationPage extends ConsumerStatefulWidget {
  const TextTranslationPage({super.key});

  @override
  ConsumerState<TextTranslationPage> createState() => _TextTranslationPageState();
}

class _TextTranslationPageState extends ConsumerState<TextTranslationPage>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  TranslationResult? _currentResult;
  bool _isTranslating = false;
  String _inputText = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _textController.addListener(_onTextChanged);
    
    // Auto-focus on text field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  void _onTextChanged() {
    setState(() {
      _inputText = _textController.text;
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final theme = Theme.of(context);
    final languagePreferences = ref.watch(languagePreferencesProvider);
    
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Input Section
                _buildInputSection(theme, languagePreferences),
                
                const SizedBox(height: 16),
                
                // Translation Button
                _buildTranslationButton(theme, languagePreferences),
                
                const SizedBox(height: 16),
                
                // Result Section
                if (_currentResult != null || _isTranslating)
                  _buildResultSection(theme),
                
                // Quick Actions
                if (_inputText.isNotEmpty)
                  _buildQuickActions(theme),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputSection(ThemeData theme, LanguagePreferences preferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.edit,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Texto para traduzir',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (_inputText.isNotEmpty)
                  Text(
                    '${_inputText.length} caracteres',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            TranslationInputField(
              controller: _textController,
              focusNode: _focusNode,
              hintText: 'Digite ou cole o texto aqui...',
              maxLines: 8,
              onSubmitted: (_) => _performTranslation(preferences),
            ),
            
            const SizedBox(height: 12),
            
            // Input Actions
            Row(
              children: [
                TextButton.icon(
                  onPressed: _pasteFromClipboard,
                  icon: const Icon(Icons.paste, size: 16),
                  label: const Text('Colar'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: _clearInput,
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('Limpar'),
                ),
                const Spacer(),
                if (_inputText.isNotEmpty)
                  TextButton.icon(
                    onPressed: () => _performTranslation(preferences),
                    icon: const Icon(Icons.translate, size: 16),
                    label: const Text('Traduzir'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationButton(ThemeData theme, LanguagePreferences preferences) {
    final canTranslate = _inputText.trim().isNotEmpty && !_isTranslating;
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: canTranslate ? () => _performTranslation(preferences) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          disabledBackgroundColor: Colors.grey[300],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isTranslating
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text('Traduzindo...'),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.translate),
                  const SizedBox(width: 8),
                  Text(
                    canTranslate ? 'Traduzir Texto' : 'Digite um texto',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildResultSection(ThemeData theme) {
    if (_isTranslating) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Processando com Gemma 3N...',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    if (_currentResult == null) return const SizedBox.shrink();

    return TranslationResultCard(
      result: _currentResult!,
      onCopy: () => _copyToClipboard(_currentResult!.translatedText),
      onShare: () => _shareResult(_currentResult!),
      onSave: () => _saveToHistory(_currentResult!),
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ações Rápidas',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _QuickActionChip(
                  icon: Icons.copy,
                  label: 'Copiar texto',
                  onPressed: () => _copyToClipboard(_inputText),
                ),
                _QuickActionChip(
                  icon: Icons.clear,
                  label: 'Limpar',
                  onPressed: _clearInput,
                ),
                _QuickActionChip(
                  icon: Icons.mic,
                  label: 'Ditado por voz',
                  onPressed: _startVoiceInput,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performTranslation(LanguagePreferences preferences) async {
    if (_inputText.trim().isEmpty || _isTranslating) return;

    setState(() {
      _isTranslating = true;
      _currentResult = null;
    });

    try {
      final result = await GemmaService.instance.translateText(
        text: _inputText.trim(),
        targetLanguage: preferences.targetLanguage,
        sourceLanguage: preferences.sourceLanguage != 'auto' 
            ? preferences.sourceLanguage 
            : null,
      );

      setState(() {
        _currentResult = result;
      });

      // Add to history
      ref.read(translationHistoryProvider.notifier).addTranslation(result);

      // Track analytics
      AnalyticsService.instance.trackTranslation(
        sourceLanguage: result.sourceLanguage,
        targetLanguage: result.targetLanguage,
        translationType: 'text',
        textLength: _inputText.length,
        processingTime: result.processingTime ?? Duration.zero,
        confidence: result.confidence,
      );

    } catch (e) {
      _showErrorSnackBar('Erro na tradução: $e');
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        _textController.text = clipboardData!.text!;
        _focusNode.requestFocus();
      }
    } catch (e) {
      _showErrorSnackBar('Erro ao colar: $e');
    }
  }

  void _clearInput() {
    _textController.clear();
    setState(() {
      _currentResult = null;
    });
    _focusNode.requestFocus();
  }

  Future<void> _copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      _showSuccessSnackBar('Texto copiado!');
    } catch (e) {
      _showErrorSnackBar('Erro ao copiar: $e');
    }
  }

  void _shareResult(TranslationResult result) {
    // Implement sharing functionality
    _showInfoSnackBar('Compartilhamento em desenvolvimento');
  }

  void _saveToHistory(TranslationResult result) {
    ref.read(translationHistoryProvider.notifier).addTranslation(result);
    _showSuccessSnackBar('Salvo no histórico!');
  }

  void _startVoiceInput() {
    // Implement voice input
    _showInfoSnackBar('Ditado por voz em desenvolvimento');
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}

class _QuickActionChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;

  const _QuickActionChip({
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      onPressed: onPressed,
      backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
      labelStyle: TextStyle(color: AppTheme.primaryColor),
    );
  }
}
