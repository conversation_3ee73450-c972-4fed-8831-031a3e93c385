# 🏥 DermatoGemma Multi-Detection System v2.0
# Essential Dependencies for Dermatological AI Analysis

# ===== CORE DEPENDENCIES =====
# Python Standard Library Extensions
pathlib2>=2.3.0; python_version<"3.4"
typing-extensions>=4.0.0; python_version<"3.8"

# ===== SCIENTIFIC COMPUTING =====
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0

# ===== IMAGE PROCESSING & COMPUTER VISION =====
opencv-python>=4.5.0,<5.0.0
Pillow>=8.3.0,<11.0.0
scikit-image>=0.18.0,<1.0.0

# ===== MACHINE LEARNING =====
scikit-learn>=1.0.0,<2.0.0

# ===== GUI FRAMEWORK =====
customtkinter>=5.2.0,<6.0.0

# ===== UTILITIES =====
requests>=2.31.0,<3.0.0
tqdm>=4.65.0,<5.0.0
python-dateutil>=2.8.0,<3.0.0
pydantic>=2.0.0,<3.0.0

# ===== CONFIGURATION & LOGGING =====
pyyaml>=5.4.0,<7.0.0

# ===== SYSTEM MONITORING =====
psutil>=5.8.0,<6.0.0

# ===== OPTIONAL DEPENDENCIES =====
# Uncomment if using specific features:
# pandas>=1.3.0,<3.0.0          # For data analysis features
# joblib>=1.0.0,<2.0.0          # For parallel processing
# colorlog>=6.4.0,<7.0.0        # For enhanced logging
# cryptography>=3.4.0,<42.0.0   # For data encryption
# sqlalchemy>=1.4.0,<3.0.0      # For database features
# pydicom>=2.2.0,<3.0.0         # For DICOM medical images

# ===== INSTALLATION INSTRUCTIONS =====
# 1. Python 3.8+ required
# 2. Create virtual environment: python -m venv venv
# 3. Activate: venv\Scripts\activate (Windows) or source venv/bin/activate (Linux/Mac)
# 4. Install: pip install -r requirements.txt
# 5. Run: python main.py --modern

# ===== SYSTEM REQUIREMENTS =====
# - RAM: 8GB minimum, 16GB recommended
# - Storage: 2GB free space
# - OS: Windows 10+, macOS 10.14+, Ubuntu 18.04+
# - Python: 3.8 to 3.11 (3.12+ not fully tested)

# ===== MEDICAL COMPLIANCE =====
# - Research and educational use only
# - Not for primary medical diagnosis
# - Requires physician oversight for clinical decisions
# - Local processing ensures data privacy
