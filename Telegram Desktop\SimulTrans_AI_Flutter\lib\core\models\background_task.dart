import 'package:json_annotation/json_annotation.dart';

part 'background_task.g.dart';

/// Background task model for heavy computational work
@JsonSerializable()
class BackgroundTask {
  String? id;
  final BackgroundTaskType type;
  final String name;
  final String description;
  final Map<String, dynamic> parameters;
  final int priority; // 1-10, higher is more priority
  
  BackgroundTaskStatus status;
  DateTime? queuedAt;
  DateTime? startedAt;
  DateTime? completedAt;
  String? error;
  Map<String, dynamic>? result;

  BackgroundTask({
    this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.parameters,
    this.priority = 5,
    this.status = BackgroundTaskStatus.pending,
    this.queuedAt,
    this.startedAt,
    this.completedAt,
    this.error,
    this.result,
  });

  /// Create from JSON
  factory BackgroundTask.fromJson(Map<String, dynamic> json) =>
      _$BackgroundTaskFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$BackgroundTaskToJson(this);

  /// Create a copy with modified fields
  BackgroundTask copyWith({
    String? id,
    BackgroundTaskType? type,
    String? name,
    String? description,
    Map<String, dynamic>? parameters,
    int? priority,
    BackgroundTaskStatus? status,
    DateTime? queuedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? error,
    Map<String, dynamic>? result,
  }) {
    return BackgroundTask(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      parameters: parameters ?? this.parameters,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      queuedAt: queuedAt ?? this.queuedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      error: error ?? this.error,
      result: result ?? this.result,
    );
  }

  /// Get task duration
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// Get queue wait time
  Duration? get queueWaitTime {
    if (queuedAt == null || startedAt == null) return null;
    return startedAt!.difference(queuedAt!);
  }

  /// Check if task is completed
  bool get isCompleted => status == BackgroundTaskStatus.completed;

  /// Check if task failed
  bool get isFailed => status == BackgroundTaskStatus.failed;

  /// Check if task is running
  bool get isRunning => status == BackgroundTaskStatus.running;

  /// Get status display string
  String get statusDisplay {
    switch (status) {
      case BackgroundTaskStatus.pending:
        return 'Pendente';
      case BackgroundTaskStatus.queued:
        return 'Na fila';
      case BackgroundTaskStatus.running:
        return 'Executando';
      case BackgroundTaskStatus.completed:
        return 'Concluído';
      case BackgroundTaskStatus.failed:
        return 'Falhou';
      case BackgroundTaskStatus.cancelled:
        return 'Cancelado';
    }
  }

  /// Get priority display string
  String get priorityDisplay {
    if (priority >= 8) return 'Muito Alta';
    if (priority >= 6) return 'Alta';
    if (priority >= 4) return 'Média';
    if (priority >= 2) return 'Baixa';
    return 'Muito Baixa';
  }

  /// Factory methods for common task types
  static BackgroundTask createTranslationTask({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    int priority = 5,
  }) {
    return BackgroundTask(
      type: BackgroundTaskType.translation,
      name: 'Tradução de Texto',
      description: 'Traduzir texto de $sourceLanguage para $targetLanguage',
      parameters: {
        'text': text,
        'source_language': sourceLanguage,
        'target_language': targetLanguage,
        'context': context,
      },
      priority: priority,
    );
  }

  static BackgroundTask createImageProcessingTask({
    required String imagePath,
    required String targetLanguage,
    String? sourceLanguage,
    int priority = 6,
  }) {
    return BackgroundTask(
      type: BackgroundTaskType.imageProcessing,
      name: 'Processamento de Imagem',
      description: 'Extrair e traduzir texto da imagem',
      parameters: {
        'image_path': imagePath,
        'target_language': targetLanguage,
        'source_language': sourceLanguage,
      },
      priority: priority,
    );
  }

  static BackgroundTask createAudioProcessingTask({
    required String audioPath,
    required String targetLanguage,
    String? sourceLanguage,
    int priority = 7,
  }) {
    return BackgroundTask(
      type: BackgroundTaskType.audioProcessing,
      name: 'Processamento de Áudio',
      description: 'Transcrever e traduzir áudio',
      parameters: {
        'audio_path': audioPath,
        'target_language': targetLanguage,
        'source_language': sourceLanguage,
      },
      priority: priority,
    );
  }

  static BackgroundTask createCacheOptimizationTask({
    int priority = 2,
  }) {
    return BackgroundTask(
      type: BackgroundTaskType.cacheOptimization,
      name: 'Otimização de Cache',
      description: 'Limpar e otimizar cache do sistema',
      parameters: {},
      priority: priority,
    );
  }

  static BackgroundTask createModelDownloadTask({
    required String modelId,
    required String downloadUrl,
    int priority = 8,
  }) {
    return BackgroundTask(
      type: BackgroundTaskType.modelDownload,
      name: 'Download de Modelo',
      description: 'Baixar modelo $modelId',
      parameters: {
        'model_id': modelId,
        'download_url': downloadUrl,
      },
      priority: priority,
    );
  }

  @override
  String toString() {
    return 'BackgroundTask(id: $id, type: $type, name: $name, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BackgroundTask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Background task types
enum BackgroundTaskType {
  translation,
  imageProcessing,
  audioProcessing,
  videoProcessing,
  cacheOptimization,
  modelDownload,
  dataSync,
}

/// Background task status
enum BackgroundTaskStatus {
  pending,
  queued,
  running,
  completed,
  failed,
  cancelled,
}

/// Background task update model
class BackgroundTaskUpdate {
  final String taskId;
  final BackgroundTaskStatus status;
  final double progress; // 0.0 to 1.0
  final String? message;
  final String? error;
  final dynamic result;
  final DateTime timestamp;

  BackgroundTaskUpdate({
    required this.taskId,
    required this.status,
    required this.progress,
    this.message,
    this.error,
    this.result,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Get progress percentage
  String get progressPercentage => '${(progress * 100).toInt()}%';

  /// Check if update indicates completion
  bool get isCompleted => status == BackgroundTaskStatus.completed;

  /// Check if update indicates failure
  bool get isFailed => status == BackgroundTaskStatus.failed;

  @override
  String toString() {
    return 'BackgroundTaskUpdate(taskId: $taskId, status: $status, progress: $progressPercentage)';
  }
}
