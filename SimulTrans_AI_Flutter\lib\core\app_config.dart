/// Application configuration constants and settings
class AppConfig {
  static const String appName = 'SimulTrans AI';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Tradutor Simultâneo Multimodal Offline com Ollama + Gemma';

  // Ollama Model Configuration (Primary)
  static const String defaultModelName = 'gemma3n:e2b';
  static const String fallbackModelName = 'gemma2:2b';

  // Ollama Configuration
  static const String defaultOllamaHost = 'http://localhost:11434';
  static const int defaultOllamaTimeout = 600;
  static const int defaultOllamaConnectionTimeout = 30;
  static const int defaultOllamaImageTimeout = 900;

  // Supported Ollama models
  static const List<String> supportedOllamaModels = [
    'gemma3n:e2b',
    'gemma2:2b',
    'gemma2:9b',
    'gemma2:27b',
  ];
  
  // Performance Configuration
  static const int maxTokens = 4096;
  static const int contextWindow = 32768;
  static const double defaultTemperature = 0.7;
  static const int defaultTopK = 40;
  static const double defaultTopP = 0.9;
  
  // Cache Configuration
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration cacheExpiration = Duration(days: 7);
  static const int maxTranslationHistory = 1000;
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);
  static const int maxImageSize = 1024;
  static const int imageQuality = 85;
  
  // Audio Configuration
  static const int audioSampleRate = 16000;
  static const Duration maxAudioDuration = Duration(minutes: 10);
  static const Duration audioChunkDuration = Duration(seconds: 5);
  
  // Video Configuration
  static const Duration maxVideoDuration = Duration(minutes: 5);
  static const int videoFrameRate = 30;
  static const int maxVideoFrames = 10;
  
  // Network Configuration
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration downloadTimeout = Duration(minutes: 30);
  static const int maxRetries = 3;
  
  // Language Configuration
  static const String defaultSourceLanguage = 'auto';
  static const String defaultTargetLanguage = 'en';
  static const int maxSupportedLanguages = 140;
  
  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enableCommunityFeedback = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enablePerformanceMonitoring = true;
  
  // Debug Configuration
  static const bool isDebugMode = true; // Should be false in production
  static const bool enableVerboseLogging = true;
  static const bool enablePerformanceProfiling = false;
  
  // API Configuration
  static const String ollamaApiUrl = 'http://localhost:11434';
  static const String feedbackApiUrl = 'https://api.simul-trans.ai/feedback';
  static const String analyticsApiUrl = 'https://api.simul-trans.ai/analytics';

  // Legacy Hugging Face (not used with Ollama)
  static const String huggingFaceApiUrl = 'https://huggingface.co';
  
  // Storage Keys
  static const String userPreferencesKey = 'user_preferences';
  static const String translationHistoryKey = 'translation_history';
  static const String modelCacheKey = 'model_cache';
  static const String languagePreferencesKey = 'language_preferences';
  static const String performanceMetricsKey = 'performance_metrics';
  
  // Error Messages
  static const String ollamaConnectionError = 'Erro ao conectar com Ollama. Verifique se está rodando.';
  static const String modelNotFoundError = 'Modelo não encontrado. Execute: ollama pull gemma3n:e2b';
  static const String modelInitializationError = 'Erro ao inicializar o modelo. Tente novamente.';
  static const String translationError = 'Erro na tradução. Tente novamente.';
  static const String networkError = 'Erro de conexão. Verifique sua internet.';
  static const String permissionError = 'Permissão necessária para continuar.';

  // Success Messages
  static const String ollamaConnectedSuccess = 'Conectado ao Ollama com sucesso!';
  static const String translationSuccess = 'Tradução concluída!';
  static const String feedbackSubmitted = 'Feedback enviado com sucesso!';
  
  // Supported file formats
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp', 'bmp'];
  static const List<String> supportedAudioFormats = ['wav', 'mp3', 'ogg', 'm4a', 'flac'];
  static const List<String> supportedVideoFormats = ['mp4', 'avi', 'mov', 'mkv', 'webm'];
  
  // Privacy and Security
  static const bool enableDataEncryption = true;
  static const bool enableBiometricAuth = false;
  static const Duration sessionTimeout = Duration(hours: 24);
}
