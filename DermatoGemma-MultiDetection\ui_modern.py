#!/usr/bin/env python3
"""
DermatoGemma Multi-Detection System v2.0
Modern UI/UX - Revolutionary Visual Interface

Following RetinoblastoGemma-App design patterns with advanced visual elements,
modern styling, and professional medical interface design.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import numpy as np
import cv2
from pathlib import Path
import threading
import time
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional, Any

# Apply warning suppressions
try:
    from fix_warnings import apply_runtime_fixes
    apply_runtime_fixes()
except ImportError:
    import warnings
    import os
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Configure CustomTkinter
ctk.set_appearance_mode("light")  # Professional medical appearance
ctk.set_default_color_theme("blue")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModernDermatoGemmaUI:
    """
    Modern UI/UX for DermatoGemma Multi-Detection System
    Revolutionary visual interface following RetinoblastoGemma-App design
    """
    
    def __init__(self):
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("DermatoGemma Multi-Detection System v2.0 - Professional Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Color scheme following medical professional standards
        self.colors = {
            'primary': '#2E86AB',      # Medical blue
            'secondary': '#A23B72',    # Accent purple
            'success': '#28A745',      # Success green
            'warning': '#FFC107',      # Warning yellow
            'danger': '#C73E1D',       # Alert red
            'error': '#C73E1D',        # Error red (alias for danger)
            'background': '#F8F9FA',   # Clean white
            'surface': '#FFFFFF',      # Pure white
            'text_primary': '#212529', # Dark text
            'text_secondary': '#6C757D', # Gray text
            'border': '#DEE2E6'        # Light border
        }
        
        # Initialize variables
        self.current_image = None
        self.analysis_results = None
        self.processing = False
        self.stop_requested = False

        # Initialize focus areas (will be populated in create_sidebar)
        self.focus_areas = {}

        # Initialize other UI variables (will be set in create_sidebar)
        self.selected_extraction_mode = None
        self.enable_agno_teams = None
        self.selected_agno_mode = None
        self.selected_analysis_mode = None
        self.selected_specific_disease = None
        self.available_diseases = []
        
        # Setup UI components
        self.setup_styles()
        self.create_header()
        self.create_main_layout()
        self.create_sidebar()
        self.create_analysis_panel()
        self.create_results_panel()
        self.create_footer()
        
        # Initialize analysis engine
        self.init_analysis_engine()
        
        logger.info("🎨 Modern DermatoGemma UI initialized")
    
    def setup_styles(self):
        """Setup modern styling"""
        # Configure ttk styles for professional appearance
        style = ttk.Style()
        style.theme_use('clam')
        
        # Custom button styles
        style.configure(
            "Primary.TButton",
            background=self.colors['primary'],
            foreground='white',
            borderwidth=0,
            focuscolor='none',
            padding=(20, 10)
        )
        
        style.configure(
            "Success.TButton",
            background=self.colors['success'],
            foreground='white',
            borderwidth=0,
            focuscolor='none',
            padding=(15, 8)
        )
        
        # Progress bar style
        style.configure(
            "Medical.Horizontal.TProgressbar",
            background=self.colors['primary'],
            troughcolor=self.colors['border'],
            borderwidth=0,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )
    
    def create_header(self):
        """Create professional header with branding"""
        header_frame = ctk.CTkFrame(
            self.root,
            height=80,
            fg_color=self.colors['primary'],
            corner_radius=0
        )
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Logo and title
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="left", padx=20, pady=15)
        
        # Main title
        title_label = ctk.CTkLabel(
            title_frame,
            text="🏥 DermatoGemma Multi-Detection System",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="white"
        )
        title_label.pack(side="left")
        
        # Version badge
        version_label = ctk.CTkLabel(
            title_frame,
            text="v2.0 Professional",
            font=ctk.CTkFont(size=12),
            text_color="white",
            fg_color=self.colors['secondary'],
            corner_radius=15,
            width=120,
            height=25
        )
        version_label.pack(side="left", padx=(15, 0))

        # Action buttons frame (center)
        action_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        action_frame.pack(side="left", expand=True, padx=20, pady=15)

        # Clear screens button
        self.clear_btn = ctk.CTkButton(
            action_frame,
            text="🧹 Clear Screens",
            command=self.clear_all_screens,
            width=120,
            height=35,
            font=ctk.CTkFont(size=11, weight="bold"),
            fg_color=self.colors['warning'],
            hover_color=self.colors['secondary'],
            text_color="black"
        )
        self.clear_btn.pack(side="left", padx=(0, 10))

        # Stop analysis button
        self.stop_btn = ctk.CTkButton(
            action_frame,
            text="⏹️ Stop Analysis",
            command=self.stop_analysis,
            width=120,
            height=35,
            font=ctk.CTkFont(size=11, weight="bold"),
            fg_color=self.colors['danger'],
            hover_color="#A02622",
            text_color="white",
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=(0, 10))

        # Status indicators
        status_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        status_frame.pack(side="right", padx=20, pady=15)
        
        # AI Status
        self.ai_status = ctk.CTkLabel(
            status_frame,
            text="🤖 AI Ready",
            font=ctk.CTkFont(size=12),
            text_color="white",
            fg_color=self.colors['success'],
            corner_radius=10,
            width=100,
            height=25
        )
        self.ai_status.pack(side="right", padx=(10, 0))
        
        # System Status
        self.system_status = ctk.CTkLabel(
            status_frame,
            text="✅ System Online",
            font=ctk.CTkFont(size=12),
            text_color="white",
            fg_color=self.colors['success'],
            corner_radius=10,
            width=120,
            height=25
        )
        self.system_status.pack(side="right")
    
    def create_main_layout(self):
        """Create main layout with panels"""
        # Main container
        self.main_container = ctk.CTkFrame(
            self.root,
            fg_color=self.colors['background'],
            corner_radius=0
        )
        self.main_container.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Create three-panel layout with scrollable left panel
        # Container for left panel to maintain fixed width
        self.left_panel_container = ctk.CTkFrame(
            self.main_container,
            width=300,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.left_panel_container.pack(side="left", fill="y", padx=(10, 5), pady=10)
        self.left_panel_container.pack_propagate(False)

        # Scrollable left panel for controls
        self.left_panel = ctk.CTkScrollableFrame(
            self.left_panel_container,
            width=280,
            fg_color="transparent",
            scrollbar_button_color=self.colors['primary'],
            scrollbar_button_hover_color=self.colors['secondary']
        )
        self.left_panel.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.center_panel = ctk.CTkFrame(
            self.main_container,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.center_panel.pack(side="left", fill="both", expand=True, padx=5, pady=10)
        
        self.right_panel = ctk.CTkFrame(
            self.main_container,
            width=350,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.right_panel.pack(side="right", fill="y", padx=(5, 10), pady=10)
        self.right_panel.pack_propagate(False)
    
    def create_sidebar(self):
        """Create modern sidebar with controls"""
        # Sidebar header
        sidebar_header = ctk.CTkLabel(
            self.left_panel,
            text="🔬 Analysis Controls",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        sidebar_header.pack(pady=(10, 15))
        
        # Image upload section
        upload_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        upload_frame.pack(fill="x", padx=15, pady=(5, 15))
        
        upload_label = ctk.CTkLabel(
            upload_frame,
            text="📸 Image Upload",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        upload_label.pack(anchor="w")
        
        self.upload_btn = ctk.CTkButton(
            upload_frame,
            text="Select Dermatological Image",
            command=self.upload_image,
            height=40,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=self.colors['primary'],
            hover_color=self.colors['secondary']
        )
        self.upload_btn.pack(fill="x", pady=(5, 0))

        # Separator
        separator1 = ctk.CTkFrame(self.left_panel, height=2, fg_color=self.colors['primary'])
        separator1.pack(fill="x", padx=30, pady=15)

        # Analysis options
        options_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        options_frame.pack(fill="x", padx=15, pady=(10, 15))
        
        options_label = ctk.CTkLabel(
            options_frame,
            text="⚙️ Analysis Options",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        options_label.pack(anchor="w")
        
        # ABCDE Analysis toggle
        self.abcde_var = ctk.BooleanVar(value=True)
        abcde_check = ctk.CTkCheckBox(
            options_frame,
            text="ABCDE Melanoma Analysis",
            variable=self.abcde_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        abcde_check.pack(anchor="w", pady=(10, 5))
        
        # Multi-condition analysis toggle
        self.multi_var = ctk.BooleanVar(value=True)
        multi_check = ctk.CTkCheckBox(
            options_frame,
            text="Multi-Condition Detection",
            variable=self.multi_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        multi_check.pack(anchor="w", pady=5)
        
        # AI Analysis toggle
        self.ai_var = ctk.BooleanVar(value=True)
        ai_check = ctk.CTkCheckBox(
            options_frame,
            text="Gemma-3n AI Analysis",
            variable=self.ai_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        ai_check.pack(anchor="w", pady=5)

        # Analysis mode selection - Redesigned for better UX
        mode_label = ctk.CTkLabel(
            options_frame,
            text="🎯 Analysis Target:",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        mode_label.pack(anchor="w", pady=(15, 10))

        # Create analysis mode frame with better organization
        analysis_mode_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        analysis_mode_frame.pack(fill="x", pady=(0, 10))

        # Analysis mode selection with cleaner interface
        self.analysis_mode = ctk.StringVar(value="multi")

        # Multi-condition option with icon
        multi_frame = ctk.CTkFrame(analysis_mode_frame, fg_color=self.colors['surface'], corner_radius=8)
        multi_frame.pack(fill="x", pady=(0, 8))

        multi_mode_radio = ctk.CTkRadioButton(
            multi_frame,
            text="🔬 Comprehensive Analysis (All 14 diseases)",
            variable=self.analysis_mode,
            value="multi",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary'],
            command=self.on_analysis_mode_change
        )
        multi_mode_radio.pack(anchor="w", padx=15, pady=10)

        multi_desc = ctk.CTkLabel(
            multi_frame,
            text="   Analyzes all supported dermatological conditions simultaneously",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        multi_desc.pack(anchor="w", padx=15, pady=(0, 10))

        # Single-condition option with icon
        single_frame = ctk.CTkFrame(analysis_mode_frame, fg_color=self.colors['surface'], corner_radius=8)
        single_frame.pack(fill="x", pady=(0, 8))

        single_mode_radio = ctk.CTkRadioButton(
            single_frame,
            text="🎯 Targeted Analysis (Specific disease)",
            variable=self.analysis_mode,
            value="single",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary'],
            command=self.on_analysis_mode_change
        )
        single_mode_radio.pack(anchor="w", padx=15, pady=10)

        single_desc = ctk.CTkLabel(
            single_frame,
            text="   Focus analysis on one specific condition for detailed assessment",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        single_desc.pack(anchor="w", padx=15, pady=(0, 10))

        # Disease selection section - Initially hidden
        self.disease_selection_frame = ctk.CTkFrame(options_frame, fg_color=self.colors['surface'], corner_radius=8)

        disease_header = ctk.CTkLabel(
            self.disease_selection_frame,
            text="🏥 Select Target Disease:",
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=self.colors['text_primary']
        )
        disease_header.pack(anchor="w", padx=15, pady=(15, 5))

        # Organized disease categories for better UX
        self.disease_categories = {
            "🔴 High Priority (Malignant)": [
                ("melanoma", "Melanoma"),
                ("basal_cell_carcinoma", "Basal Cell Carcinoma"),
                ("squamous_cell_carcinoma", "Squamous Cell Carcinoma")
            ],
            "🟡 Medium Priority (Precancerous/Infectious)": [
                ("actinic_keratoses", "Actinic Keratoses"),
                ("monkeypox", "Monkeypox"),
                ("measles", "Measles"),
                ("chickenpox", "Chickenpox"),
                ("cowpox", "Cowpox"),
                ("hfmd", "Hand, Foot & Mouth Disease")
            ],
            "🟢 Low Priority (Benign)": [
                ("melanocytic_nevi", "Melanocytic Nevi (Moles)"),
                ("benign_keratosis_like_lesions", "Benign Keratosis-like Lesions"),
                ("dermatofibroma", "Dermatofibroma"),
                ("vascular_lesions", "Vascular Lesions"),
                ("healthy", "Healthy Skin Assessment")
            ]
        }

        # Create disease selection with categories
        self.selected_disease = ctk.StringVar(value="melanoma")

        # Create a scrollable frame for disease selection
        disease_scroll_frame = ctk.CTkScrollableFrame(
            self.disease_selection_frame,
            height=200,
            fg_color="transparent"
        )
        disease_scroll_frame.pack(fill="both", expand=True, padx=15, pady=(5, 15))

        # Add diseases by category
        for category, diseases in self.disease_categories.items():
            # Category header
            category_label = ctk.CTkLabel(
                disease_scroll_frame,
                text=category,
                font=ctk.CTkFont(size=11, weight="bold"),
                text_color=self.colors['text_primary']
            )
            category_label.pack(anchor="w", pady=(10, 5))

            # Disease options in this category
            for disease_key, disease_name in diseases:
                disease_radio = ctk.CTkRadioButton(
                    disease_scroll_frame,
                    text=disease_name,
                    variable=self.selected_disease,
                    value=disease_key,
                    font=ctk.CTkFont(size=10),
                    text_color=self.colors['text_primary']
                )
                disease_radio.pack(anchor="w", padx=20, pady=2)

        # Agno Teams Multi-Agent Analysis
        agno_label = ctk.CTkLabel(
            options_frame,
            text="🤖 Agno Teams Multi-Agent Analysis:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary']
        )
        agno_label.pack(anchor="w", pady=(15, 5))

        # Enable Agno Teams toggle (DEFAULT: YES)
        self.enable_agno_teams = ctk.BooleanVar(value=True)  # ✅ DEFAULT: SIM
        agno_check = ctk.CTkCheckBox(
            options_frame,
            text="✅ Enable Autonomous Multi-Agent Analysis (Recommended)",
            variable=self.enable_agno_teams,
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_primary']
        )
        agno_check.pack(anchor="w", pady=2)

        # Agno Teams mode selection
        agno_mode_label = ctk.CTkLabel(
            options_frame,
            text="Team Mode:",
            font=ctk.CTkFont(size=11, weight="bold"),
            text_color=self.colors['text_primary']
        )
        agno_mode_label.pack(anchor="w", pady=(5, 2))

        self.agno_modes = ["coordinate", "collaborate", "route"]
        self.selected_agno_mode = ctk.StringVar(value="coordinate")
        self.agno_mode_dropdown = ctk.CTkComboBox(
            options_frame,
            values=self.agno_modes,
            variable=self.selected_agno_mode,
            font=ctk.CTkFont(size=10),
            width=280
        )
        self.agno_mode_dropdown.pack(anchor="w", pady=2)

        # Image Data Extraction options
        extraction_label = ctk.CTkLabel(
            options_frame,
            text="Image Data Extraction (Gemma 3n-E4B):",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary']
        )
        extraction_label.pack(anchor="w", pady=(15, 5))

        # Enable extraction toggle
        self.enable_extraction = ctk.BooleanVar(value=True)
        extraction_check = ctk.CTkCheckBox(
            options_frame,
            text="Enable Visual Data Extraction",
            variable=self.enable_extraction,
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_primary']
        )
        extraction_check.pack(anchor="w", pady=2)

        # Extraction mode selection
        extraction_mode_label = ctk.CTkLabel(
            options_frame,
            text="Extraction Mode:",
            font=ctk.CTkFont(size=11, weight="bold"),
            text_color=self.colors['text_primary']
        )
        extraction_mode_label.pack(anchor="w", pady=(5, 2))

        self.extraction_modes = ["comprehensive", "focused", "summary", "detailed"]
        self.selected_extraction_mode = ctk.StringVar(value="comprehensive")
        self.extraction_mode_dropdown = ctk.CTkComboBox(
            options_frame,
            values=self.extraction_modes,
            variable=self.selected_extraction_mode,
            font=ctk.CTkFont(size=10),
            width=280
        )
        self.extraction_mode_dropdown.pack(anchor="w", pady=2)

        # Focus areas selection
        focus_areas_label = ctk.CTkLabel(
            options_frame,
            text="Focus Areas:",
            font=ctk.CTkFont(size=11, weight="bold"),
            text_color=self.colors['text_primary']
        )
        focus_areas_label.pack(anchor="w", pady=(10, 5))

        # Define focus areas with checkboxes
        self.focus_areas = {}
        focus_area_options = [
            ("colors", "Color Analysis"),
            ("textures", "Texture Analysis"),
            ("patterns", "Pattern Recognition"),
            ("morphology", "Morphology Analysis"),
            ("medical_features", "Medical Features")
        ]

        for area_key, area_label in focus_area_options:
            self.focus_areas[area_key] = ctk.BooleanVar(value=True)  # Default all enabled
            area_check = ctk.CTkCheckBox(
                options_frame,
                text=area_label,
                variable=self.focus_areas[area_key],
                font=ctk.CTkFont(size=10),
                text_color=self.colors['text_primary']
            )
            area_check.pack(anchor="w", padx=20, pady=1)



        # Separator
        separator2 = ctk.CTkFrame(self.left_panel, height=2, fg_color=self.colors['primary'])
        separator2.pack(fill="x", padx=30, pady=15)

        # Unified analysis button (All-in-One)
        self.analyze_btn = ctk.CTkButton(
            self.left_panel,
            text="🚀 Start Unified Analysis (All-in-One)",
            command=self.start_unified_analysis,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=self.colors['success'],
            hover_color=self.colors['danger'],
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", padx=15, pady=(15, 8))

        # Note: Individual analysis buttons removed - now using unified analysis
        # The unified analysis includes:
        # - Multi-condition dermatological analysis
        # - Image data extraction with Gemma 3n-E4B
        # - Agno Teams multi-agent analysis (optional)

        # Add info label about unified analysis
        unified_info = ctk.CTkLabel(
            self.left_panel,
            text="ℹ️ Unified analysis includes multi-condition analysis,\nimage data extraction, and optional Agno Teams",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary'],
            justify="center"
        )
        unified_info.pack(pady=(5, 15))

        # Keep references for compatibility (but hidden)
        self.extract_btn = None
        self.agno_teams_btn = None

        # Reset button
        self.reset_btn = ctk.CTkButton(
            self.left_panel,
            text="🔄 Reset & Clear Results",
            command=self.reset_analysis,
            height=40,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=self.colors['secondary'],
            hover_color=self.colors['primary'],
            state="disabled"
        )
        self.reset_btn.pack(fill="x", padx=15, pady=(5, 15))

        # Separator
        separator3 = ctk.CTkFrame(self.left_panel, height=2, fg_color=self.colors['primary'])
        separator3.pack(fill="x", padx=30, pady=10)

        # Progress section
        progress_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        progress_frame.pack(fill="x", padx=15, pady=(10, 15))
        
        self.progress_label = ctk.CTkLabel(
            progress_frame,
            text="Ready for analysis",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.progress_label.pack(anchor="w")
        
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            height=8,
            progress_color=self.colors['primary']
        )
        self.progress_bar.pack(fill="x", pady=(5, 0))
        self.progress_bar.set(0)

        # Final spacer to ensure all content is visible
        final_spacer = ctk.CTkFrame(self.left_panel, height=20, fg_color="transparent")
        final_spacer.pack(fill="x", pady=10)
    
    def create_analysis_panel(self):
        """Create central analysis panel with image display"""
        # Panel header
        analysis_header = ctk.CTkLabel(
            self.center_panel,
            text="🖼️ Image Analysis Workspace",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        analysis_header.pack(pady=(20, 10))
        
        # Image display area
        self.image_frame = ctk.CTkFrame(
            self.center_panel,
            fg_color=self.colors['border'],
            corner_radius=10
        )
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Placeholder for image
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="📷\n\nNo image selected\n\nClick 'Select Dermatological Image' to begin",
            font=ctk.CTkFont(size=16),
            text_color=self.colors['text_secondary']
        )
        self.image_label.pack(expand=True)
        
        # Image info panel
        self.info_frame = ctk.CTkFrame(self.center_panel, height=60, fg_color="transparent")
        self.info_frame.pack(fill="x", padx=20, pady=(0, 20))
        self.info_frame.pack_propagate(False)
        
        self.image_info = ctk.CTkLabel(
            self.info_frame,
            text="Image information will appear here",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.image_info.pack(pady=10)
    
    def create_results_panel(self):
        """Create results panel with detailed analysis"""
        # Panel header
        results_header = ctk.CTkLabel(
            self.right_panel,
            text="📊 Analysis Results",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        results_header.pack(pady=(20, 10))
        
        # Results container with scrolling
        self.results_container = ctk.CTkScrollableFrame(
            self.right_panel,
            fg_color="transparent"
        )
        self.results_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Placeholder
        self.results_placeholder = ctk.CTkLabel(
            self.results_container,
            text="🔬\n\nAnalysis results will\nappear here after\nprocessing an image",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        self.results_placeholder.pack(pady=50)
    
    def create_footer(self):
        """Create professional footer"""
        footer_frame = ctk.CTkFrame(
            self.root,
            height=40,
            fg_color=self.colors['border'],
            corner_radius=0
        )
        footer_frame.pack(fill="x", side="bottom")
        footer_frame.pack_propagate(False)
        
        # Footer content
        footer_left = ctk.CTkLabel(
            footer_frame,
            text="© 2025 DermatoGemma Multi-Detection System - Professional Medical AI",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        footer_left.pack(side="left", padx=20, pady=10)
        
        footer_right = ctk.CTkLabel(
            footer_frame,
            text=f"Session: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        footer_right.pack(side="right", padx=20, pady=10)
    
    def init_analysis_engine(self):
        """Initialize analysis engine"""
        try:
            # Import analysis components
            import sys
            from pathlib import Path
            project_root = Path(__file__).parent
            sys.path.insert(0, str(project_root))
            
            from core.multi_condition_engine import MultiConditionAnalysisEngine
            self.analysis_engine = MultiConditionAnalysisEngine()
            
            self.update_status("✅ Analysis engine ready", "success")
            logger.info("✅ Analysis engine initialized")
            
        except Exception as e:
            self.update_status("❌ Engine initialization failed", "danger")
            logger.error(f"❌ Failed to initialize analysis engine: {e}")
            self.analysis_engine = None

    def display_agno_teams_results(self, results: dict):
        """Display Agno Teams analysis results"""
        try:
            # Clear previous results
            for widget in self.results_container.winfo_children():
                widget.destroy()

            # Create main results frame
            main_frame = ctk.CTkFrame(self.results_container)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Title
            title_label = ctk.CTkLabel(
                main_frame,
                text="🤖 Agno Teams Multi-Agent Analysis Results",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.colors['primary']
            )
            title_label.pack(pady=(10, 20))

            # Create scrollable frame for results
            scrollable_frame = ctk.CTkScrollableFrame(
                main_frame,
                height=500,
                fg_color=self.colors['card_bg']
            )
            scrollable_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            # Team Analysis Summary
            team_analysis = results.get('team_analysis', 'No team analysis available')

            summary_label = ctk.CTkLabel(
                scrollable_frame,
                text="📋 Coordinated Team Analysis:",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=self.colors['primary']
            )
            summary_label.pack(anchor="w", pady=(0, 10))

            summary_text = ctk.CTkTextbox(
                scrollable_frame,
                height=250,
                font=ctk.CTkFont(size=12),
                fg_color=self.colors['input_bg'],
                text_color=self.colors['text_primary']
            )
            summary_text.pack(fill="x", pady=(0, 20))
            summary_text.insert("1.0", str(team_analysis))
            summary_text.configure(state="disabled")

            # Individual Agent Responses
            agent_responses = results.get('agent_responses', {})

            if agent_responses:
                agents_label = ctk.CTkLabel(
                    scrollable_frame,
                    text="🤖 Individual Agent Contributions:",
                    font=ctk.CTkFont(size=16, weight="bold"),
                    text_color=self.colors['primary']
                )
                agents_label.pack(anchor="w", pady=(0, 10))

                for agent_name, response in agent_responses.items():
                    agent_frame = ctk.CTkFrame(scrollable_frame, fg_color=self.colors['input_bg'])
                    agent_frame.pack(fill="x", pady=8)

                    agent_title = ctk.CTkLabel(
                        agent_frame,
                        text=f"🔬 {agent_name.replace('_', ' ').title()}:",
                        font=ctk.CTkFont(size=14, weight="bold"),
                        text_color=self.colors['primary']
                    )
                    agent_title.pack(anchor="w", padx=15, pady=(15, 8))

                    agent_text = ctk.CTkTextbox(
                        agent_frame,
                        height=100,
                        font=ctk.CTkFont(size=11),
                        fg_color=self.colors['bg_primary'],
                        text_color=self.colors['text_primary']
                    )
                    agent_text.pack(fill="x", padx=15, pady=(0, 15))
                    agent_text.insert("1.0", str(response))
                    agent_text.configure(state="disabled")

            # Analysis Metadata
            metadata_label = ctk.CTkLabel(
                scrollable_frame,
                text="📊 Analysis Metadata:",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=self.colors['primary']
            )
            metadata_label.pack(anchor="w", pady=(20, 10))

            metadata_frame = ctk.CTkFrame(scrollable_frame, fg_color=self.colors['input_bg'])
            metadata_frame.pack(fill="x", pady=8)

            metadata_info = [
                f"Analysis Type: {results.get('analysis_type', 'Unknown')}",
                f"Timestamp: {results.get('timestamp', 'Unknown')}",
                f"Success: {'✅ Yes' if results.get('success', False) else '❌ No'}"
            ]

            for info in metadata_info:
                info_label = ctk.CTkLabel(
                    metadata_frame,
                    text=info,
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_primary']
                )
                info_label.pack(anchor="w", padx=15, pady=5)

        except Exception as e:
            error_label = ctk.CTkLabel(
                main_frame,
                text=f"❌ Error displaying Agno Teams results: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="red"
            )
            error_label.pack(pady=20)

    def display_unified_results(self, results: dict):
        """Display unified comprehensive analysis results"""
        try:
            # Clear previous results
            for widget in self.results_container.winfo_children():
                widget.destroy()

            # Create main results frame
            main_frame = ctk.CTkFrame(self.results_container)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Title
            title_label = ctk.CTkLabel(
                main_frame,
                text="🚀 Unified Comprehensive Analysis Results",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.colors['primary']
            )
            title_label.pack(pady=(10, 20))

            # Create scrollable frame for results
            scrollable_frame = ctk.CTkScrollableFrame(
                main_frame,
                height=600,
                fg_color=self.colors['card_bg']
            )
            scrollable_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            # Unified Summary
            unified_summary = results.get('unified_summary', {})
            if unified_summary:
                summary_label = ctk.CTkLabel(
                    scrollable_frame,
                    text="📋 Unified Summary:",
                    font=ctk.CTkFont(size=16, weight="bold"),
                    text_color=self.colors['primary']
                )
                summary_label.pack(anchor="w", pady=(0, 10))

                summary_frame = ctk.CTkFrame(scrollable_frame, fg_color=self.colors['input_bg'])
                summary_frame.pack(fill="x", pady=(0, 20))

                # Overall assessment
                assessment = unified_summary.get('overall_assessment', 'No assessment available')
                assessment_label = ctk.CTkLabel(
                    summary_frame,
                    text=f"Assessment: {assessment}",
                    font=ctk.CTkFont(size=12, weight="bold"),
                    text_color=self.colors['text_primary']
                )
                assessment_label.pack(anchor="w", padx=15, pady=(15, 5))

                # Risk level and confidence
                risk_level = unified_summary.get('risk_level', 'unknown')
                confidence = unified_summary.get('confidence', 0.0)

                risk_color = {
                    'low': '#10B981',
                    'medium': '#F59E0B',
                    'high': '#EF4444',
                    'critical': '#DC2626'
                }.get(risk_level, '#6B7280')

                risk_label = ctk.CTkLabel(
                    summary_frame,
                    text=f"Risk Level: {risk_level.upper()} | Confidence: {confidence:.1%}",
                    font=ctk.CTkFont(size=12),
                    text_color=risk_color
                )
                risk_label.pack(anchor="w", padx=15, pady=5)

                # Key findings
                key_findings = unified_summary.get('key_findings', [])
                if key_findings:
                    findings_label = ctk.CTkLabel(
                        summary_frame,
                        text="Key Findings:",
                        font=ctk.CTkFont(size=11, weight="bold"),
                        text_color=self.colors['text_primary']
                    )
                    findings_label.pack(anchor="w", padx=15, pady=(10, 5))

                    for finding in key_findings[:3]:  # Show top 3
                        finding_label = ctk.CTkLabel(
                            summary_frame,
                            text=f"• {finding}",
                            font=ctk.CTkFont(size=10),
                            text_color=self.colors['text_primary']
                        )
                        finding_label.pack(anchor="w", padx=25, pady=2)

                # Recommendations
                recommendations = unified_summary.get('recommendations', [])
                if recommendations:
                    rec_label = ctk.CTkLabel(
                        summary_frame,
                        text="Recommendations:",
                        font=ctk.CTkFont(size=11, weight="bold"),
                        text_color=self.colors['text_primary']
                    )
                    rec_label.pack(anchor="w", padx=15, pady=(10, 5))

                    for rec in recommendations[:2]:  # Show top 2
                        rec_item = ctk.CTkLabel(
                            summary_frame,
                            text=f"• {rec}",
                            font=ctk.CTkFont(size=10),
                            text_color=self.colors['text_primary']
                        )
                        rec_item.pack(anchor="w", padx=25, pady=(2, 15))

            # Component Results
            components = results.get('components', [])

            if 'multi_condition_analysis' in components:
                self._display_component_summary(scrollable_frame, "🔬 Multi-Condition Analysis",
                                              results.get('multi_condition_analysis', {}))

            if 'image_data_extraction' in components:
                self._display_component_summary(scrollable_frame, "🖼️ Image Data Extraction",
                                              results.get('image_data_extraction', {}))

            if 'agno_teams_analysis' in components:
                self._display_component_summary(scrollable_frame, "🤖 Agno Teams Analysis",
                                              results.get('agno_teams_analysis', {}))

            # Analysis Metadata
            metadata_label = ctk.CTkLabel(
                scrollable_frame,
                text="📊 Analysis Metadata:",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=self.colors['primary']
            )
            metadata_label.pack(anchor="w", pady=(20, 10))

            metadata_frame = ctk.CTkFrame(scrollable_frame, fg_color=self.colors['input_bg'])
            metadata_frame.pack(fill="x", pady=8)

            metadata_info = [
                f"Analysis Type: {results.get('analysis_type', 'Unknown')}",
                f"Components: {', '.join(components)}",
                f"Processing Time: {results.get('processing_time', 0):.1f}s",
                f"Success: {'✅ Yes' if results.get('success', False) else '❌ No'}",
                f"Timestamp: {results.get('timestamp', 'Unknown')}"
            ]

            for info in metadata_info:
                info_label = ctk.CTkLabel(
                    metadata_frame,
                    text=info,
                    font=ctk.CTkFont(size=11),
                    text_color=self.colors['text_primary']
                )
                info_label.pack(anchor="w", padx=15, pady=3)

        except Exception as e:
            error_label = ctk.CTkLabel(
                main_frame,
                text=f"❌ Error displaying unified results: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="red"
            )
            error_label.pack(pady=20)

    def _display_component_summary(self, parent, title: str, component_results: dict):
        """Display summary of a component's results"""
        try:
            component_label = ctk.CTkLabel(
                parent,
                text=title,
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['primary']
            )
            component_label.pack(anchor="w", pady=(15, 5))

            component_frame = ctk.CTkFrame(parent, fg_color=self.colors['input_bg'])
            component_frame.pack(fill="x", pady=5)

            if component_results.get('success', False):
                status_text = "✅ Completed successfully"

                # Add specific summary based on component type
                if 'overall_assessment' in component_results:
                    # Multi-condition analysis
                    assessment = component_results['overall_assessment']
                    most_likely = assessment.get('most_likely_condition', 'Unknown')
                    confidence = assessment.get('confidence', 0.0)
                    summary_text = f"Most likely: {most_likely} ({confidence:.1%} confidence)"
                elif 'visual_summary' in component_results:
                    # Image data extraction
                    visual_summary = component_results.get('visual_summary', '')
                    summary_text = f"Visual: {visual_summary[:80]}..." if len(visual_summary) > 80 else visual_summary
                elif 'team_analysis' in component_results:
                    # Agno Teams analysis
                    team_analysis = str(component_results.get('team_analysis', ''))
                    summary_text = f"Team: {team_analysis[:80]}..." if len(team_analysis) > 80 else team_analysis
                else:
                    summary_text = "Analysis completed"

            else:
                status_text = "❌ Failed"
                error = component_results.get('error', 'Unknown error')
                summary_text = f"Error: {error[:60]}..." if len(error) > 60 else error

            status_label = ctk.CTkLabel(
                component_frame,
                text=status_text,
                font=ctk.CTkFont(size=11, weight="bold"),
                text_color=self.colors['text_primary']
            )
            status_label.pack(anchor="w", padx=15, pady=(10, 5))

            summary_label = ctk.CTkLabel(
                component_frame,
                text=summary_text,
                font=ctk.CTkFont(size=10),
                text_color=self.colors['text_primary']
            )
            summary_label.pack(anchor="w", padx=15, pady=(0, 10))

        except Exception as e:
            error_label = ctk.CTkLabel(
                parent,
                text=f"❌ Error displaying {title}: {str(e)}",
                font=ctk.CTkFont(size=10),
                text_color="red"
            )
            error_label.pack(anchor="w", pady=5)

    def update_status(self, message: str, status_type: str = "info"):
        """Update status indicators"""
        colors = {
            "success": self.colors['success'],
            "danger": self.colors['danger'],
            "info": self.colors['primary']
        }
        
        self.progress_label.configure(text=message)
        if hasattr(self, 'system_status'):
            self.system_status.configure(
                text=message,
                fg_color=colors.get(status_type, self.colors['primary'])
            )
    
    def upload_image(self):
        """Handle image upload with modern file dialog"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="Select Dermatological Image for Analysis",
            filetypes=file_types,
            initialdir=str(Path.home() / "Pictures")
        )
        
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path: str):
        """Load and display image with professional presentation"""
        try:
            # Load image
            image = Image.open(file_path)
            self.current_image = np.array(image)
            
            # Calculate display size maintaining aspect ratio
            display_width = 600
            display_height = 400
            
            img_width, img_height = image.size
            aspect_ratio = img_width / img_height
            
            if aspect_ratio > display_width / display_height:
                new_width = display_width
                new_height = int(display_width / aspect_ratio)
            else:
                new_height = display_height
                new_width = int(display_height * aspect_ratio)
            
            # Resize image for display
            display_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(display_image)
            
            # Update image display
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # Keep reference
            
            # Update image info
            file_size = Path(file_path).stat().st_size / 1024  # KB
            info_text = f"📁 {Path(file_path).name} | 📐 {img_width}×{img_height} | 💾 {file_size:.1f} KB"
            self.image_info.configure(text=info_text)
            
            # Enable unified analysis and reset buttons
            self.analyze_btn.configure(state="normal")
            self.reset_btn.configure(state="normal")
            self.update_status("✅ Image loaded successfully", "success")
            
            logger.info(f"✅ Image loaded: {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image:\n{str(e)}")
            logger.error(f"❌ Failed to load image: {e}")
    
    def start_analysis(self):
        """Start comprehensive analysis in background thread"""
        if self.current_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return
        
        if self.processing:
            return
        
        # Start analysis in background thread
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def start_unified_analysis(self):
        """Start unified comprehensive analysis in background thread"""
        if self.current_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return

        if self.processing:
            return

        # Start unified analysis in background thread
        unified_thread = threading.Thread(target=self.run_unified_analysis)
        unified_thread.daemon = True
        unified_thread.start()



    def start_image_extraction(self):
        """Start Gemma 3n-E4B image data extraction in background thread"""
        if self.current_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return

        if self.processing:
            return

        # Start extraction in background thread
        extraction_thread = threading.Thread(target=self.run_image_extraction)
        extraction_thread.daemon = True
        extraction_thread.start()

    def start_agno_teams_analysis(self):
        """Start Agno Teams multi-agent analysis in background thread"""
        if self.current_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return

        if self.processing:
            return

        # Start Agno Teams analysis in background thread
        agno_thread = threading.Thread(target=self.run_agno_teams_analysis)
        agno_thread.daemon = True
        agno_thread.start()
    
    def run_analysis(self):
        """Run analysis with progress updates"""
        self.processing = True
        self.stop_requested = False
        self.analyze_btn.configure(state="disabled", text="🔄 Processing...")
        self.stop_btn.configure(state="normal")
        
        try:
            # Simulate analysis steps with progress updates
            steps = [
                ("🔍 Initializing analysis...", 0.1),
                ("🖼️ Processing image...", 0.2),
                ("🔬 Detecting lesions...", 0.4),
                ("🎨 ABCDE analysis...", 0.6),
                ("🤖 AI multi-condition analysis...", 0.8),
                ("📊 Generating results...", 0.9),
                ("✅ Analysis complete!", 1.0)
            ]
            
            for step_text, progress in steps:
                # Check if stop was requested
                if self.stop_requested:
                    print("⏹️ DEBUG: Analysis stopped by user during progress")
                    return

                self.root.after(0, lambda t=step_text, p=progress: self.update_progress(t, p))
                time.sleep(1)  # Simulate processing time
            
            # Run actual analysis if engine is available
            if self.analysis_engine:
                # Get analysis mode and condition
                analysis_mode = self.analysis_mode.get()
                selected_condition = self.selected_condition.get() if analysis_mode == "single" else None

                analysis_options = {
                    'enable_abcde_analysis': True,  # Always enable ABCDE
                    'enable_ai_analysis': True,     # Always enable AI/LLM analysis
                    'enable_multi_condition': analysis_mode == "multi",  # Based on selection
                    'enable_single_condition': analysis_mode == "single",  # NEW: Single condition mode
                    'target_condition': selected_condition,  # NEW: Specific condition
                    'enable_temporal_analysis': False,
                    'confidence_threshold': 0.3,
                    'max_lesions_per_image': 10,
                    'risk_threshold_high': 0.7,
                    'risk_threshold_medium': 0.4,
                    'force_llm_analysis': True,    # Force LLM usage
                    'llm_detailed_analysis': True  # Enable detailed LLM analysis
                }
                
                patient_context = {
                    'age': 45,  # Default values
                    'gender': 'unknown',
                    'skin_type': 'unknown'
                }
                
                # Check if stop was requested before starting analysis
                if self.stop_requested:
                    print("⏹️ DEBUG: Analysis stopped by user before engine call")
                    return

                print(f"\n🔍 DEBUG: Starting analysis with engine...")
                print(f"   - Image shape: {self.current_image.shape if hasattr(self.current_image, 'shape') else 'No shape'}")
                print(f"   - Analysis options: {analysis_options}")
                print(f"   - Analysis Mode: {analysis_mode}")
                print(f"   - Target Condition: {selected_condition if analysis_mode == 'single' else 'All conditions'}")
                print(f"   - Multi-condition: {analysis_options.get('enable_multi_condition', False)}")
                print(f"   - Single-condition: {analysis_options.get('enable_single_condition', False)}")
                print(f"   - LLM Analysis ENABLED: {analysis_options.get('enable_ai_analysis', False)}")
                print(f"   - Force LLM: {analysis_options.get('force_llm_analysis', False)}")
                print(f"   - Detailed LLM: {analysis_options.get('llm_detailed_analysis', False)}")

                # Check if LLM is available
                if hasattr(self.analysis_engine, 'gemma_handler'):
                    llm_available = self.analysis_engine.gemma_handler.initialized
                    print(f"   - LLM Handler Available: {llm_available}")
                    if llm_available:
                        print(f"   - LLM Model: {self.analysis_engine.gemma_handler.model_name}")
                        print(f"   - LLM URL: {self.analysis_engine.gemma_handler.ollama_url}")
                else:
                    print(f"   - LLM Handler: NOT FOUND")

                results = self.analysis_engine.analyze_comprehensive(
                    self.current_image,
                    patient_context=patient_context,
                    analysis_options=analysis_options
                )

                print(f"🔍 DEBUG: Analysis completed, results received:")
                print(f"   - Results type: {type(results)}")
                print(f"   - Results keys: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")
                print(f"   - Success: {results.get('success', 'N/A') if isinstance(results, dict) else 'N/A'}")

                self.analysis_results = results
                print(f"🔍 DEBUG: Results stored in self.analysis_results, calling display_results...")
                self.root.after(0, self.display_results)
            else:
                # Show demo results
                self.root.after(0, self.display_demo_results)
            
        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"❌ Analysis failed: {str(e)}", "danger"))
            logger.error(f"❌ Analysis failed: {e}")
        
        finally:
            self.processing = False
            self.stop_requested = False
            self.root.after(0, lambda: self.analyze_btn.configure(
                state="normal",
                text="🚀 Start Comprehensive Analysis"
            ))
            self.root.after(0, lambda: self.reset_btn.configure(state="normal"))
            self.root.after(0, lambda: self.stop_btn.configure(state="disabled"))

    def run_image_extraction(self):
        """Run Gemma 3n-E4B image data extraction with progress updates"""
        self.processing = True
        self.stop_requested = False
        self.extract_btn.configure(state="disabled", text="🔄 Extracting Data...")
        self.stop_btn.configure(state="normal")

        try:
            # Update progress
            self.root.after(0, lambda: self.update_progress("🎯 Initializing Gemma 3n-E4B extraction...", 0.1))

            if not self.analysis_engine or not hasattr(self.analysis_engine, 'gemma_handler'):
                raise Exception("Analysis engine or Gemma handler not available")

            # Check if stop was requested
            if self.stop_requested:
                print("⏹️ DEBUG: Extraction stopped by user before starting")
                return

            # Get extraction settings
            extraction_mode = self.selected_extraction_mode.get()
            enable_extraction = self.enable_extraction.get()

            if not enable_extraction:
                self.root.after(0, lambda: self.update_status("⚠️ Image extraction is disabled", "warning"))
                return

            print(f"\n🎯 DEBUG: Starting Gemma 3n-E4B Image Data Extraction...")
            print(f"   - Extraction mode: {extraction_mode}")
            print(f"   - Image shape: {self.current_image.shape if hasattr(self.current_image, 'shape') else 'No shape'}")

            # Update progress
            self.root.after(0, lambda: self.update_progress("🖼️ Preprocessing image for Gemma 3n-E4B...", 0.3))
            time.sleep(1)

            # Check if stop was requested
            if self.stop_requested:
                print("⏹️ DEBUG: Extraction stopped by user during preprocessing")
                return

            # Update progress
            self.root.after(0, lambda: self.update_progress("🤖 Gemma 3n-E4B analyzing image data...", 0.6))

            # Perform image data extraction
            extraction_results = self.analysis_engine.gemma_handler.extract_image_data(
                self.current_image,
                extraction_mode=extraction_mode,
                focus_areas=['colors', 'textures', 'shapes', 'patterns', 'medical_features']
            )

            # Check if stop was requested
            if self.stop_requested:
                print("⏹️ DEBUG: Extraction stopped by user after analysis")
                return

            # Update progress
            self.root.after(0, lambda: self.update_progress("📊 Processing extraction results...", 0.9))
            time.sleep(0.5)

            # Display results
            self.root.after(0, lambda: self.display_extraction_results(extraction_results))

            # Update progress
            self.root.after(0, lambda: self.update_progress("✅ Image data extraction complete!", 1.0))
            self.root.after(0, lambda: self.update_status("✅ Gemma 3n-E4B extraction completed successfully", "success"))

            print(f"✅ DEBUG: Image data extraction completed successfully")
            print(f"   - Processing time: {extraction_results.get('processing_time', 0):.2f}s")
            print(f"   - Features extracted: {len(extraction_results.get('key_features', []))}")

        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"❌ Extraction failed: {str(e)}", "danger"))
            logger.error(f"❌ Image extraction failed: {e}")
            print(f"❌ DEBUG: Extraction failed: {e}")

        finally:
            self.processing = False
            self.stop_requested = False
            self.root.after(0, lambda: self.extract_btn.configure(
                state="normal",
                text="🎯 Extract Image Data (Gemma 3n-E4B)"
            ))
            self.root.after(0, lambda: self.stop_btn.configure(state="disabled"))

    def run_unified_analysis(self):
        """Run unified comprehensive analysis combining all capabilities"""
        try:
            self.processing = True
            self.root.after(0, lambda: self.analyze_btn.configure(
                state="disabled",
                text="🚀 Running Unified Analysis..."
            ))

            # Update status
            self.root.after(0, lambda: self.update_status("🚀 Starting Unified Comprehensive Analysis..."))

            # Initialize analysis engine
            try:
                if self.analysis_engine is None:
                    self.root.after(0, lambda: self.update_status("🔧 Initializing analysis engine..."))
                    from core.multi_condition_engine import MultiConditionAnalysisEngine
                    self.analysis_engine = MultiConditionAnalysisEngine()

                    if not self.analysis_engine.initialize():
                        raise RuntimeError("Failed to initialize analysis engine")

                self.root.after(0, lambda: self.update_status("✅ Analysis engine ready"))

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ Engine initialization failed: {str(e)}"))
                messagebox.showerror("Error", f"Analysis engine initialization failed:\n{str(e)}")
                return

            # Prepare patient context with improved disease selection
            analysis_mode = self.analysis_mode.get()

            # Determine which diseases to analyze based on new interface
            if analysis_mode == "multi":
                target_diseases = None  # Analyze all diseases
                disease_info = "All 14 dermatological conditions"
                self.root.after(0, lambda: self.update_status("🔬 Comprehensive Analysis: All diseases"))
            else:
                # Get selected specific disease from new interface
                selected_disease = self.selected_disease.get()
                target_diseases = [selected_disease]

                # Get disease display name for user feedback
                disease_display_name = "Unknown Disease"
                for category, diseases in self.disease_categories.items():
                    for disease_key, disease_name in diseases:
                        if disease_key == selected_disease:
                            disease_display_name = disease_name
                            break

                disease_info = f"Targeted analysis: {disease_display_name}"
                self.root.after(0, lambda: self.update_status(f"🎯 Targeted Analysis: {disease_display_name}"))

            patient_context = {
                'extraction_mode': self.selected_extraction_mode.get(),
                'focus_areas': [area for area, var in self.focus_areas.items() if var.get()],
                'agno_mode': self.selected_agno_mode.get() if hasattr(self, 'selected_agno_mode') else 'coordinate',
                'target_diseases': target_diseases,
                'disease_analysis_mode': analysis_mode,
                'timestamp': datetime.now().isoformat()
            }

            # Check if Agno Teams is enabled
            enable_agno_teams = self.enable_agno_teams.get() if hasattr(self, 'enable_agno_teams') else False
            agno_mode = patient_context['agno_mode']

            # Run unified analysis
            self.root.after(0, lambda: self.update_status("🔬 Running unified comprehensive analysis..."))
            self.root.after(0, lambda: self.update_status(f"   - Disease analysis: {disease_info}"))
            self.root.after(0, lambda: self.update_status("   - Image data extraction"))
            if enable_agno_teams:
                self.root.after(0, lambda: self.update_status(f"   - Agno Teams ({agno_mode} mode)"))

            # Use the unified analysis method from the handler
            handler = self.analysis_engine.gemma_handler
            results = handler.analyze_comprehensive_unified(
                self.current_image,
                patient_context=patient_context,
                enable_agno_teams=enable_agno_teams,
                agno_mode=agno_mode
            )

            if results.get('success', False):
                self.root.after(0, lambda: self.update_status("✅ Unified analysis completed successfully!"))

                # Display unified results
                self.root.after(0, lambda: self.display_unified_results(results))

            else:
                error_msg = results.get('error', 'Unknown error')
                self.root.after(0, lambda: self.update_status(f"❌ Unified analysis failed: {error_msg}"))
                messagebox.showerror("Analysis Error", f"Unified analysis failed:\n{error_msg}")

        except Exception as e:
            error_msg = f"Unified analysis error: {str(e)}"
            self.root.after(0, lambda: self.update_status(f"❌ {error_msg}"))
            messagebox.showerror("Error", error_msg)

        finally:
            self.processing = False
            self.root.after(0, lambda: self.analyze_btn.configure(
                state="normal",
                text="🚀 Start Unified Analysis (All-in-One)"
            ))

    def run_agno_teams_analysis(self):
        """Run Agno Teams multi-agent analysis"""
        try:
            self.processing = True
            self.root.after(0, lambda: self.agno_teams_btn.configure(
                state="disabled",
                text="🤖 Running Multi-Agent Analysis..."
            ))

            # Update status
            self.root.after(0, lambda: self.update_status("🤖 Initializing Agno Teams multi-agent system..."))

            # Initialize Agno Teams
            try:
                from core.agno_derma_team import AgnoDermaTeam
                agno_team = AgnoDermaTeam()

                self.root.after(0, lambda: self.update_status("✅ Agno Teams initialized - Starting autonomous analysis..."))

            except ImportError:
                self.root.after(0, lambda: self.update_status("❌ Agno not available. Install with: pip install agno"))
                messagebox.showerror("Error", "Agno Teams not available.\nInstall with: pip install agno")
                return
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ Agno Teams initialization failed: {str(e)}"))
                messagebox.showerror("Error", f"Agno Teams initialization failed:\n{str(e)}")
                return

            # Convert image to base64
            self.root.after(0, lambda: self.update_status("🖼️ Processing image for multi-agent analysis..."))

            from PIL import Image
            import io
            import base64

            # Convert numpy array to PIL Image
            pil_image = Image.fromarray(self.current_image)

            # Convert to base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Prepare patient context
            patient_context = {
                "analysis_mode": self.selected_agno_mode.get(),
                "extraction_mode": self.selected_extraction_mode.get(),
                "focus_areas": [area for area, var in self.focus_areas.items() if var.get()],
                "timestamp": datetime.now().isoformat()
            }

            # Run multi-agent analysis
            self.root.after(0, lambda: self.update_status("🤖 Running coordinated multi-agent analysis..."))

            results = agno_team.analyze_lesion(image_base64, patient_context)

            if results.get('success', False):
                self.root.after(0, lambda: self.update_status("✅ Multi-agent analysis completed successfully!"))

                # Display results
                self.root.after(0, lambda: self.display_agno_teams_results(results))

            else:
                error_msg = results.get('error', 'Unknown error')
                self.root.after(0, lambda: self.update_status(f"❌ Multi-agent analysis failed: {error_msg}"))
                messagebox.showerror("Analysis Error", f"Multi-agent analysis failed:\n{error_msg}")

        except Exception as e:
            error_msg = f"Agno Teams analysis error: {str(e)}"
            self.root.after(0, lambda: self.update_status(f"❌ {error_msg}"))
            messagebox.showerror("Error", error_msg)

        finally:
            self.processing = False
            self.root.after(0, lambda: self.agno_teams_btn.configure(
                state="normal",
                text="🤖 Agno Teams Multi-Agent Analysis"
            ))

    def display_extraction_results(self, extraction_results: Dict):
        """Display Gemma 3n-E4B image data extraction results"""
        try:
            # Clear previous results
            for widget in self.results_container.winfo_children():
                widget.destroy()

            # Create main results frame
            main_frame = ctk.CTkFrame(self.results_container)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Title
            title_label = ctk.CTkLabel(
                main_frame,
                text="🎯 Gemma 3n-E4B Image Data Extraction Results",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.colors['primary']
            )
            title_label.pack(pady=(10, 20))

            # Create scrollable frame for results
            scrollable_frame = ctk.CTkScrollableFrame(main_frame, height=400)
            scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Extraction info
            info_frame = ctk.CTkFrame(scrollable_frame)
            info_frame.pack(fill="x", pady=(0, 10))

            info_text = f"""🔍 Extraction Mode: {extraction_results.get('extraction_mode', 'Unknown')}
⏱️ Processing Time: {extraction_results.get('processing_time', 0):.2f}s
🤖 Model Used: {extraction_results.get('model_used', 'gemma3n:e4b')}
📊 Quality: {extraction_results.get('extraction_quality', 'Unknown')}
🎯 Confidence: {extraction_results.get('confidence_score', 0):.1%}"""

            info_label = ctk.CTkLabel(
                info_frame,
                text=info_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            info_label.pack(padx=15, pady=10)

            # Visual Summary
            if extraction_results.get('visual_summary'):
                summary_frame = ctk.CTkFrame(scrollable_frame)
                summary_frame.pack(fill="x", pady=(0, 10))

                summary_title = ctk.CTkLabel(
                    summary_frame,
                    text="📋 Visual Summary",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['primary']
                )
                summary_title.pack(pady=(10, 5))

                summary_text = ctk.CTkTextbox(
                    summary_frame,
                    height=80,
                    font=ctk.CTkFont(size=11)
                )
                summary_text.pack(fill="x", padx=10, pady=(0, 10))
                summary_text.insert("1.0", extraction_results['visual_summary'])
                summary_text.configure(state="disabled")

            # Key Features
            if extraction_results.get('key_features'):
                features_frame = ctk.CTkFrame(scrollable_frame)
                features_frame.pack(fill="x", pady=(0, 10))

                features_title = ctk.CTkLabel(
                    features_frame,
                    text="🔑 Key Features Extracted",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['success']
                )
                features_title.pack(pady=(10, 5))

                features_text = "\n".join([f"• {feature}" for feature in extraction_results['key_features'][:10]])
                features_label = ctk.CTkLabel(
                    features_frame,
                    text=features_text,
                    font=ctk.CTkFont(size=11),
                    justify="left"
                )
                features_label.pack(padx=15, pady=(0, 10))

            # Color Analysis
            color_analysis = extraction_results.get('color_analysis', {})
            if color_analysis:
                color_frame = ctk.CTkFrame(scrollable_frame)
                color_frame.pack(fill="x", pady=(0, 10))

                color_title = ctk.CTkLabel(
                    color_frame,
                    text="🎨 Color Analysis",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['warning']
                )
                color_title.pack(pady=(10, 5))

                color_info = f"""Dominant Colors: {', '.join(color_analysis.get('dominant_colors', ['Unknown']))}
Color Variation: {color_analysis.get('color_variation', 'Not analyzed')}
Medical Significance: {color_analysis.get('medical_significance', 'Not available')}"""

                color_label = ctk.CTkLabel(
                    color_frame,
                    text=color_info,
                    font=ctk.CTkFont(size=11),
                    justify="left"
                )
                color_label.pack(padx=15, pady=(0, 10))

            # Medical Observations
            if extraction_results.get('medical_observations'):
                medical_frame = ctk.CTkFrame(scrollable_frame)
                medical_frame.pack(fill="x", pady=(0, 10))

                medical_title = ctk.CTkLabel(
                    medical_frame,
                    text="🏥 Medical Observations",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['danger']
                )
                medical_title.pack(pady=(10, 5))

                medical_text = "\n".join([f"• {obs}" for obs in extraction_results['medical_observations'][:8]])
                medical_label = ctk.CTkLabel(
                    medical_frame,
                    text=medical_text,
                    font=ctk.CTkFont(size=11),
                    justify="left"
                )
                medical_label.pack(padx=15, pady=(0, 10))

            # Clinical Relevance
            if extraction_results.get('clinical_relevance'):
                clinical_frame = ctk.CTkFrame(scrollable_frame)
                clinical_frame.pack(fill="x", pady=(0, 10))

                clinical_title = ctk.CTkLabel(
                    clinical_frame,
                    text="⚕️ Clinical Relevance",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['primary']
                )
                clinical_title.pack(pady=(10, 5))

                clinical_text = ctk.CTkTextbox(
                    clinical_frame,
                    height=60,
                    font=ctk.CTkFont(size=11)
                )
                clinical_text.pack(fill="x", padx=10, pady=(0, 10))
                clinical_text.insert("1.0", extraction_results['clinical_relevance'])
                clinical_text.configure(state="disabled")

            # Text Description for Communication
            if extraction_results.get('text_description'):
                text_frame = ctk.CTkFrame(scrollable_frame)
                text_frame.pack(fill="x", pady=(0, 10))

                text_title = ctk.CTkLabel(
                    text_frame,
                    text="💬 Text Description for Communication",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.colors['secondary']
                )
                text_title.pack(pady=(10, 5))

                text_description = ctk.CTkTextbox(
                    text_frame,
                    height=80,
                    font=ctk.CTkFont(size=11)
                )
                text_description.pack(fill="x", padx=10, pady=(0, 10))
                text_description.insert("1.0", extraction_results['text_description'])
                text_description.configure(state="disabled")

            # Enable reset button
            self.reset_btn.configure(state="normal")

        except Exception as e:
            logger.error(f"❌ Failed to display extraction results: {e}")
            error_label = ctk.CTkLabel(
                self.results_container,
                text=f"❌ Failed to display results: {str(e)}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['danger']
            )
            error_label.pack(pady=20)
    
    def reset_analysis(self):
        """Reset the analysis interface and clear all results"""
        try:
            print("\n🔄 RESET: Clearing analysis interface...")

            # Clear current image
            self.current_image = None

            # Clear analysis results
            self.analysis_results = None

            # Reset image display
            if hasattr(self, 'image_label'):
                self.image_label.configure(image=None, text="📷 No image selected\n\nClick 'Load Image' to begin analysis")

            # Clear image info
            if hasattr(self, 'image_info'):
                self.image_info.configure(text="No image loaded")

            # Clear results panel
            if hasattr(self, 'results_container'):
                for widget in self.results_container.winfo_children():
                    widget.destroy()

                # Add placeholder message
                placeholder_label = ctk.CTkLabel(
                    self.results_container,
                    text="📊 Analysis results will appear here after processing an image.\n\nLoad an image and start analysis to see detailed results.",
                    font=ctk.CTkFont(size=14),
                    text_color=self.colors['text_secondary'],
                    justify="center"
                )
                placeholder_label.pack(pady=50)

            # Reset progress bar
            if hasattr(self, 'progress_bar'):
                self.progress_bar.set(0)

            # Reset progress label
            if hasattr(self, 'progress_label'):
                self.progress_label.configure(text="Ready for analysis")

            # Reset button states
            self.analyze_btn.configure(state="disabled", text="🚀 Start Unified Analysis (All-in-One)")
            self.reset_btn.configure(state="disabled")

            # Reset processing flag
            self.processing = False

            # Update status
            self.update_status("🔄 Interface reset - ready for new analysis", "info")

            print("✅ RESET: Interface cleared successfully")
            logger.info("🔄 Analysis interface reset")

        except Exception as e:
            print(f"❌ RESET: Reset failed: {e}")
            logger.error(f"❌ Reset failed: {e}")
            self.update_status("❌ Reset failed", "danger")

    def clear_all_screens(self):
        """Clear all screens and reset interface to initial state"""
        try:
            print("\n🧹 CLEAR: Clearing all screens and resetting interface...")

            # Clear current image
            self.current_image = None

            # Clear analysis results
            self.analysis_results = None

            # Reset image display
            if hasattr(self, 'image_label'):
                self.image_label.configure(
                    image=None,
                    text="📷 No image selected\n\nClick 'Load Image' to begin analysis"
                )

            # Clear image info
            if hasattr(self, 'image_info'):
                self.image_info.configure(text="No image loaded")

            # Clear results panel
            if hasattr(self, 'results_container'):
                for widget in self.results_container.winfo_children():
                    widget.destroy()

                # Add placeholder message
                placeholder_label = ctk.CTkLabel(
                    self.results_container,
                    text="📊 Analysis results will appear here after processing an image.\n\nLoad an image and start analysis to see detailed results.",
                    font=ctk.CTkFont(size=14),
                    text_color=self.colors['text_secondary'],
                    justify="center"
                )
                placeholder_label.pack(pady=50)

            # Reset progress bar
            if hasattr(self, 'progress_bar'):
                self.progress_bar.set(0)

            # Reset progress label
            if hasattr(self, 'progress_label'):
                self.progress_label.configure(text="Ready for analysis")

            # Reset button states
            self.analyze_btn.configure(state="disabled", text="🚀 Start Unified Analysis (All-in-One)")
            self.reset_btn.configure(state="disabled")
            self.stop_btn.configure(state="disabled")

            # Reset analysis mode to default
            self.analysis_mode.set("multi")
            self.condition_dropdown.configure(state="disabled")
            self.selected_condition.set(self.condition_options[0])

            # Reset processing flag
            self.processing = False

            # Update status
            self.update_status("🧹 All screens cleared - ready for new analysis", "success")

            print("✅ CLEAR: All screens cleared successfully")
            logger.info("🧹 All screens cleared and interface reset")

        except Exception as e:
            print(f"❌ CLEAR: Clear screens failed: {e}")
            logger.error(f"❌ Clear screens failed: {e}")
            self.update_status("❌ Clear screens failed", "danger")

    def stop_analysis(self):
        """Stop current analysis process"""
        try:
            print("\n⏹️ STOP: Stopping current analysis...")

            if not self.processing:
                print("⚠️ STOP: No analysis currently running")
                self.update_status("⚠️ No analysis currently running", "warning")
                return

            # Set stop flag
            self.processing = False
            self.stop_requested = True

            # Reset button states
            self.analyze_btn.configure(state="normal", text="🚀 Start Unified Analysis (All-in-One)")
            self.stop_btn.configure(state="disabled")

            # Reset progress
            self.progress_bar.set(0)
            self.progress_label.configure(text="Analysis stopped by user")

            # Update status
            self.update_status("⏹️ Analysis stopped by user", "warning")

            print("✅ STOP: Analysis stopped successfully")
            logger.info("⏹️ Analysis stopped by user request")

        except Exception as e:
            print(f"❌ STOP: Stop analysis failed: {e}")
            logger.error(f"❌ Stop analysis failed: {e}")
            self.update_status("❌ Stop analysis failed", "danger")

    def on_analysis_mode_change(self):
        """Handle analysis mode change with improved UX"""
        try:
            mode = self.analysis_mode.get()

            if mode == "single":
                # Show disease selection frame with smooth transition
                self.disease_selection_frame.pack(fill="x", pady=(10, 0))
                print(f"🎯 DEBUG: Targeted analysis mode selected")
                logger.info("🎯 Switched to targeted analysis mode")
            else:
                # Hide disease selection frame
                self.disease_selection_frame.pack_forget()
                print(f"🔬 DEBUG: Comprehensive analysis mode selected")
                logger.info("🔬 Switched to comprehensive analysis mode")

        except Exception as e:
            logger.error(f"❌ Analysis mode change failed: {e}")
            print(f"❌ Error changing analysis mode: {e}")

    def update_progress(self, text: str, progress: float):
        """Update progress bar and text"""
        self.progress_label.configure(text=text)
        self.progress_bar.set(progress)
    
    def display_results(self):
        """Display comprehensive analysis results"""
        # Clear previous results
        for widget in self.results_container.winfo_children():
            widget.destroy()

        # Debug: Print analysis results to console
        print(f"\n🔍 DEBUG: Analysis results received in UI:")
        print(f"   - Success: {self.analysis_results.get('success', 'N/A') if self.analysis_results else 'No results'}")
        print(f"   - Keys: {list(self.analysis_results.keys()) if self.analysis_results else 'No keys'}")
        if self.analysis_results:
            print(f"   - Processing time: {self.analysis_results.get('processing_time', 'N/A')}")
            print(f"   - No lesions detected: {self.analysis_results.get('no_lesions_detected', 'N/A')}")
        print(f"🔍 DEBUG: Displaying results in GUI now...\n")
        
        if not self.analysis_results:
            # Show placeholder message
            placeholder_label = ctk.CTkLabel(
                self.results_container,
                text="No analysis results available. Please run an analysis first.",
                font=ctk.CTkFont(size=14),
                text_color=self.colors['text_secondary']
            )
            placeholder_label.pack(pady=50)
            return

        # Check if analysis failed
        if not self.analysis_results.get('success', False):
            error_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['error'])
            error_frame.pack(fill="x", pady=10, padx=15)

            error_label = ctk.CTkLabel(
                error_frame,
                text=f"❌ Analysis Failed: {self.analysis_results.get('error', 'Unknown error')}",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white",
                wraplength=400
            )
            error_label.pack(padx=15, pady=10)
            return

        # Results header
        header = ctk.CTkLabel(
            self.results_container,
            text="🎯 Comprehensive Analysis Results",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['text_primary']
        )
        header.pack(pady=(0, 20))

        # Check if no lesions detected
        if self.analysis_results.get('no_lesions_detected', False):
            success_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['success'])
            success_frame.pack(fill="x", pady=10, padx=15)

            success_label = ctk.CTkLabel(
                success_frame,
                text="🟢 NO SKIN LESIONS DETECTED\nYour skin appears healthy in the analyzed image.",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white",
                wraplength=400
            )
            success_label.pack(padx=15, pady=10)

            # Show recommendations for healthy skin
            self.create_clinical_recommendations()
            return

        # Display results sections with correct data structure
        self.create_detection_summary()
        self.create_risk_assessment()
        self.create_conditions_detected()
        self.create_clinical_recommendations()
        self.create_confidence_metrics()
    
    def display_demo_results(self):
        """Display demo results when engine is not available"""
        # Clear previous results
        for widget in self.results_container.winfo_children():
            widget.destroy()
        
        # Demo results
        demo_label = ctk.CTkLabel(
            self.results_container,
            text="🎯 Demo Analysis Results\n\n✅ 3 lesions detected\n🎨 ABCDE scores calculated\n🤖 AI analysis completed\n⚠️ Low risk assessment\n📋 4 recommendations generated",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_primary'],
            justify="left"
        )
        demo_label.pack(pady=20)
    
    def create_results_section(self, title: str, data: Any):
        """Create a results section with modern styling"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)
        
        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))
        
        # Section content
        if isinstance(data, dict) and data:
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    content_text = f"{key}: {value:.2f}" if isinstance(value, float) else f"{key}: {value}"
                else:
                    content_text = f"{key}: {str(value)[:50]}..."
                
                content_label = ctk.CTkLabel(
                    section_frame,
                    text=content_text,
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        
        elif isinstance(data, list) and data:
            for i, item in enumerate(data[:3]):  # Show first 3 items
                if isinstance(item, dict):
                    content_text = f"Item {i+1}: {len(item)} properties"
                else:
                    content_text = f"Item {i+1}: {str(item)[:50]}..."
                
                content_label = ctk.CTkLabel(
                    section_frame,
                    text=content_text,
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        
        else:
            no_data_label = ctk.CTkLabel(
                section_frame,
                text="No data available",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            no_data_label.pack(anchor="w", padx=25, pady=(5, 10))

    def create_detection_summary(self):
        """Create detection summary section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🔍 DETECTION SUMMARY",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get detection data
        metadata = self.analysis_results.get('analysis_metadata', {})
        total_lesions = metadata.get('total_lesions_detected', 0)
        analyzed_lesions = metadata.get('lesions_analyzed', 0)
        processing_time = self.analysis_results.get('processing_time', 0.0)

        # Display detection info
        info_items = [
            f"Total lesions detected: {total_lesions}",
            f"Lesions analyzed: {analyzed_lesions}",
            f"Processing time: {processing_time:.2f} seconds"
        ]

        for item in info_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

    def create_risk_assessment(self):
        """Create risk assessment section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🎯 RISK ASSESSMENT",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get risk data
        overall_assessment = self.analysis_results.get('overall_assessment', {})
        risk_assessment = self.analysis_results.get('risk_assessment', {})

        overall_risk = overall_assessment.get('overall_risk_level', 'unknown').upper()
        clinical_urgency = risk_assessment.get('clinical_urgency', 'routine').upper()
        follow_up = risk_assessment.get('follow_up_recommended', False)

        # Risk level with color coding
        risk_color = self.colors['success'] if overall_risk == 'LOW' else \
                    self.colors['warning'] if overall_risk == 'MODERATE' else \
                    self.colors['danger'] if overall_risk == 'HIGH' else \
                    self.colors['text_secondary']

        risk_label = ctk.CTkLabel(
            section_frame,
            text=f"Overall Risk Level: {overall_risk}",
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=risk_color
        )
        risk_label.pack(anchor="w", padx=25, pady=5)

        # Additional risk info
        risk_items = [
            f"Clinical Urgency: {clinical_urgency}",
            f"Follow-up Required: {'YES' if follow_up else 'NO'}"
        ]

        for item in risk_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

    def create_conditions_detected(self):
        """Create conditions detected section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🔬 CONDITIONS DETECTED",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get conditions data
        overall_assessment = self.analysis_results.get('overall_assessment', {})
        condition_detections = overall_assessment.get('condition_detections', {})

        if condition_detections:
            # Sort conditions by count/probability
            sorted_conditions = sorted(condition_detections.items(),
                                     key=lambda x: x[1] if isinstance(x[1], (int, float)) else 0,
                                     reverse=True)

            conditions_shown = 0
            for condition, count in sorted_conditions[:5]:
                if isinstance(count, (int, float)) and count > 0:
                    condition_name = condition.replace('_', ' ').title()
                    content_label = ctk.CTkLabel(
                        section_frame,
                        text=f"• {condition_name}: {count}",
                        font=ctk.CTkFont(size=12),
                        text_color=self.colors['text_secondary']
                    )
                    content_label.pack(anchor="w", padx=25, pady=2)
                    conditions_shown += 1

            if conditions_shown == 0:
                no_conditions_label = ctk.CTkLabel(
                    section_frame,
                    text="• No specific conditions detected with high confidence",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                no_conditions_label.pack(anchor="w", padx=25, pady=2)
        else:
            # Try to get conditions from lesion analyses
            lesion_analyses = self.analysis_results.get('lesion_analyses', [])
            conditions_found = {}

            for analysis in lesion_analyses:
                if isinstance(analysis, dict) and analysis.get('success', False):
                    condition_probs = analysis.get('condition_probabilities', {})
                    for condition_id, prob_data in condition_probs.items():
                        if isinstance(prob_data, dict):
                            prob = prob_data.get('probability', 0.0)
                            if prob > 0.3:  # Show conditions with >30% probability
                                conditions_found[condition_id] = conditions_found.get(condition_id, 0) + 1

            if conditions_found:
                sorted_found = sorted(conditions_found.items(), key=lambda x: x[1], reverse=True)
                for condition, count in sorted_found[:5]:
                    condition_name = condition.replace('_', ' ').title()
                    content_label = ctk.CTkLabel(
                        section_frame,
                        text=f"• {condition_name}: {count} lesion(s)",
                        font=ctk.CTkFont(size=12),
                        text_color=self.colors['text_secondary']
                    )
                    content_label.pack(anchor="w", padx=25, pady=2)
            else:
                analysis_label = ctk.CTkLabel(
                    section_frame,
                    text="• Analysis completed - see recommendations below",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                analysis_label.pack(anchor="w", padx=25, pady=2)

    def create_clinical_recommendations(self):
        """Create clinical recommendations section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="📋 CLINICAL RECOMMENDATIONS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get recommendations
        clinical_recommendations = self.analysis_results.get('clinical_recommendations', [])

        if clinical_recommendations:
            for i, rec in enumerate(clinical_recommendations[:6], 1):
                # Handle both string and dict recommendations
                if isinstance(rec, dict):
                    clean_rec = rec.get('recommendation', rec.get('text', str(rec)))
                else:
                    clean_rec = str(rec).replace('🔴', '').replace('🟠', '').replace('🟡', '').replace('🟢', '').strip()

                content_label = ctk.CTkLabel(
                    section_frame,
                    text=f"{i}. {clean_rec}",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary'],
                    wraplength=400
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        else:
            no_rec_label = ctk.CTkLabel(
                section_frame,
                text="• No specific recommendations available",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            no_rec_label.pack(anchor="w", padx=25, pady=2)

    def create_confidence_metrics(self):
        """Create confidence metrics section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="📊 ANALYSIS CONFIDENCE",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get confidence data
        confidence_metrics = self.analysis_results.get('confidence_metrics', {})
        overall_confidence = confidence_metrics.get('overall_confidence', 0.0)
        analysis_quality = confidence_metrics.get('analysis_quality', 'unknown').upper()

        # Display confidence info
        confidence_items = [
            f"Overall Confidence: {overall_confidence:.1%}",
            f"Analysis Quality: {analysis_quality}"
        ]

        for item in confidence_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

        # Add disclaimer
        disclaimer_frame = ctk.CTkFrame(section_frame, fg_color=self.colors['warning'], corner_radius=8)
        disclaimer_frame.pack(fill="x", padx=15, pady=(10, 15))

        disclaimer_label = ctk.CTkLabel(
            disclaimer_frame,
            text="⚠️ IMPORTANT: This is an AI-assisted analysis, not a medical diagnosis.\nAlways consult a qualified dermatologist for professional evaluation.",
            font=ctk.CTkFont(size=11),
            text_color="black",
            wraplength=400
        )
        disclaimer_label.pack(padx=10, pady=8)

    def run(self):
        """Start the modern UI application"""
        logger.info("🚀 Starting Modern DermatoGemma UI")
        self.root.mainloop()

def main():
    """Main function to run the modern UI"""
    try:
        app = ModernDermatoGemmaUI()
        app.run()
    except Exception as e:
        logger.error(f"❌ Failed to start UI: {e}")
        messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")

if __name__ == "__main__":
    main()
