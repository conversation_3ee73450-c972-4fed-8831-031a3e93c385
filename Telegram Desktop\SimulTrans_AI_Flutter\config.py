"""
Configurações do CrisisComm
"""

import os
from typing import Dict, List

class CrisisCommConfig:
    """Configurações centralizadas do aplicativo"""
    
    # Configurações do modelo
    MODEL_NAME = "google/gemma-3n-e2b-it"
    MAX_NEW_TOKENS = 500
    TEMPERATURE = 0.1
    
    # Configurações de cache
    CACHE_SIZE_MB = 100
    CACHE_TTL_HOURS = 24
    
    # Configurações de áudio
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_MAX_DURATION = 300  # 5 minutos
    
    # Configurações de imagem
    IMAGE_MAX_SIZE = (1024, 1024)
    SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg', 'webp']
    
    # Configurações de vídeo
    VIDEO_MAX_DURATION = 60  # 1 minuto
    SUPPORTED_VIDEO_FORMATS = ['mp4', 'avi', 'mov']
    
    # Idiomas suportados com códigos ISO
    SUPPORTED_LANGUAGES = {
        "en": {"name": "English", "native": "English", "rtl": False},
        "es": {"name": "Spanish", "native": "Español", "rtl": False},
        "fr": {"name": "French", "native": "Français", "rtl": False},
        "de": {"name": "German", "native": "Deutsch", "rtl": False},
        "pt": {"name": "Portuguese", "native": "Português", "rtl": False},
        "ar": {"name": "Arabic", "native": "العربية", "rtl": True},
        "zh": {"name": "Chinese", "native": "中文", "rtl": False},
        "ja": {"name": "Japanese", "native": "日本語", "rtl": False},
        "ko": {"name": "Korean", "native": "한국어", "rtl": False},
        "hi": {"name": "Hindi", "native": "हिन्दी", "rtl": False},
        "ru": {"name": "Russian", "native": "Русский", "rtl": False},
        "it": {"name": "Italian", "native": "Italiano", "rtl": False},
        "tr": {"name": "Turkish", "native": "Türkçe", "rtl": False},
        "pl": {"name": "Polish", "native": "Polski", "rtl": False},
        "nl": {"name": "Dutch", "native": "Nederlands", "rtl": False},
        "sv": {"name": "Swedish", "native": "Svenska", "rtl": False},
        "da": {"name": "Danish", "native": "Dansk", "rtl": False},
        "no": {"name": "Norwegian", "native": "Norsk", "rtl": False},
        "fi": {"name": "Finnish", "native": "Suomi", "rtl": False},
        "he": {"name": "Hebrew", "native": "עברית", "rtl": True},
        "th": {"name": "Thai", "native": "ไทย", "rtl": False},
        "vi": {"name": "Vietnamese", "native": "Tiếng Việt", "rtl": False},
        "uk": {"name": "Ukrainian", "native": "Українська", "rtl": False},
        "cs": {"name": "Czech", "native": "Čeština", "rtl": False},
        "hu": {"name": "Hungarian", "native": "Magyar", "rtl": False},
        "ro": {"name": "Romanian", "native": "Română", "rtl": False},
        "bg": {"name": "Bulgarian", "native": "Български", "rtl": False},
        "hr": {"name": "Croatian", "native": "Hrvatski", "rtl": False},
        "sk": {"name": "Slovak", "native": "Slovenčina", "rtl": False},
        "sl": {"name": "Slovenian", "native": "Slovenščina", "rtl": False},
        "et": {"name": "Estonian", "native": "Eesti", "rtl": False},
        "lv": {"name": "Latvian", "native": "Latviešu", "rtl": False},
        "lt": {"name": "Lithuanian", "native": "Lietuvių", "rtl": False}
    }
    
    # Templates de emergência
    EMERGENCY_TEMPLATES = {
        "medical": {
            "name": "Emergência Médica",
            "icon": "🏥",
            "template": "Emergência médica: {description}. Localização: {location}. Urgência: {urgency}. Vítimas: {casualties}.",
            "priority": "high"
        },
        "fire": {
            "name": "Incêndio",
            "icon": "🔥",
            "template": "Incêndio reportado: {description}. Localização: {location}. Extensão: {extent}. Risco de propagação: {risk}.",
            "priority": "high"
        },
        "natural_disaster": {
            "name": "Desastre Natural",
            "icon": "🌪️",
            "template": "Desastre natural: {description}. Localização: {location}. Situação: {status}. Área afetada: {area}.",
            "priority": "critical"
        },
        "accident": {
            "name": "Acidente",
            "icon": "🚗",
            "template": "Acidente: {description}. Localização: {location}. Vítimas: {casualties}. Bloqueio de via: {blocked}.",
            "priority": "medium"
        },
        "security": {
            "name": "Emergência de Segurança",
            "icon": "🚨",
            "template": "Emergência de segurança: {description}. Localização: {location}. Risco: {risk_level}. Ação necessária: {action}.",
            "priority": "high"
        },
        "chemical": {
            "name": "Vazamento Químico",
            "icon": "☢️",
            "template": "Vazamento químico: {description}. Localização: {location}. Substância: {substance}. Área de evacuação: {evacuation_area}.",
            "priority": "critical"
        },
        "flood": {
            "name": "Inundação",
            "icon": "🌊",
            "template": "Inundação: {description}. Localização: {location}. Nível da água: {water_level}. Pessoas isoladas: {isolated}.",
            "priority": "high"
        },
        "earthquake": {
            "name": "Terremoto",
            "icon": "🏗️",
            "template": "Terremoto: {description}. Localização: {location}. Magnitude: {magnitude}. Danos estruturais: {structural_damage}.",
            "priority": "critical"
        }
    }
    
    # Níveis de urgência
    URGENCY_LEVELS = {
        "low": {"name": "Baixa", "color": "#28a745", "icon": "🟢"},
        "medium": {"name": "Média", "color": "#ffc107", "icon": "🟡"},
        "high": {"name": "Alta", "color": "#fd7e14", "icon": "🟠"},
        "critical": {"name": "Crítica", "color": "#dc3545", "icon": "🔴"}
    }
    
    # Configurações de interface
    UI_CONFIG = {
        "theme": "light",
        "primary_color": "#ff4b4b",
        "background_color": "#ffffff",
        "text_color": "#262730",
        "font_family": "sans-serif"
    }
    
    # Configurações de segurança
    SECURITY_CONFIG = {
        "max_file_size_mb": 50,
        "allowed_domains": [],
        "rate_limit_requests_per_minute": 60,
        "session_timeout_minutes": 30
    }
    
    @classmethod
    def get_language_display_name(cls, lang_code: str) -> str:
        """Retorna o nome de exibição do idioma"""
        lang_info = cls.SUPPORTED_LANGUAGES.get(lang_code, {})
        return f"{lang_info.get('native', lang_code)} ({lang_info.get('name', lang_code)})"
    
    @classmethod
    def is_rtl_language(cls, lang_code: str) -> bool:
        """Verifica se o idioma é da direita para esquerda"""
        return cls.SUPPORTED_LANGUAGES.get(lang_code, {}).get('rtl', False)
    
    @classmethod
    def get_emergency_template(cls, emergency_type: str) -> Dict:
        """Retorna o template de emergência"""
        return cls.EMERGENCY_TEMPLATES.get(emergency_type, cls.EMERGENCY_TEMPLATES['medical'])
    
    @classmethod
    def get_urgency_info(cls, level: str) -> Dict:
        """Retorna informações do nível de urgência"""
        return cls.URGENCY_LEVELS.get(level.lower(), cls.URGENCY_LEVELS['medium'])

# Configurações de ambiente
class EnvironmentConfig:
    """Configurações baseadas em variáveis de ambiente"""

    # Hugging Face
    HF_TOKEN = os.getenv('HUGGING_FACE_TOKEN', os.getenv('HF_TOKEN', ''))
    HF_CACHE_DIR = os.getenv('HF_CACHE_DIR', './cache/huggingface')

    # Configurações de desenvolvimento
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

    # Configurações de dispositivo
    DEVICE = os.getenv('DEVICE', 'auto')  # auto, cpu, cuda
    USE_GPU = os.getenv('USE_GPU', 'True').lower() == 'true'

    # Configurações de cache
    CACHE_DIR = os.getenv('CACHE_DIR', './cache')
    TEMP_DIR = os.getenv('TEMP_DIR', './temp')

    @classmethod
    def ensure_directories(cls):
        """Garante que os diretórios necessários existam"""
        directories = [cls.HF_CACHE_DIR, cls.CACHE_DIR, cls.TEMP_DIR]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    @classmethod
    def setup_huggingface_auth(cls):
        """Configura autenticação do Hugging Face"""
        if cls.HF_TOKEN:
            try:
                from huggingface_hub import login
                login(token=cls.HF_TOKEN, add_to_git_credential=False)
                return True
            except ImportError:
                print("⚠️  huggingface_hub não instalado. Instale com: pip install huggingface_hub")
                return False
            except Exception as e:
                print(f"⚠️  Erro na autenticação HF: {e}")
                return False
        return False

# Constantes do aplicativo
class AppConstants:
    """Constantes gerais do aplicativo"""
    
    APP_NAME = "CrisisComm"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "Comunicador de Emergência Multilíngue"
    
    # URLs e links
    GITHUB_URL = "https://github.com/seu-usuario/crisiscomm"
    DOCUMENTATION_URL = "https://docs.crisiscomm.app"
    SUPPORT_EMAIL = "<EMAIL>"
    
    # Limites do sistema
    MAX_TEXT_LENGTH = 5000
    MAX_TRANSLATION_HISTORY = 100
    MAX_CONCURRENT_REQUESTS = 5
    
    # Formatos de arquivo
    SUPPORTED_AUDIO_FORMATS = ['mp3', 'wav', 'm4a', 'flac', 'ogg']
    SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg', 'webp', 'bmp', 'tiff']
    SUPPORTED_VIDEO_FORMATS = ['mp4', 'avi', 'mov', 'mkv', 'webm']
    
    # Códigos de status
    STATUS_SUCCESS = "success"
    STATUS_ERROR = "error"
    STATUS_WARNING = "warning"
    STATUS_INFO = "info"
