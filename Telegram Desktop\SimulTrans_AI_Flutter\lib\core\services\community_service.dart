import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../app_config.dart';
import '../models/community_feedback.dart';
import '../models/translation_improvement.dart';
import '../models/user_contribution.dart';
import '../utils/logger.dart';
import 'cache_service.dart';
import 'analytics_service.dart';

/// Community service for collaborative translation improvements
class CommunityService {
  static final CommunityService _instance = CommunityService._internal();
  static CommunityService get instance => _instance;
  CommunityService._internal();

  final http.Client _httpClient = http.Client();
  bool _isInitialized = false;
  String? _userId;
  UserContribution? _userProfile;
  
  final List<CommunityFeedback> _pendingFeedback = [];
  Timer? _syncTimer;

  bool get isInitialized => _isInitialized;
  String? get userId => _userId;
  UserContribution? get userProfile => _userProfile;

  /// Initialize community service
  Future<void> initialize({String? userId}) async {
    if (_isInitialized) return;

    try {
      _userId = userId ?? _generateAnonymousUserId();
      
      // Load user profile
      await _loadUserProfile();
      
      // Start periodic sync
      _startPeriodicSync();
      
      _isInitialized = true;
      Logger.info('Community service initialized for user: $_userId');
      
    } catch (e) {
      Logger.error('Failed to initialize community service: $e');
      rethrow;
    }
  }

  /// Submit feedback for a translation
  Future<bool> submitTranslationFeedback({
    required String originalText,
    required String translatedText,
    required String sourceLanguage,
    required String targetLanguage,
    required FeedbackType feedbackType,
    String? improvedTranslation,
    String? comment,
    int? rating, // 1-5 stars
  }) async {
    if (!AppConfig.enableCommunityFeedback) {
      Logger.info('Community feedback is disabled');
      return false;
    }

    try {
      final feedback = CommunityFeedback(
        id: _generateFeedbackId(),
        userId: _userId!,
        originalText: originalText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        feedbackType: feedbackType,
        improvedTranslation: improvedTranslation,
        comment: comment,
        rating: rating,
        timestamp: DateTime.now(),
        isVerified: false,
      );

      // Add to pending queue
      _pendingFeedback.add(feedback);
      
      // Try to submit immediately
      final success = await _submitFeedbackToServer(feedback);
      
      if (success) {
        _pendingFeedback.remove(feedback);
        await _updateUserContribution(feedback);
        
        Logger.info('Translation feedback submitted successfully');
        
        // Track analytics
        AnalyticsService.instance.trackFeatureUsage(
          featureName: 'community_feedback',
          parameters: {
            'feedback_type': feedbackType.name,
            'language_pair': '${sourceLanguage}_to_$targetLanguage',
            'has_improvement': improvedTranslation != null,
            'has_rating': rating != null,
          },
        );
        
        return true;
      } else {
        Logger.info('Feedback queued for later submission');
        return false;
      }
      
    } catch (e) {
      Logger.error('Failed to submit translation feedback: $e');
      return false;
    }
  }

  /// Get translation improvements from community
  Future<List<TranslationImprovement>> getTranslationImprovements({
    required String sourceLanguage,
    required String targetLanguage,
    int limit = 10,
  }) async {
    try {
      // Check cache first
      final cacheKey = 'improvements_${sourceLanguage}_${targetLanguage}_$limit';
      final cached = await CacheService.instance.getModelData(cacheKey);
      
      if (cached != null) {
        final List<dynamic> data = cached['improvements'];
        return data.map((json) => TranslationImprovement.fromJson(json)).toList();
      }

      // Fetch from server
      final improvements = await _fetchImprovementsFromServer(
        sourceLanguage,
        targetLanguage,
        limit,
      );

      // Cache results
      await CacheService.instance.cacheModelData(cacheKey, {
        'improvements': improvements.map((i) => i.toJson()).toList(),
        'cached_at': DateTime.now().toIso8601String(),
      });

      return improvements;
      
    } catch (e) {
      Logger.error('Failed to get translation improvements: $e');
      return [];
    }
  }

  /// Vote on a translation improvement
  Future<bool> voteOnImprovement({
    required String improvementId,
    required bool isUpvote,
  }) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.feedbackApiUrl}/improvements/$improvementId/vote'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': _userId,
          'is_upvote': isUpvote,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        Logger.info('Vote submitted for improvement: $improvementId');
        return true;
      } else {
        Logger.warning('Failed to submit vote: ${response.statusCode}');
        return false;
      }
      
    } catch (e) {
      Logger.error('Failed to vote on improvement: $e');
      return false;
    }
  }

  /// Report inappropriate content
  Future<bool> reportContent({
    required String contentId,
    required String contentType, // 'feedback', 'improvement', 'comment'
    required String reason,
    String? additionalInfo,
  }) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.feedbackApiUrl}/reports'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'reporter_id': _userId,
          'content_id': contentId,
          'content_type': contentType,
          'reason': reason,
          'additional_info': additionalInfo,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      return response.statusCode == 200;
      
    } catch (e) {
      Logger.error('Failed to report content: $e');
      return false;
    }
  }

  /// Get user's contribution statistics
  Future<Map<String, dynamic>> getUserStats() async {
    if (_userProfile == null) {
      await _loadUserProfile();
    }

    return {
      'total_contributions': _userProfile?.totalContributions ?? 0,
      'verified_contributions': _userProfile?.verifiedContributions ?? 0,
      'reputation_score': _userProfile?.reputationScore ?? 0,
      'badges': _userProfile?.badges ?? [],
      'languages_contributed': _userProfile?.languagesContributed ?? [],
      'contribution_streak': _userProfile?.contributionStreak ?? 0,
      'rank': _userProfile?.rank ?? 'Novato',
    };
  }

  /// Get community leaderboard
  Future<List<UserContribution>> getLeaderboard({
    String period = 'month', // 'week', 'month', 'year', 'all'
    int limit = 20,
  }) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.feedbackApiUrl}/leaderboard?period=$period&limit=$limit'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> users = data['users'];
        return users.map((json) => UserContribution.fromJson(json)).toList();
      }
      
      return [];
      
    } catch (e) {
      Logger.error('Failed to get leaderboard: $e');
      return [];
    }
  }

  /// Get trending language pairs
  Future<List<Map<String, dynamic>>> getTrendingLanguagePairs() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.feedbackApiUrl}/trending-pairs'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['pairs']);
      }
      
      return [];
      
    } catch (e) {
      Logger.error('Failed to get trending language pairs: $e');
      return [];
    }
  }

  /// Sync pending feedback
  Future<void> syncPendingFeedback() async {
    if (_pendingFeedback.isEmpty) return;

    final toRemove = <CommunityFeedback>[];
    
    for (final feedback in _pendingFeedback) {
      try {
        final success = await _submitFeedbackToServer(feedback);
        if (success) {
          toRemove.add(feedback);
          await _updateUserContribution(feedback);
        }
      } catch (e) {
        Logger.warning('Failed to sync feedback ${feedback.id}: $e');
      }
    }

    _pendingFeedback.removeWhere((f) => toRemove.contains(f));
    
    if (toRemove.isNotEmpty) {
      Logger.info('Synced ${toRemove.length} pending feedback items');
    }
  }

  /// Dispose resources
  void dispose() {
    _syncTimer?.cancel();
    _httpClient.close();
  }

  // Private helper methods
  Future<void> _loadUserProfile() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.feedbackApiUrl}/users/$_userId'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _userProfile = UserContribution.fromJson(data);
      } else if (response.statusCode == 404) {
        // Create new user profile
        _userProfile = await _createUserProfile();
      }
      
    } catch (e) {
      Logger.warning('Failed to load user profile: $e');
      // Create default profile
      _userProfile = UserContribution.createDefault(_userId!);
    }
  }

  Future<UserContribution> _createUserProfile() async {
    try {
      final newProfile = UserContribution.createDefault(_userId!);
      
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.feedbackApiUrl}/users'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(newProfile.toJson()),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return UserContribution.fromJson(data);
      }
      
      return newProfile;
      
    } catch (e) {
      Logger.error('Failed to create user profile: $e');
      return UserContribution.createDefault(_userId!);
    }
  }

  Future<bool> _submitFeedbackToServer(CommunityFeedback feedback) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('${AppConfig.feedbackApiUrl}/feedback'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(feedback.toJson()),
      );

      return response.statusCode == 201;
      
    } catch (e) {
      Logger.warning('Failed to submit feedback to server: $e');
      return false;
    }
  }

  Future<List<TranslationImprovement>> _fetchImprovementsFromServer(
    String sourceLanguage,
    String targetLanguage,
    int limit,
  ) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('${AppConfig.feedbackApiUrl}/improvements'
            '?source=$sourceLanguage&target=$targetLanguage&limit=$limit'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> improvements = data['improvements'];
        return improvements.map((json) => TranslationImprovement.fromJson(json)).toList();
      }
      
      return [];
      
    } catch (e) {
      Logger.error('Failed to fetch improvements from server: $e');
      return [];
    }
  }

  Future<void> _updateUserContribution(CommunityFeedback feedback) async {
    if (_userProfile == null) return;

    _userProfile = _userProfile!.copyWith(
      totalContributions: _userProfile!.totalContributions + 1,
      lastContribution: DateTime.now(),
    );

    // Add language to contributed languages
    final languages = Set<String>.from(_userProfile!.languagesContributed);
    languages.add(feedback.sourceLanguage);
    languages.add(feedback.targetLanguage);
    
    _userProfile = _userProfile!.copyWith(
      languagesContributed: languages.toList(),
    );
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      syncPendingFeedback();
    });
  }

  String _generateAnonymousUserId() {
    return 'anon_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _generateFeedbackId() {
    return 'feedback_${DateTime.now().millisecondsSinceEpoch}_${_pendingFeedback.length}';
  }
}
