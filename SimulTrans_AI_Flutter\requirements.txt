# CrisisComm - Requisitos do Projeto
# Aplicativo de comunicação de emergência com Gemma 3n

# Framework principal
streamlit>=1.28.0

# Modelo de IA e processamento
transformers>=4.53.0
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
huggingface_hub>=0.19.0

# Processamento de imagem
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# Processamento de áudio
librosa>=0.10.0
soundfile>=0.12.0

# Utilitários
requests>=2.31.0
python-dotenv>=1.0.0

# Aceleração (necessário para Gemma 3n)
accelerate>=0.21.0
bitsandbytes>=0.41.0

# Desenvolvimento e testes
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
