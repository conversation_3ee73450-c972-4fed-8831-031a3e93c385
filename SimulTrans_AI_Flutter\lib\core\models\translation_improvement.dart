import 'package:json_annotation/json_annotation.dart';

part 'translation_improvement.g.dart';

/// Translation improvement suggestion from community
@JsonSerializable()
class TranslationImprovement {
  final String id;
  final String originalText;
  final String originalTranslation;
  final String improvedTranslation;
  final String sourceLanguage;
  final String targetLanguage;
  final String contributorId;
  final String? contributorName;
  final String? explanation;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime? verifiedAt;
  final String? verifiedBy;
  final int upvotes;
  final int downvotes;
  final double confidenceScore;
  final ImprovementType type;
  final bool isVerified;
  final Map<String, dynamic>? metadata;

  TranslationImprovement({
    required this.id,
    required this.originalText,
    required this.originalTranslation,
    required this.improvedTranslation,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.contributorId,
    this.contributorName,
    this.explanation,
    this.tags = const [],
    required this.createdAt,
    this.verifiedAt,
    this.verifiedBy,
    this.upvotes = 0,
    this.downvotes = 0,
    this.confidenceScore = 0.0,
    this.type = ImprovementType.general,
    this.isVerified = false,
    this.metadata,
  });

  /// Create from JSON
  factory TranslationImprovement.fromJson(Map<String, dynamic> json) =>
      _$TranslationImprovementFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$TranslationImprovementToJson(this);

  /// Create a copy with modified fields
  TranslationImprovement copyWith({
    String? id,
    String? originalText,
    String? originalTranslation,
    String? improvedTranslation,
    String? sourceLanguage,
    String? targetLanguage,
    String? contributorId,
    String? contributorName,
    String? explanation,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? verifiedAt,
    String? verifiedBy,
    int? upvotes,
    int? downvotes,
    double? confidenceScore,
    ImprovementType? type,
    bool? isVerified,
    Map<String, dynamic>? metadata,
  }) {
    return TranslationImprovement(
      id: id ?? this.id,
      originalText: originalText ?? this.originalText,
      originalTranslation: originalTranslation ?? this.originalTranslation,
      improvedTranslation: improvedTranslation ?? this.improvedTranslation,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      contributorId: contributorId ?? this.contributorId,
      contributorName: contributorName ?? this.contributorName,
      explanation: explanation ?? this.explanation,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      upvotes: upvotes ?? this.upvotes,
      downvotes: downvotes ?? this.downvotes,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      type: type ?? this.type,
      isVerified: isVerified ?? this.isVerified,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get improvement type display name
  String get typeDisplay {
    switch (type) {
      case ImprovementType.general:
        return 'Geral';
      case ImprovementType.grammar:
        return 'Gramática';
      case ImprovementType.vocabulary:
        return 'Vocabulário';
      case ImprovementType.style:
        return 'Estilo';
      case ImprovementType.cultural:
        return 'Cultural';
      case ImprovementType.technical:
        return 'Técnico';
      case ImprovementType.context:
        return 'Contexto';
      case ImprovementType.formality:
        return 'Formalidade';
    }
  }

  /// Get vote score
  int get voteScore => upvotes - downvotes;

  /// Get vote ratio
  double get voteRatio {
    final total = upvotes + downvotes;
    if (total == 0) return 0.0;
    return upvotes / total;
  }

  /// Get overall quality score
  double get qualityScore {
    double score = 0.0;
    
    // Verification bonus
    if (isVerified) score += 0.4;
    
    // Vote ratio
    score += voteRatio * 0.3;
    
    // Confidence score
    score += confidenceScore * 0.2;
    
    // Explanation bonus
    if (explanation != null && explanation!.length > 20) score += 0.1;
    
    return score.clamp(0.0, 1.0);
  }

  /// Get time since creation
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} dias atrás';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} horas atrás';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutos atrás';
    } else {
      return 'Agora mesmo';
    }
  }

  /// Check if improvement has explanation
  bool get hasExplanation => explanation != null && explanation!.isNotEmpty;

  /// Get contributor display name
  String get contributorDisplay => contributorName ?? 'Usuário ${contributorId.substring(0, 8)}';

  /// Get verification status display
  String get verificationStatus {
    if (isVerified) return 'Verificado';
    if (voteScore >= 5) return 'Bem avaliado';
    if (voteScore >= 0) return 'Neutro';
    return 'Controverso';
  }

  /// Calculate similarity to another improvement
  double calculateSimilarity(TranslationImprovement other) {
    if (originalText != other.originalText) return 0.0;
    if (sourceLanguage != other.sourceLanguage) return 0.0;
    if (targetLanguage != other.targetLanguage) return 0.0;
    
    // Simple similarity based on improved translation
    final words1 = improvedTranslation.toLowerCase().split(' ');
    final words2 = other.improvedTranslation.toLowerCase().split(' ');
    
    final commonWords = words1.where((word) => words2.contains(word)).length;
    final totalWords = (words1.length + words2.length) / 2;
    
    return commonWords / totalWords;
  }

  /// Get improvement impact level
  ImprovementImpact get impactLevel {
    final originalLength = originalTranslation.length;
    final improvedLength = improvedTranslation.length;
    final lengthDiff = (improvedLength - originalLength).abs();
    
    if (lengthDiff > originalLength * 0.5) {
      return ImprovementImpact.major;
    } else if (lengthDiff > originalLength * 0.2) {
      return ImprovementImpact.moderate;
    } else {
      return ImprovementImpact.minor;
    }
  }

  @override
  String toString() {
    return 'TranslationImprovement(id: $id, type: $type, score: $voteScore)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TranslationImprovement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Types of translation improvements
enum ImprovementType {
  general,      // General improvement
  grammar,      // Grammar correction
  vocabulary,   // Better word choice
  style,        // Style improvement
  cultural,     // Cultural adaptation
  technical,    // Technical terminology
  context,      // Context-specific improvement
  formality,    // Formality level adjustment
}

/// Impact level of improvement
enum ImprovementImpact {
  minor,        // Small changes
  moderate,     // Moderate changes
  major,        // Significant changes
}

/// Improvement validation status
enum ValidationStatus {
  pending,      // Awaiting validation
  approved,     // Approved by moderators
  rejected,     // Rejected by moderators
  disputed,     // Under dispute
}
