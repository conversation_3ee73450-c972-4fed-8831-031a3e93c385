"""
🏥 DermatoGemma Multi-Detection System v2.0
Advanced ABCDE Analysis System for Melanoma Detection

This module implements the revolutionary ABCDE analysis system:
- A: Asymmetry Analysis with geometric and AI-based methods
- B: Border Irregularity using advanced edge detection
- C: Color Variation with multi-spectral analysis
- D: Diameter Measurement with precision scaling
- E: Evolution Tracking for temporal analysis
"""

# Suppress warnings before any imports
import os
import warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
warnings.filterwarnings('ignore')

# Suppress absl logging
try:
    import absl.logging
    absl.logging.set_verbosity(absl.logging.ERROR)
except ImportError:
    pass

import cv2
import numpy as np
import logging
from PIL import Image
from typing import Dict, List, Tuple, Optional, Union, Any
import time
from pathlib import Path
from sklearn.cluster import KMeans
from datetime import datetime
from skimage import measure, morphology, segmentation, feature
from skimage.filters import gaussian, sobel
from scipy import ndimage, spatial
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
import json
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedABCDEAnalyzer:
    """
    🎯 Revolutionary ABCDE Analysis System for Melanoma Detection
    
    Features:
    - Advanced Asymmetry Analysis (geometric + AI-based)
    - Sophisticated Border Irregularity Detection
    - Multi-spectral Color Variation Analysis
    - Precision Diameter Measurement
    - Temporal Evolution Tracking
    - Clinical Scoring System
    - Confidence Assessment
    """
    
    def __init__(self):
        # ABCDE scoring weights (clinical guidelines)
        self.abcde_weights = {
            'asymmetry': 0.25,
            'border': 0.25,
            'color': 0.25,
            'diameter': 0.15,
            'evolution': 0.10
        }
        
        # Clinical thresholds
        self.thresholds = {
            'asymmetry_score': 0.3,  # Above this = concerning asymmetry
            'border_irregularity': 0.4,  # Above this = irregular border
            'color_variation': 0.35,  # Above this = significant color variation
            'diameter_mm': 6.0,  # Above 6mm = concerning size
            'evolution_change': 0.2  # Above this = significant evolution
        }
        
        # Analysis configuration
        self.config = {
            'min_lesion_size': 50,  # Minimum pixels for analysis
            'color_clusters': 8,  # Number of color clusters for analysis
            'border_smoothing': 3,  # Gaussian smoothing for border analysis
            'asymmetry_axes': 16,  # Number of axes for asymmetry analysis
            'fourier_descriptors': 32,  # Number of Fourier descriptors
            'texture_window': 9  # Window size for texture analysis
        }
        
        # Performance tracking
        self.analysis_stats = {
            'total_analyses': 0,
            'melanoma_risk_high': 0,
            'melanoma_risk_medium': 0,
            'melanoma_risk_low': 0,
            'processing_times': []
        }
        
        logger.info("🎯 Advanced ABCDE Analyzer initialized")

    def analyze_abcde_comprehensive(self, lesion_image: np.ndarray, contour: np.ndarray, mask: np.ndarray) -> Dict[str, Any]:
        """Comprehensive ABCDE analysis - wrapper for existing method"""
        try:
            # Use existing analyze_abcde method
            return self.analyze_abcde(lesion_image, contour, mask)
        except Exception as e:
            logger.error(f"ABCDE comprehensive analysis failed: {e}")
            return {
                'asymmetry_score': 0.0,
                'border_irregularity_score': 0.0,
                'color_variation_score': 0.0,
                'diameter_score': 0.0,
                'evolution_score': 0.0,
                'overall_abcde_score': 0.0,
                'melanoma_risk_level': 'low',
                'error': str(e)
            }
    
    def analyze_lesion_abcde(self, lesion_image: np.ndarray, 
                           lesion_mask: Optional[np.ndarray] = None,
                           reference_size_mm: Optional[float] = None,
                           previous_analysis: Optional[Dict] = None) -> Dict:
        """
        🔬 Complete ABCDE analysis of a lesion
        
        Args:
            lesion_image: RGB image of the lesion
            lesion_mask: Binary mask of the lesion (optional, will be computed if not provided)
            reference_size_mm: Known size in mm for diameter calculation
            previous_analysis: Previous ABCDE analysis for evolution tracking
            
        Returns:
            Complete ABCDE analysis results with clinical scoring
        """
        start_time = time.time()
        
        try:
            logger.info("🔬 Starting comprehensive ABCDE analysis")
            
            # Prepare lesion mask if not provided
            if lesion_mask is None:
                lesion_mask = self._create_lesion_mask(lesion_image)
            
            # Validate inputs
            if not self._validate_inputs(lesion_image, lesion_mask):
                return self._create_error_result("Invalid input data")
            
            # Perform individual ABCDE analyses
            asymmetry_results = self._analyze_asymmetry(lesion_image, lesion_mask)
            border_results = self._analyze_border_irregularity(lesion_image, lesion_mask)
            color_results = self._analyze_color_variation(lesion_image, lesion_mask)
            diameter_results = self._analyze_diameter(lesion_image, lesion_mask, reference_size_mm)
            evolution_results = self._analyze_evolution(lesion_image, lesion_mask, previous_analysis)
            
            # Calculate overall ABCDE score
            overall_score = self._calculate_overall_abcde_score(
                asymmetry_results, border_results, color_results, 
                diameter_results, evolution_results
            )
            
            # Determine risk level
            risk_assessment = self._assess_melanoma_risk(overall_score)
            
            # Compile comprehensive results
            results = {
                'timestamp': time.time(),
                'processing_time': time.time() - start_time,
                'asymmetry': asymmetry_results,
                'border': border_results,
                'color': color_results,
                'diameter': diameter_results,
                'evolution': evolution_results,
                'overall_score': overall_score,
                'risk_assessment': risk_assessment,
                'clinical_recommendations': self._generate_clinical_recommendations(risk_assessment),
                'confidence': self._calculate_analysis_confidence({
                    'asymmetry': asymmetry_results,
                    'border': border_results,
                    'color': color_results,
                    'diameter': diameter_results,
                    'evolution': evolution_results
                }),
                'success': True
            }
            
            # Update statistics
            self._update_analysis_stats(results)
            
            logger.info(f"✅ ABCDE analysis completed - Risk: {risk_assessment['level']} ({overall_score:.3f})")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ ABCDE analysis failed: {e}")
            return self._create_error_result(f"Analysis error: {str(e)}")
    
    def _analyze_asymmetry(self, lesion_image: np.ndarray, lesion_mask: np.ndarray) -> Dict:
        """
        🔄 Advanced Asymmetry Analysis
        
        Methods:
        - Geometric symmetry analysis
        - Fourier descriptor comparison
        - Multi-axis symmetry evaluation
        - Shape moment analysis
        """
        try:
            logger.info("🔄 Analyzing asymmetry...")
            
            # Find lesion contour
            contours, _ = cv2.findContours(lesion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                return {'score': 0.0, 'confidence': 0.0, 'error': 'No contour found'}
            
            main_contour = max(contours, key=cv2.contourArea)
            
            # Method 1: Geometric symmetry analysis
            geometric_asymmetry = self._calculate_geometric_asymmetry(main_contour, lesion_mask)
            
            # Method 2: Fourier descriptor analysis
            fourier_asymmetry = self._calculate_fourier_asymmetry(main_contour)
            
            # Method 3: Multi-axis symmetry
            multiaxis_asymmetry = self._calculate_multiaxis_asymmetry(lesion_mask)
            
            # Method 4: Moment-based asymmetry
            moment_asymmetry = self._calculate_moment_asymmetry(lesion_mask)
            
            # Combine asymmetry measures
            combined_score = np.mean([
                geometric_asymmetry, fourier_asymmetry, 
                multiaxis_asymmetry, moment_asymmetry
            ])
            
            # Calculate confidence based on consistency
            confidence = 1.0 - np.std([
                geometric_asymmetry, fourier_asymmetry, 
                multiaxis_asymmetry, moment_asymmetry
            ])
            
            return {
                'score': float(combined_score),
                'confidence': float(max(0.0, min(1.0, confidence))),
                'geometric_asymmetry': float(geometric_asymmetry),
                'fourier_asymmetry': float(fourier_asymmetry),
                'multiaxis_asymmetry': float(multiaxis_asymmetry),
                'moment_asymmetry': float(moment_asymmetry),
                'is_concerning': combined_score > self.thresholds['asymmetry_score'],
                'clinical_significance': self._interpret_asymmetry_score(combined_score)
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Asymmetry analysis failed: {e}")
            return {'score': 0.0, 'confidence': 0.0, 'error': str(e)}
    
    def _analyze_border_irregularity(self, lesion_image: np.ndarray, lesion_mask: np.ndarray) -> Dict:
        """
        🔲 Advanced Border Irregularity Analysis
        
        Methods:
        - Edge detection and smoothness analysis
        - Fractal dimension calculation
        - Notching and scalloping detection
        - Curvature variation analysis
        """
        try:
            logger.info("🔲 Analyzing border irregularity...")
            
            # Find lesion contour
            contours, _ = cv2.findContours(lesion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                return {'score': 0.0, 'confidence': 0.0, 'error': 'No contour found'}
            
            main_contour = max(contours, key=cv2.contourArea)
            
            # Method 1: Perimeter-to-area ratio (compactness)
            area = cv2.contourArea(main_contour)
            perimeter = cv2.arcLength(main_contour, True)
            compactness = (perimeter ** 2) / (4 * np.pi * area) if area > 0 else 0
            
            # Method 2: Fractal dimension
            fractal_dimension = self._calculate_fractal_dimension(main_contour)
            
            # Method 3: Curvature variation
            curvature_variation = self._calculate_curvature_variation(main_contour)
            
            # Method 4: Notching detection
            notching_score = self._detect_border_notching(main_contour)
            
            # Method 5: Smoothness analysis
            smoothness_score = self._calculate_border_smoothness(main_contour)
            
            # Combine border irregularity measures
            # Normalize compactness (1.0 = perfect circle)
            normalized_compactness = min(1.0, (compactness - 1.0) / 2.0) if compactness > 1.0 else 0.0
            
            combined_score = np.mean([
                normalized_compactness,
                fractal_dimension / 2.0,  # Normalize fractal dimension
                curvature_variation,
                notching_score,
                1.0 - smoothness_score  # Invert smoothness (higher = more irregular)
            ])
            
            # Calculate confidence
            confidence = 0.8  # High confidence for border analysis
            
            return {
                'score': float(combined_score),
                'confidence': float(confidence),
                'compactness': float(compactness),
                'fractal_dimension': float(fractal_dimension),
                'curvature_variation': float(curvature_variation),
                'notching_score': float(notching_score),
                'smoothness_score': float(smoothness_score),
                'is_concerning': combined_score > self.thresholds['border_irregularity'],
                'clinical_significance': self._interpret_border_score(combined_score, {
                    'fractal_dimension': fractal_dimension,
                    'curvature_variation': curvature_variation,
                    'notching_score': notching_score,
                    'smoothness_score': smoothness_score
                })
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Border analysis failed: {e}")
            return {'score': 0.0, 'confidence': 0.0, 'error': str(e)}
    
    def _analyze_color_variation(self, lesion_image: np.ndarray, lesion_mask: np.ndarray) -> Dict:
        """
        🎨 Advanced Color Variation Analysis
        
        Methods:
        - Multi-spectral color space analysis (RGB, HSV, LAB)
        - Color clustering and distribution
        - Pigmentation pattern detection
        - Color heterogeneity measurement
        """
        try:
            logger.info("🎨 Analyzing color variation...")
            
            # Extract lesion pixels
            lesion_pixels = lesion_image[lesion_mask > 0]
            if len(lesion_pixels) == 0:
                return {'score': 0.0, 'confidence': 0.0, 'error': 'No lesion pixels found'}
            
            # Method 1: RGB color variation
            rgb_variation = self._calculate_rgb_variation(lesion_pixels)
            
            # Method 2: HSV color analysis
            hsv_variation = self._calculate_hsv_variation(lesion_image, lesion_mask)
            
            # Method 3: LAB color space analysis
            lab_variation = self._calculate_lab_variation(lesion_image, lesion_mask)
            
            # Method 4: Color clustering analysis
            clustering_variation = self._calculate_color_clustering_variation(lesion_pixels)
            
            # Method 5: Pigmentation pattern analysis
            pigmentation_score = self._analyze_pigmentation_patterns(lesion_image, lesion_mask)
            
            # Combine color variation measures
            combined_score = np.mean([
                rgb_variation, hsv_variation, lab_variation,
                clustering_variation, pigmentation_score
            ])
            
            # Calculate confidence
            confidence = 0.85  # High confidence for color analysis
            
            return {
                'score': float(combined_score),
                'confidence': float(confidence),
                'rgb_variation': float(rgb_variation),
                'hsv_variation': float(hsv_variation),
                'lab_variation': float(lab_variation),
                'clustering_variation': float(clustering_variation),
                'pigmentation_score': float(pigmentation_score),
                'dominant_colors': self._extract_dominant_colors(lesion_pixels),
                'is_concerning': combined_score > self.thresholds['color_variation'],
                'clinical_significance': self._interpret_color_score(combined_score)
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Color analysis failed: {e}")
            return {'score': 0.0, 'confidence': 0.0, 'error': str(e)}

    def _analyze_diameter(self, lesion_image: np.ndarray, lesion_mask: np.ndarray,
                         reference_size_mm: Optional[float] = None) -> Dict:
        """
        📏 Advanced Diameter Analysis

        Methods:
        - Maximum diameter calculation
        - Equivalent diameter (area-based)
        - Perimeter-based diameter
        - 3D volume estimation
        """
        try:
            logger.info("📏 Analyzing diameter...")

            # Find lesion contour
            contours, _ = cv2.findContours(lesion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                return {'score': 0.0, 'confidence': 0.0, 'error': 'No contour found'}

            main_contour = max(contours, key=cv2.contourArea)

            # Method 1: Maximum diameter (Feret diameter)
            diameter_info = self._calculate_max_diameter(lesion_image, lesion_mask, reference_size_mm)
            max_diameter_pixels = diameter_info.get('diameter_pixels', 0.0)

            # Method 2: Equivalent diameter (area-based)
            area = cv2.contourArea(main_contour)
            equivalent_diameter_pixels = 2 * np.sqrt(area / np.pi)

            # Method 3: Average diameter
            perimeter = cv2.arcLength(main_contour, True)
            avg_diameter_pixels = perimeter / np.pi

            # Convert to millimeters if reference provided
            if reference_size_mm is not None:
                # Assume reference_size_mm corresponds to the maximum dimension
                pixel_to_mm_ratio = reference_size_mm / max(lesion_image.shape[:2])
                max_diameter_mm = max_diameter_pixels * pixel_to_mm_ratio
                equivalent_diameter_mm = equivalent_diameter_pixels * pixel_to_mm_ratio
                avg_diameter_mm = avg_diameter_pixels * pixel_to_mm_ratio
            else:
                # Use pixel measurements
                max_diameter_mm = max_diameter_pixels
                equivalent_diameter_mm = equivalent_diameter_pixels
                avg_diameter_mm = avg_diameter_pixels

            # Calculate diameter score (normalized by 6mm threshold)
            diameter_score = min(1.0, max_diameter_mm / self.thresholds['diameter_mm'])

            # Calculate confidence
            confidence = 0.9 if reference_size_mm is not None else 0.6

            return {
                'score': float(diameter_score),
                'confidence': float(confidence),
                'max_diameter_pixels': float(max_diameter_pixels),
                'equivalent_diameter_pixels': float(equivalent_diameter_pixels),
                'avg_diameter_pixels': float(avg_diameter_pixels),
                'max_diameter_mm': float(max_diameter_mm),
                'equivalent_diameter_mm': float(equivalent_diameter_mm),
                'avg_diameter_mm': float(avg_diameter_mm),
                'area_pixels': float(area),
                'has_reference': reference_size_mm is not None,
                'is_concerning': max_diameter_mm > self.thresholds['diameter_mm'],
                'clinical_significance': self._interpret_diameter_score({
                    'diameter_pixels': max_diameter_pixels,
                    'diameter_mm': max_diameter_mm,
                    'area_pixels': area
                })
            }

        except Exception as e:
            logger.warning(f"⚠️ Diameter analysis failed: {e}")
            return {'score': 0.0, 'confidence': 0.0, 'error': str(e)}

    def _analyze_evolution(self, lesion_image: np.ndarray, lesion_mask: np.ndarray,
                          previous_analysis: Optional[Dict] = None) -> Dict:
        """
        📈 Evolution Analysis (Temporal Changes)

        Methods:
        - Size change detection
        - Shape evolution analysis
        - Color change tracking
        - New feature appearance
        """
        try:
            logger.info("📈 Analyzing evolution...")

            if previous_analysis is None:
                return {
                    'score': 0.0,
                    'confidence': 0.0,
                    'has_previous_data': False,
                    'message': 'No previous analysis available for comparison'
                }

            # Extract current measurements
            current_area = np.sum(lesion_mask > 0)
            current_contours, _ = cv2.findContours(lesion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not current_contours:
                return {'score': 0.0, 'confidence': 0.0, 'error': 'No current contour found'}

            current_contour = max(current_contours, key=cv2.contourArea)
            current_perimeter = cv2.arcLength(current_contour, True)

            # Extract previous measurements
            prev_diameter = previous_analysis.get('diameter', {})
            prev_area = prev_diameter.get('area_pixels', current_area)

            # Calculate changes
            area_change = abs(current_area - prev_area) / prev_area if prev_area > 0 else 0

            # Size evolution score
            size_evolution = min(1.0, area_change * 2)  # Normalize to 0-1

            # Shape evolution (if previous contour available)
            shape_evolution = 0.0
            if 'shape_descriptor' in previous_analysis:
                shape_evolution = self._calculate_shape_evolution(current_contour, previous_analysis)

            # Color evolution (if previous color data available)
            color_evolution = 0.0
            if 'color' in previous_analysis:
                color_evolution = self._calculate_color_evolution(lesion_image, lesion_mask, previous_analysis)

            # Combined evolution score
            combined_score = np.mean([size_evolution, shape_evolution, color_evolution])

            # Calculate confidence
            confidence = 0.7 if len(previous_analysis) > 3 else 0.4

            return {
                'score': float(combined_score),
                'confidence': float(confidence),
                'has_previous_data': True,
                'area_change_percent': float(area_change * 100),
                'size_evolution': float(size_evolution),
                'shape_evolution': float(shape_evolution),
                'color_evolution': float(color_evolution),
                'is_concerning': combined_score > self.thresholds['evolution_change'],
                'clinical_significance': self._interpret_evolution_score(combined_score)
            }

        except Exception as e:
            logger.warning(f"⚠️ Evolution analysis failed: {e}")
            return {'score': 0.0, 'confidence': 0.0, 'error': str(e)}

    def _calculate_geometric_asymmetry(self, contour: np.ndarray, mask: np.ndarray) -> float:
        """REAL IMPLEMENTATION: Calculate geometric asymmetry using advanced mathematical methods"""
        try:
            logger.info("🔍 Starting REAL geometric asymmetry analysis...")

            # Get moments for centroid
            M = cv2.moments(mask)
            if M['m00'] == 0:
                return 0.0

            cx = int(M['m10'] / M['m00'])
            cy = int(M['m01'] / M['m00'])

            # Method 1: Multi-axis asymmetry analysis (enhanced)
            asymmetry_scores = []

            # Test comprehensive set of axes (every 10 degrees)
            for angle in np.linspace(0, np.pi, 18):  # 18 axes for thorough analysis
                axis_asymmetry = self._calculate_real_axis_asymmetry_advanced(mask, cx, cy, angle)
                asymmetry_scores.append(axis_asymmetry)

            # Method 2: Principal Component Analysis for natural axes
            pca_asymmetry = self._calculate_pca_asymmetry_real(contour, cx, cy)

            # Method 3: Moment-based asymmetry (using higher order moments)
            moment_asymmetry = self._calculate_moment_asymmetry_real(M)

            # Method 4: Fourier descriptor asymmetry
            fourier_asymmetry = self._calculate_fourier_asymmetry_real(contour)

            # Method 5: Radial distance asymmetry
            radial_asymmetry = self._calculate_radial_asymmetry_real(contour, cx, cy)

            # Combine all methods with clinical weights
            geometric_asymmetry = np.max(asymmetry_scores)  # Worst axis asymmetry
            combined_asymmetry = (
                0.3 * geometric_asymmetry +      # Multi-axis analysis
                0.25 * pca_asymmetry +           # Principal component
                0.2 * moment_asymmetry +         # Moment-based
                0.15 * fourier_asymmetry +       # Fourier descriptors
                0.1 * radial_asymmetry           # Radial analysis
            )

            logger.info(f"✅ REAL Asymmetry Components: Geometric={geometric_asymmetry:.3f}, PCA={pca_asymmetry:.3f}, Moment={moment_asymmetry:.3f}, Fourier={fourier_asymmetry:.3f}, Radial={radial_asymmetry:.3f}")

            return min(combined_asymmetry, 1.0)

        except Exception as e:
            logger.warning(f"⚠️ REAL geometric asymmetry calculation failed: {e}")
            return 0.0

        except Exception as e:
            logger.warning(f"Geometric asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_fourier_asymmetry(self, contour: np.ndarray) -> float:
        """Calculate asymmetry using Fourier descriptors"""
        try:
            # Resample contour to fixed number of points
            contour_points = contour.reshape(-1, 2)

            if len(contour_points) < 10:
                return 0.0

            # Calculate Fourier descriptors
            complex_contour = contour_points[:, 0] + 1j * contour_points[:, 1]
            fourier_desc = np.fft.fft(complex_contour)

            # Normalize by first descriptor
            if abs(fourier_desc[0]) > 0:
                fourier_desc = fourier_desc / fourier_desc[0]

            # Calculate asymmetry from high-frequency components
            high_freq = fourier_desc[len(fourier_desc)//4:]
            asymmetry = np.mean(np.abs(high_freq))

            return min(1.0, asymmetry)

        except Exception as e:
            logger.warning(f"Fourier asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_multiaxis_asymmetry(self, mask: np.ndarray) -> float:
        """Calculate asymmetry across multiple axes"""
        try:
            # Find principal axes using PCA
            y_coords, x_coords = np.where(mask > 0)
            if len(x_coords) < 10:
                return 0.0

            points = np.column_stack([x_coords, y_coords])

            # Calculate covariance matrix
            cov_matrix = np.cov(points.T)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

            # Sort by eigenvalue
            idx = eigenvalues.argsort()[::-1]
            eigenvectors = eigenvectors[:, idx]

            # Calculate asymmetry along principal axes
            asymmetries = []
            for i in range(2):  # Two principal axes
                axis = eigenvectors[:, i]
                asymmetry = self._calculate_axis_asymmetry(points, axis)
                asymmetries.append(asymmetry)

            return np.mean(asymmetries)

        except Exception as e:
            logger.warning(f"Multi-axis asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_moment_asymmetry(self, mask: np.ndarray) -> float:
        """Calculate asymmetry using image moments"""
        try:
            # Calculate central moments
            moments = cv2.moments(mask)
            if moments['m00'] == 0:
                return 0.0

            # Calculate normalized central moments
            mu20 = moments['mu20'] / moments['m00']
            mu02 = moments['mu02'] / moments['m00']
            mu11 = moments['mu11'] / moments['m00']

            # Calculate asymmetry from moment invariants
            asymmetry = abs(mu20 - mu02) / (mu20 + mu02 + 1e-7)

            return min(1.0, asymmetry)

        except Exception as e:
            logger.warning(f"Moment asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_fractal_dimension(self, contour: np.ndarray) -> float:
        """Calculate fractal dimension of the border"""
        try:
            # Box counting method for fractal dimension
            contour_points = contour.reshape(-1, 2)

            if len(contour_points) < 10:
                return 1.0

            # Create different box sizes
            min_coord = np.min(contour_points, axis=0)
            max_coord = np.max(contour_points, axis=0)
            size = np.max(max_coord - min_coord)

            box_sizes = []
            box_counts = []

            for box_size in [size/4, size/8, size/16, size/32]:
                if box_size < 1:
                    continue

                # Count boxes containing contour points
                boxes = set()
                for point in contour_points:
                    box_x = int((point[0] - min_coord[0]) / box_size)
                    box_y = int((point[1] - min_coord[1]) / box_size)
                    boxes.add((box_x, box_y))

                box_sizes.append(box_size)
                box_counts.append(len(boxes))

            if len(box_sizes) < 2:
                return 1.0

            # Calculate fractal dimension using linear regression
            log_sizes = np.log(box_sizes)
            log_counts = np.log(box_counts)

            # Linear regression
            A = np.vstack([log_sizes, np.ones(len(log_sizes))]).T
            slope, _ = np.linalg.lstsq(A, log_counts, rcond=None)[0]

            fractal_dim = -slope
            return max(1.0, min(2.0, fractal_dim))

        except Exception as e:
            logger.warning(f"Fractal dimension calculation failed: {e}")
            return 1.0

    def _calculate_curvature_variation(self, contour: np.ndarray) -> float:
        """Calculate variation in border curvature"""
        try:
            contour_points = contour.reshape(-1, 2)

            if len(contour_points) < 6:
                return 0.0

            curvatures = []

            # Calculate curvature at each point
            for i in range(len(contour_points)):
                p1 = contour_points[i-2]
                p2 = contour_points[i-1]
                p3 = contour_points[i]

                # Calculate curvature using three points
                v1 = p2 - p1
                v2 = p3 - p2

                # Cross product for curvature
                cross = v1[0] * v2[1] - v1[1] * v2[0]
                norm1 = np.linalg.norm(v1)
                norm2 = np.linalg.norm(v2)

                if norm1 > 0 and norm2 > 0:
                    curvature = abs(cross) / (norm1 * norm2)
                    curvatures.append(curvature)

            if not curvatures:
                return 0.0

            # Return coefficient of variation
            mean_curvature = np.mean(curvatures)
            std_curvature = np.std(curvatures)

            if mean_curvature > 0:
                return min(1.0, std_curvature / mean_curvature)
            else:
                return 0.0

        except Exception as e:
            logger.warning(f"Curvature variation calculation failed: {e}")
            return 0.0

    def _detect_border_notching(self, contour: np.ndarray) -> float:
        """Detect notching and scalloping in the border"""
        try:
            contour_points = contour.reshape(-1, 2)

            if len(contour_points) < 10:
                return 0.0

            # Smooth the contour
            smoothed_contour = cv2.GaussianBlur(contour_points.astype(np.float32),
                                              (self.config['border_smoothing'], 1), 0)

            # Calculate distances from original to smoothed contour
            distances = []
            for i, point in enumerate(contour_points):
                smooth_point = smoothed_contour[i]
                distance = np.linalg.norm(point - smooth_point)
                distances.append(distance)

            # Detect significant deviations (notches)
            mean_distance = np.mean(distances)
            std_distance = np.std(distances)
            threshold = mean_distance + 2 * std_distance

            notches = sum(1 for d in distances if d > threshold)
            notching_score = min(1.0, notches / len(distances) * 10)

            return notching_score

        except Exception as e:
            logger.warning(f"Border notching detection failed: {e}")
            return 0.0

    def _calculate_border_smoothness(self, contour: np.ndarray) -> float:
        """Calculate overall border smoothness"""
        try:
            contour_points = contour.reshape(-1, 2)

            if len(contour_points) < 6:
                return 1.0

            # Calculate second derivatives (acceleration)
            accelerations = []

            for i in range(2, len(contour_points)):
                p1 = contour_points[i-2]
                p2 = contour_points[i-1]
                p3 = contour_points[i]

                # Second derivative approximation
                acceleration = p3 - 2*p2 + p1
                acc_magnitude = np.linalg.norm(acceleration)
                accelerations.append(acc_magnitude)

            if not accelerations:
                return 1.0

            # Smoothness is inverse of mean acceleration
            mean_acceleration = np.mean(accelerations)
            smoothness = 1.0 / (1.0 + mean_acceleration)

            return smoothness

        except Exception as e:
            logger.warning(f"Border smoothness calculation failed: {e}")
            return 1.0

    def _calculate_real_axis_asymmetry_advanced(self, mask: np.ndarray, cx: int, cy: int, angle: float) -> float:
        """REAL advanced axis asymmetry calculation with shape analysis"""
        try:
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            h, w = mask.shape
            y_coords, x_coords = np.ogrid[:h, :w]

            # Points on each side of the axis
            side1 = ((x_coords - cx) * cos_a + (y_coords - cy) * sin_a) >= 0
            side2 = ~side1

            # Calculate not just areas but also shape characteristics
            mask1 = mask & side1
            mask2 = mask & side2

            area1 = np.sum(mask1)
            area2 = np.sum(mask2)

            if area1 + area2 == 0:
                return 0.0

            # Area asymmetry
            area_asymmetry = abs(area1 - area2) / (area1 + area2)

            # Shape asymmetry (moments)
            if area1 > 0 and area2 > 0:
                # Calculate second moments for each side
                M1 = cv2.moments(mask1.astype(np.uint8))
                M2 = cv2.moments(mask2.astype(np.uint8))

                # Normalized second moments
                if M1['m00'] > 0 and M2['m00'] > 0:
                    mu20_1 = M1['mu20'] / M1['m00']
                    mu02_1 = M1['mu02'] / M1['m00']
                    mu20_2 = M2['mu20'] / M2['m00']
                    mu02_2 = M2['mu02'] / M2['m00']

                    shape_asymmetry = abs(mu20_1 - mu20_2) + abs(mu02_1 - mu02_2)
                    shape_asymmetry = min(shape_asymmetry / 1000, 1.0)  # Normalize
                else:
                    shape_asymmetry = 0.0
            else:
                shape_asymmetry = 1.0  # Maximum asymmetry if one side is empty

            # Combined asymmetry
            combined = 0.7 * area_asymmetry + 0.3 * shape_asymmetry
            return combined

        except Exception as e:
            logger.warning(f"⚠️ Advanced axis asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_pca_asymmetry_real(self, contour: np.ndarray, cx: int, cy: int) -> float:
        """REAL PCA-based asymmetry analysis"""
        try:
            # Extract contour points
            points = contour.reshape(-1, 2).astype(np.float32)
            if len(points) < 5:
                return 0.0

            # Center the points
            centered_points = points - np.array([cx, cy])

            # Perform PCA
            cov_matrix = np.cov(centered_points.T)
            eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

            # Sort by eigenvalue
            idx = np.argsort(eigenvalues)[::-1]
            eigenvalues = eigenvalues[idx]
            eigenvectors = eigenvectors[:, idx]

            # Principal axes
            principal_axis = eigenvectors[:, 0]
            secondary_axis = eigenvectors[:, 1]

            # Calculate asymmetry along principal axes
            principal_asymmetry = self._calculate_axis_asymmetry_from_points(
                centered_points, principal_axis
            )

            secondary_asymmetry = self._calculate_axis_asymmetry_from_points(
                centered_points, secondary_axis
            )

            # Eigenvalue ratio (shape elongation)
            if eigenvalues[1] > 0:
                elongation = eigenvalues[0] / eigenvalues[1]
                elongation_factor = min(elongation / 10.0, 1.0)  # Normalize
            else:
                elongation_factor = 1.0

            # Combined PCA asymmetry
            pca_asymmetry = 0.5 * principal_asymmetry + 0.3 * secondary_asymmetry + 0.2 * elongation_factor

            return min(pca_asymmetry, 1.0)

        except Exception as e:
            logger.warning(f"⚠️ PCA asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_axis_asymmetry_from_points(self, points: np.ndarray, axis: np.ndarray) -> float:
        """Calculate asymmetry of points along a given axis"""
        try:
            # Project points onto the axis
            projections = np.dot(points, axis)

            # Split points by sign of projection
            positive_side = points[projections >= 0]
            negative_side = points[projections < 0]

            if len(positive_side) == 0 or len(negative_side) == 0:
                return 1.0  # Maximum asymmetry

            # Calculate asymmetry based on point distribution
            pos_count = len(positive_side)
            neg_count = len(negative_side)

            count_asymmetry = abs(pos_count - neg_count) / (pos_count + neg_count)

            # Distance asymmetry
            pos_distances = np.abs(np.dot(positive_side, axis))
            neg_distances = np.abs(np.dot(negative_side, axis))

            pos_mean_dist = np.mean(pos_distances)
            neg_mean_dist = np.mean(neg_distances)

            if pos_mean_dist + neg_mean_dist > 0:
                distance_asymmetry = abs(pos_mean_dist - neg_mean_dist) / (pos_mean_dist + neg_mean_dist)
            else:
                distance_asymmetry = 0.0

            # Combined asymmetry
            return 0.6 * count_asymmetry + 0.4 * distance_asymmetry

        except Exception as e:
            logger.warning(f"⚠️ Axis asymmetry from points calculation failed: {e}")
            return 0.0

    def _calculate_moment_asymmetry_real(self, moments: dict) -> float:
        """REAL moment-based asymmetry using higher order moments"""
        try:
            if moments['m00'] == 0:
                return 0.0

            # Normalized central moments
            mu20 = moments['mu20'] / moments['m00']
            mu02 = moments['mu02'] / moments['m00']
            mu11 = moments['mu11'] / moments['m00']
            mu30 = moments['mu30'] / moments['m00']
            mu03 = moments['mu03'] / moments['m00']
            mu21 = moments['mu21'] / moments['m00']
            mu12 = moments['mu12'] / moments['m00']

            # Second order asymmetry (ellipticity)
            if mu20 + mu02 > 0:
                ellipticity = abs(mu20 - mu02) / (mu20 + mu02)
            else:
                ellipticity = 0.0

            # Third order asymmetry (skewness)
            skewness_x = abs(mu30) / (mu20 ** 1.5) if mu20 > 0 else 0.0
            skewness_y = abs(mu03) / (mu02 ** 1.5) if mu02 > 0 else 0.0

            # Cross moment asymmetry
            cross_asymmetry = abs(mu11) / np.sqrt(mu20 * mu02) if mu20 * mu02 > 0 else 0.0

            # Mixed moments asymmetry
            mixed_asymmetry = (abs(mu21) + abs(mu12)) / (mu20 + mu02) if mu20 + mu02 > 0 else 0.0

            # Combined moment asymmetry
            moment_asymmetry = (
                0.3 * ellipticity +
                0.25 * skewness_x +
                0.25 * skewness_y +
                0.1 * cross_asymmetry +
                0.1 * mixed_asymmetry
            )

            return min(moment_asymmetry, 1.0)

        except Exception as e:
            logger.warning(f"⚠️ Moment asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_fourier_asymmetry_real(self, contour: np.ndarray) -> float:
        """REAL Fourier descriptor-based asymmetry analysis"""
        try:
            # Extract contour points
            points = contour.reshape(-1, 2)
            if len(points) < 8:
                return 0.0

            # Convert to complex representation
            complex_points = points[:, 0] + 1j * points[:, 1]

            # Center the contour
            centroid = np.mean(complex_points)
            centered_points = complex_points - centroid

            # Compute Fourier descriptors
            fourier_desc = np.fft.fft(centered_points)

            # Normalize by the first descriptor (DC component)
            if abs(fourier_desc[0]) > 0:
                normalized_desc = fourier_desc / fourier_desc[0]
            else:
                return 0.0

            # Asymmetry from Fourier descriptors
            # Even harmonics represent symmetric components
            # Odd harmonics represent asymmetric components
            n = len(normalized_desc)

            even_energy = 0.0
            odd_energy = 0.0

            for i in range(1, min(n//2, 10)):  # Use first 10 harmonics
                if i % 2 == 0:
                    even_energy += abs(normalized_desc[i]) ** 2
                else:
                    odd_energy += abs(normalized_desc[i]) ** 2

            total_energy = even_energy + odd_energy
            if total_energy > 0:
                asymmetry_ratio = odd_energy / total_energy
            else:
                asymmetry_ratio = 0.0

            return min(asymmetry_ratio, 1.0)

        except Exception as e:
            logger.warning(f"⚠️ Fourier asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_radial_asymmetry_real(self, contour: np.ndarray, cx: int, cy: int) -> float:
        """REAL radial distance asymmetry analysis"""
        try:
            # Extract contour points
            points = contour.reshape(-1, 2)
            if len(points) < 8:
                return 0.0

            # Calculate distances from centroid
            distances = np.sqrt((points[:, 0] - cx)**2 + (points[:, 1] - cy)**2)

            # Calculate angles
            angles = np.arctan2(points[:, 1] - cy, points[:, 0] - cx)

            # Normalize angles to [0, 2π]
            angles = (angles + 2*np.pi) % (2*np.pi)

            # Create radial profile by binning
            n_bins = 36  # 10-degree bins
            bin_edges = np.linspace(0, 2*np.pi, n_bins + 1)

            radial_profile = []
            for i in range(n_bins):
                mask = (angles >= bin_edges[i]) & (angles < bin_edges[i+1])
                if np.any(mask):
                    radial_profile.append(np.mean(distances[mask]))
                else:
                    # Interpolate missing values
                    if i > 0:
                        radial_profile.append(radial_profile[-1])
                    else:
                        radial_profile.append(np.mean(distances))

            radial_profile = np.array(radial_profile)

            # Calculate asymmetry by comparing opposite sides
            asymmetry_scores = []
            for i in range(n_bins // 2):
                opposite_idx = (i + n_bins // 2) % n_bins
                if radial_profile[i] + radial_profile[opposite_idx] > 0:
                    asymmetry = abs(radial_profile[i] - radial_profile[opposite_idx]) / (radial_profile[i] + radial_profile[opposite_idx])
                    asymmetry_scores.append(asymmetry)

            if asymmetry_scores:
                return np.mean(asymmetry_scores)
            else:
                return 0.0

        except Exception as e:
            logger.warning(f"⚠️ Radial asymmetry calculation failed: {e}")
            return 0.0

    def _calculate_real_border_irregularity_advanced(self, contour: np.ndarray) -> float:
        """REAL advanced border irregularity calculation using multiple methods"""
        try:
            logger.info("🔍 Starting REAL advanced border irregularity analysis...")

            # Method 1: Fractal dimension
            fractal_dim = self._calculate_fractal_dimension_real(contour)

            # Method 2: Curvature analysis
            curvature_irregularity = self._calculate_curvature_irregularity_real(contour)

            # Method 3: Perimeter-to-area ratio
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            if area > 0:
                compactness = (perimeter ** 2) / (4 * np.pi * area)
                compactness_irregularity = min((compactness - 1.0) / 2.0, 1.0)  # Normalize
            else:
                compactness_irregularity = 0.0

            # Method 4: Convexity defects
            convexity_irregularity = self._calculate_convexity_defects_real(contour)

            # Method 5: Smoothness analysis
            smoothness_irregularity = 1.0 - self._calculate_border_smoothness_real(contour)

            # Combine all methods with clinical weights
            combined_irregularity = (
                0.25 * fractal_dim +
                0.25 * curvature_irregularity +
                0.2 * compactness_irregularity +
                0.15 * convexity_irregularity +
                0.15 * smoothness_irregularity
            )

            logger.info(f"✅ REAL Border Components: Fractal={fractal_dim:.3f}, Curvature={curvature_irregularity:.3f}, Compactness={compactness_irregularity:.3f}, Convexity={convexity_irregularity:.3f}, Smoothness={smoothness_irregularity:.3f}")

            return min(combined_irregularity, 1.0)

        except Exception as e:
            logger.warning(f"⚠️ REAL border irregularity calculation failed: {e}")
            return 0.0

    def _validate_inputs(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> bool:
        """Validate input data for ABCDE analysis"""
        try:
            # Check if lesion_image is valid
            if lesion_image is None:
                logger.error("❌ Lesion image is None")
                return False

            if not isinstance(lesion_image, np.ndarray):
                logger.error("❌ Lesion image is not a numpy array")
                return False

            if lesion_image.size == 0:
                logger.error("❌ Lesion image is empty")
                return False

            # Check image dimensions
            if len(lesion_image.shape) != 3:
                logger.error(f"❌ Invalid image shape: {lesion_image.shape}. Expected 3D array (H, W, C)")
                return False

            if lesion_image.shape[2] != 3:
                logger.error(f"❌ Invalid number of channels: {lesion_image.shape[2]}. Expected 3 (RGB)")
                return False

            # Check image size
            height, width = lesion_image.shape[:2]
            if height < 10 or width < 10:
                logger.error(f"❌ Image too small: {width}x{height}. Minimum size is 10x10")
                return False

            # Check lesion mask if provided
            if lesion_mask is not None:
                if not isinstance(lesion_mask, np.ndarray):
                    logger.error("❌ Lesion mask is not a numpy array")
                    return False

                if lesion_mask.shape[:2] != lesion_image.shape[:2]:
                    logger.error(f"❌ Mask shape {lesion_mask.shape[:2]} doesn't match image shape {lesion_image.shape[:2]}")
                    return False

            # Check data type and range
            if lesion_image.dtype != np.uint8:
                if lesion_image.max() <= 1.0:
                    # Probably normalized to [0,1], convert to [0,255]
                    lesion_image = (lesion_image * 255).astype(np.uint8)
                else:
                    logger.warning(f"⚠️ Unexpected image data type: {lesion_image.dtype}")

            logger.debug("✅ Input validation passed")
            return True

        except Exception as e:
            logger.error(f"❌ Input validation failed: {e}")
            return False

    def _create_error_result(self, error_message: str) -> Dict:
        """Create error result structure for ABCDE analysis"""
        return {
            'timestamp': datetime.now().isoformat(),
            'processing_time': 0.0,
            'asymmetry': {
                'score': 0.0,
                'confidence': 0.0,
                'analysis_method': 'error',
                'error': error_message
            },
            'border': {
                'score': 0.0,
                'confidence': 0.0,
                'analysis_method': 'error',
                'error': error_message
            },
            'color': {
                'score': 0.0,
                'confidence': 0.0,
                'analysis_method': 'error',
                'error': error_message
            },
            'diameter': {
                'score': 0.0,
                'confidence': 0.0,
                'analysis_method': 'error',
                'error': error_message
            },
            'evolution': {
                'score': 0.0,
                'confidence': 0.0,
                'analysis_method': 'error',
                'error': error_message
            },
            'overall_score': 0.0,
            'melanoma_risk_level': 'unknown',
            'clinical_significance': 'error',
            'recommendations': [
                "ABCDE analysis failed - professional evaluation required",
                f"Error: {error_message}"
            ],
            'success': False,
            'error': error_message
        }

    def _calculate_axis_asymmetry(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> float:
        """Calculate multi-axis asymmetry"""
        try:
            # Simple implementation - compare horizontal and vertical asymmetry
            height, width = lesion_image.shape[:2]

            # Horizontal asymmetry
            left_half = lesion_image[:, :width//2]
            right_half = lesion_image[:, width//2:]
            right_half_flipped = np.fliplr(right_half)

            # Resize to match if needed
            min_width = min(left_half.shape[1], right_half_flipped.shape[1])
            left_half = left_half[:, :min_width]
            right_half_flipped = right_half_flipped[:, :min_width]

            horizontal_diff = np.mean(np.abs(left_half.astype(float) - right_half_flipped.astype(float)))

            # Vertical asymmetry
            top_half = lesion_image[:height//2, :]
            bottom_half = lesion_image[height//2:, :]
            bottom_half_flipped = np.flipud(bottom_half)

            # Resize to match if needed
            min_height = min(top_half.shape[0], bottom_half_flipped.shape[0])
            top_half = top_half[:min_height, :]
            bottom_half_flipped = bottom_half_flipped[:min_height, :]

            vertical_diff = np.mean(np.abs(top_half.astype(float) - bottom_half_flipped.astype(float)))

            # Combine asymmetries
            axis_asymmetry = (horizontal_diff + vertical_diff) / 2.0
            return min(1.0, axis_asymmetry / 255.0)  # Normalize to [0,1]

        except Exception as e:
            logger.warning(f"⚠️ Axis asymmetry calculation failed: {e}")
            return 0.5

    def _interpret_asymmetry_score(self, asymmetry_components) -> Dict:
        """Interpret asymmetry score and provide clinical context"""
        try:
            # Handle different input types
            if isinstance(asymmetry_components, dict):
                # Extract components from dict
                geometric = asymmetry_components.get('geometric', 0.0)
                pca = asymmetry_components.get('pca', 0.0)
                moment = asymmetry_components.get('moment', 0.0)
                fourier = asymmetry_components.get('fourier', 0.0)
                radial = asymmetry_components.get('radial', 0.0)
            else:
                # Handle single numeric value
                geometric = float(asymmetry_components) if asymmetry_components is not None else 0.0
                pca = 0.5
                moment = 0.5
                fourier = 0.5
                radial = 0.5

            # Normalize geometric component (it's often very large)
            geometric_normalized = min(1.0, geometric / 10000000.0)  # Normalize large values

            # Calculate weighted average
            weights = {'geometric': 0.3, 'pca': 0.2, 'moment': 0.2, 'fourier': 0.15, 'radial': 0.15}

            overall_score = (
                geometric_normalized * weights['geometric'] +
                pca * weights['pca'] +
                moment * weights['moment'] +
                fourier * weights['fourier'] +
                radial * weights['radial']
            )

            # Determine risk level
            if overall_score >= 0.7:
                risk_level = 'high'
                interpretation = 'Significant asymmetry detected - concerning for malignancy'
            elif overall_score >= 0.4:
                risk_level = 'medium'
                interpretation = 'Moderate asymmetry - warrants monitoring'
            else:
                risk_level = 'low'
                interpretation = 'Minimal asymmetry - likely benign pattern'

            return {
                'overall_score': overall_score,
                'risk_level': risk_level,
                'interpretation': interpretation,
                'components': asymmetry_components,
                'confidence': 0.8
            }

        except Exception as e:
            logger.warning(f"⚠️ Asymmetry interpretation failed: {e}")
            return {
                'overall_score': 0.5,
                'risk_level': 'unknown',
                'interpretation': 'Asymmetry analysis incomplete',
                'confidence': 0.3
            }

    def _interpret_border_score(self, border_score: float, border_features: Dict) -> Dict:
        """Interpret border irregularity score"""
        try:
            # Determine risk level based on score
            if border_score >= 0.7:
                risk_level = 'high'
                interpretation = 'Highly irregular borders - concerning for malignancy'
            elif border_score >= 0.4:
                risk_level = 'medium'
                interpretation = 'Moderately irregular borders - requires evaluation'
            else:
                risk_level = 'low'
                interpretation = 'Regular borders - likely benign'

            return {
                'score': border_score,
                'risk_level': risk_level,
                'interpretation': interpretation,
                'features': border_features,
                'confidence': 0.8
            }

        except Exception as e:
            logger.warning(f"⚠️ Border interpretation failed: {e}")
            return {
                'score': 0.5,
                'risk_level': 'unknown',
                'interpretation': 'Border analysis incomplete',
                'confidence': 0.3
            }

    def _calculate_rgb_variation(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> Dict:
        """Calculate RGB color variation within lesion"""
        try:
            if lesion_mask is not None:
                # Apply mask to get only lesion pixels
                masked_image = lesion_image.copy()
                masked_image[lesion_mask == 0] = 0
                lesion_pixels = masked_image[lesion_mask > 0]
            else:
                lesion_pixels = lesion_image.reshape(-1, 3)

            if len(lesion_pixels) == 0:
                return {'r_var': 0.0, 'g_var': 0.0, 'b_var': 0.0, 'total_var': 0.0}

            # Calculate variance for each channel
            r_var = np.var(lesion_pixels[:, 0]) / (255.0 ** 2)  # Normalize
            g_var = np.var(lesion_pixels[:, 1]) / (255.0 ** 2)
            b_var = np.var(lesion_pixels[:, 2]) / (255.0 ** 2)

            # Total variation
            total_var = (r_var + g_var + b_var) / 3.0

            return {
                'r_var': float(r_var),
                'g_var': float(g_var),
                'b_var': float(b_var),
                'total_var': float(total_var)
            }

        except Exception as e:
            logger.warning(f"⚠️ RGB variation calculation failed: {e}")
            return {'r_var': 0.0, 'g_var': 0.0, 'b_var': 0.0, 'total_var': 0.0}

    def _calculate_hsv_variation(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> Dict:
        """Calculate HSV color variation within lesion"""
        try:
            # Convert RGB to HSV
            hsv_image = cv2.cvtColor(lesion_image, cv2.COLOR_RGB2HSV)

            if lesion_mask is not None:
                # Apply mask to get only lesion pixels
                masked_hsv = hsv_image.copy()
                masked_hsv[lesion_mask == 0] = 0
                lesion_pixels = masked_hsv[lesion_mask > 0]
            else:
                lesion_pixels = hsv_image.reshape(-1, 3)

            if len(lesion_pixels) == 0:
                return {'h_var': 0.0, 's_var': 0.0, 'v_var': 0.0, 'total_var': 0.0}

            # Calculate variance for each HSV channel
            h_var = np.var(lesion_pixels[:, 0]) / (180.0 ** 2)  # Hue: 0-180
            s_var = np.var(lesion_pixels[:, 1]) / (255.0 ** 2)  # Saturation: 0-255
            v_var = np.var(lesion_pixels[:, 2]) / (255.0 ** 2)  # Value: 0-255

            # Total variation
            total_var = (h_var + s_var + v_var) / 3.0

            return {
                'h_var': float(h_var),
                's_var': float(s_var),
                'v_var': float(v_var),
                'total_var': float(total_var)
            }

        except Exception as e:
            logger.warning(f"⚠️ HSV variation calculation failed: {e}")
            return {'h_var': 0.0, 's_var': 0.0, 'v_var': 0.0, 'total_var': 0.0}

    def _calculate_lab_variation(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> Dict:
        """Calculate LAB color variation within lesion"""
        try:
            # Convert RGB to LAB
            lab_image = cv2.cvtColor(lesion_image, cv2.COLOR_RGB2LAB)

            if lesion_mask is not None:
                # Apply mask to get only lesion pixels
                masked_lab = lab_image.copy()
                masked_lab[lesion_mask == 0] = 0
                lesion_pixels = masked_lab[lesion_mask > 0]
            else:
                lesion_pixels = lab_image.reshape(-1, 3)

            if len(lesion_pixels) == 0:
                return {'l_var': 0.0, 'a_var': 0.0, 'b_var': 0.0, 'total_var': 0.0}

            # Calculate variance for each LAB channel
            l_var = np.var(lesion_pixels[:, 0]) / (100.0 ** 2)  # L: 0-100
            a_var = np.var(lesion_pixels[:, 1]) / (255.0 ** 2)  # A: 0-255 (centered at 128)
            b_var = np.var(lesion_pixels[:, 2]) / (255.0 ** 2)  # B: 0-255 (centered at 128)

            # Total variation
            total_var = (l_var + a_var + b_var) / 3.0

            return {
                'l_var': float(l_var),
                'a_var': float(a_var),
                'b_var': float(b_var),
                'total_var': float(total_var)
            }

        except Exception as e:
            logger.warning(f"⚠️ LAB variation calculation failed: {e}")
            return {'l_var': 0.0, 'a_var': 0.0, 'b_var': 0.0, 'total_var': 0.0}

    def _calculate_color_clustering_variation(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> Dict:
        """Calculate color variation using K-means clustering"""
        try:
            # Ensure image has correct number of channels
            if len(lesion_image.shape) == 2:
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_GRAY2RGB)
            elif lesion_image.shape[2] == 4:
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_RGBA2RGB)

            # Apply mask if provided
            if lesion_mask is not None:
                masked_image = lesion_image.copy()
                masked_image[lesion_mask == 0] = 0
                lesion_pixels = masked_image[lesion_mask > 0]
            else:
                lesion_pixels = lesion_image.reshape(-1, 3)

            if len(lesion_pixels) == 0:
                return {
                    'num_clusters': 0,
                    'cluster_variation': 0.0,
                    'dominant_colors': [],
                    'color_distribution': [],
                    'clustering_score': 0.0
                }

            # Remove black pixels (masked areas)
            lesion_pixels = lesion_pixels[np.sum(lesion_pixels, axis=1) > 0]

            if len(lesion_pixels) < 10:  # Need minimum pixels for clustering
                return {
                    'num_clusters': 1,
                    'cluster_variation': 0.0,
                    'dominant_colors': [lesion_pixels[0].tolist() if len(lesion_pixels) > 0 else [0, 0, 0]],
                    'color_distribution': [1.0],
                    'clustering_score': 0.0
                }

            # Perform K-means clustering with different K values
            best_k = 2
            best_score = float('inf')
            best_labels = None
            best_centers = None

            for k in range(2, min(8, len(lesion_pixels) // 5)):  # Try 2-7 clusters
                try:
                    from sklearn.cluster import KMeans
                    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                    labels = kmeans.fit_predict(lesion_pixels.astype(np.float32))
                    centers = kmeans.cluster_centers_

                    # Calculate within-cluster sum of squares
                    wcss = kmeans.inertia_

                    # Use elbow method to find optimal K
                    if wcss < best_score:
                        best_score = wcss
                        best_k = k
                        best_labels = labels
                        best_centers = centers

                except ImportError:
                    # Fallback: simple color analysis without sklearn
                    unique_colors = np.unique(lesion_pixels.reshape(-1, lesion_pixels.shape[-1]), axis=0)
                    best_k = min(len(unique_colors), 3)
                    best_centers = unique_colors[:best_k]
                    best_labels = np.zeros(len(lesion_pixels))
                    break
                except Exception:
                    continue

            if best_labels is None:
                # Fallback analysis
                return {
                    'num_clusters': 1,
                    'cluster_variation': 0.0,
                    'dominant_colors': [np.mean(lesion_pixels, axis=0).tolist()],
                    'color_distribution': [1.0],
                    'clustering_score': 0.0
                }

            # Calculate cluster statistics
            cluster_counts = np.bincount(best_labels)
            color_distribution = cluster_counts / len(lesion_pixels)

            # Calculate inter-cluster variation
            cluster_variation = 0.0
            if len(best_centers) > 1:
                for i in range(len(best_centers)):
                    for j in range(i + 1, len(best_centers)):
                        color_diff = np.linalg.norm(best_centers[i] - best_centers[j])
                        cluster_variation += color_diff
                cluster_variation /= (len(best_centers) * (len(best_centers) - 1) / 2)
                cluster_variation /= 255.0  # Normalize to 0-1

            # Calculate clustering score (higher = more color variation)
            clustering_score = min(1.0, (best_k - 1) / 6.0 + cluster_variation / 2.0)

            return {
                'num_clusters': int(best_k),
                'cluster_variation': float(cluster_variation),
                'dominant_colors': [center.tolist() for center in best_centers],
                'color_distribution': color_distribution.tolist(),
                'clustering_score': float(clustering_score)
            }

        except Exception as e:
            logger.warning(f"⚠️ Color clustering variation calculation failed: {e}")
            return {
                'num_clusters': 1,
                'cluster_variation': 0.0,
                'dominant_colors': [[128, 128, 128]],
                'color_distribution': [1.0],
                'clustering_score': 0.0
            }

    def _analyze_pigmentation_patterns(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None) -> float:
        """Analyze pigmentation patterns in the lesion"""
        try:
            # Ensure image has correct number of channels
            if len(lesion_image.shape) == 2:
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_GRAY2RGB)
            elif lesion_image.shape[2] == 4:
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_RGBA2RGB)

            # Apply mask if provided
            if lesion_mask is not None:
                masked_image = lesion_image.copy()
                masked_image[lesion_mask == 0] = 0
                lesion_pixels = masked_image[lesion_mask > 0]
            else:
                lesion_pixels = lesion_image.reshape(-1, 3)

            if len(lesion_pixels) == 0:
                return 0.0

            # Remove black pixels (masked areas)
            lesion_pixels = lesion_pixels[np.sum(lesion_pixels, axis=1) > 0]

            if len(lesion_pixels) < 10:
                return 0.0

            # Convert to different color spaces for pigmentation analysis
            # 1. Analyze melanin content (darker regions)
            gray_values = np.mean(lesion_pixels, axis=1)
            melanin_score = 1.0 - (np.mean(gray_values) / 255.0)  # Darker = more melanin

            # 2. Analyze color uniformity
            color_std = np.std(lesion_pixels, axis=0)
            uniformity_score = 1.0 - (np.mean(color_std) / 255.0)  # Less variation = more uniform

            # 3. Analyze brown/black pigmentation (typical melanin colors)
            # Convert to HSV for better color analysis
            hsv_pixels = []
            for pixel in lesion_pixels[:100]:  # Sample for performance
                rgb_pixel = pixel.reshape(1, 1, 3).astype(np.uint8)
                hsv_pixel = cv2.cvtColor(rgb_pixel, cv2.COLOR_RGB2HSV)[0, 0]
                hsv_pixels.append(hsv_pixel)

            if len(hsv_pixels) > 0:
                hsv_pixels = np.array(hsv_pixels)

                # Look for brown/black hues (typical melanin)
                # Brown: H ~10-20, Black: low saturation, low value
                brown_mask = (hsv_pixels[:, 0] >= 5) & (hsv_pixels[:, 0] <= 25)
                dark_mask = hsv_pixels[:, 2] < 100  # Low value (darkness)

                melanin_pixel_ratio = (np.sum(brown_mask) + np.sum(dark_mask)) / len(hsv_pixels)
            else:
                melanin_pixel_ratio = 0.0

            # 4. Analyze pigmentation distribution patterns
            # Check for irregular pigmentation (patchy vs uniform)
            if lesion_mask is not None and lesion_mask.shape[0] > 20 and lesion_mask.shape[1] > 20:
                # Divide lesion into quadrants and analyze pigmentation variation
                h, w = lesion_mask.shape
                quadrants = [
                    lesion_image[0:h//2, 0:w//2],
                    lesion_image[0:h//2, w//2:w],
                    lesion_image[h//2:h, 0:w//2],
                    lesion_image[h//2:h, w//2:w]
                ]

                quadrant_means = []
                for quad in quadrants:
                    if lesion_mask is not None:
                        quad_mask = lesion_mask[quad.shape[0]*quadrants.index(quad)//2:
                                              quad.shape[0]*(quadrants.index(quad)//2+1),
                                              quad.shape[1]*(quadrants.index(quad)%2):
                                              quad.shape[1]*(quadrants.index(quad)%2+1)]
                        if np.sum(quad_mask) > 0:
                            quad_pixels = quad[quad_mask > 0]
                            if len(quad_pixels) > 0:
                                quadrant_means.append(np.mean(quad_pixels))

                if len(quadrant_means) > 1:
                    pigmentation_variation = np.std(quadrant_means) / 255.0
                else:
                    pigmentation_variation = 0.0
            else:
                pigmentation_variation = 0.0

            # 5. Calculate overall pigmentation pattern score
            # Higher score indicates more concerning pigmentation patterns
            pigmentation_score = (
                melanin_score * 0.3 +           # Amount of melanin
                (1.0 - uniformity_score) * 0.3 + # Non-uniformity
                melanin_pixel_ratio * 0.2 +      # Melanin pixel ratio
                pigmentation_variation * 0.2     # Spatial variation
            )

            # Normalize to 0-1 range
            pigmentation_score = min(1.0, max(0.0, pigmentation_score))

            return float(pigmentation_score)

        except Exception as e:
            logger.warning(f"⚠️ Pigmentation pattern analysis failed: {e}")
            return 0.0

    def _calculate_max_diameter(self, lesion_image: np.ndarray, lesion_mask: Optional[np.ndarray] = None, reference_size_mm: Optional[float] = None) -> Dict:
        """Calculate maximum diameter of lesion"""
        try:
            # Ensure image has correct number of channels
            if len(lesion_image.shape) == 2:
                # Convert grayscale to RGB
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_GRAY2RGB)
            elif lesion_image.shape[2] == 4:
                # Convert RGBA to RGB
                lesion_image = cv2.cvtColor(lesion_image, cv2.COLOR_RGBA2RGB)
            elif lesion_image.shape[2] == 2:
                # Handle 2-channel images by duplicating one channel
                lesion_image = np.dstack([lesion_image[:,:,0], lesion_image[:,:,1], lesion_image[:,:,0]])

            if lesion_mask is None:
                # Create simple mask from image
                if len(lesion_image.shape) == 3:
                    gray = cv2.cvtColor(lesion_image, cv2.COLOR_RGB2GRAY)
                else:
                    gray = lesion_image
                _, lesion_mask = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Find contours
            contours, _ = cv2.findContours(lesion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return {'diameter_pixels': 0.0, 'diameter_mm': 0.0, 'area_pixels': 0.0}

            # Get largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Calculate bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            diameter_pixels = max(w, h)

            # Calculate area
            area_pixels = cv2.contourArea(largest_contour)

            # Convert to mm if reference size provided
            diameter_mm = 0.0
            if reference_size_mm and diameter_pixels > 0:
                # Simple conversion assuming reference_size_mm is for the whole image
                image_diagonal = np.sqrt(lesion_image.shape[0]**2 + lesion_image.shape[1]**2)
                pixels_per_mm = image_diagonal / reference_size_mm if reference_size_mm > 0 else 1.0
                diameter_mm = diameter_pixels / pixels_per_mm

            return {
                'diameter_pixels': float(diameter_pixels),
                'diameter_mm': float(diameter_mm),
                'area_pixels': float(area_pixels)
            }

        except Exception as e:
            logger.warning(f"⚠️ Diameter calculation failed: {e}")
            return {'diameter_pixels': 0.0, 'diameter_mm': 0.0, 'area_pixels': 0.0}

    def _interpret_diameter_score(self, diameter_info: Dict) -> Dict:
        """Interpret diameter measurements and provide clinical context"""
        try:
            diameter_pixels = diameter_info.get('diameter_pixels', 0.0)
            diameter_mm = diameter_info.get('diameter_mm', 0.0)
            area_pixels = diameter_info.get('area_pixels', 0.0)

            # Determine score based on diameter
            # 6mm rule for melanoma (diameter > 6mm is concerning)
            if diameter_mm > 0:
                # Use actual mm measurement
                if diameter_mm >= 6.0:
                    score = 0.8
                    risk_level = 'high'
                    interpretation = f'Large diameter ({diameter_mm:.1f}mm) - exceeds 6mm melanoma threshold'
                elif diameter_mm >= 4.0:
                    score = 0.6
                    risk_level = 'moderate'
                    interpretation = f'Moderate diameter ({diameter_mm:.1f}mm) - approaching concern threshold'
                elif diameter_mm >= 2.0:
                    score = 0.4
                    risk_level = 'low'
                    interpretation = f'Small diameter ({diameter_mm:.1f}mm) - within normal range'
                else:
                    score = 0.2
                    risk_level = 'very_low'
                    interpretation = f'Very small diameter ({diameter_mm:.1f}mm) - minimal concern'
            else:
                # Use pixel-based estimation (rough approximation)
                if diameter_pixels >= 200:
                    score = 0.7
                    risk_level = 'high'
                    interpretation = f'Large lesion ({diameter_pixels} pixels) - professional evaluation recommended'
                elif diameter_pixels >= 100:
                    score = 0.5
                    risk_level = 'moderate'
                    interpretation = f'Moderate lesion size ({diameter_pixels} pixels)'
                elif diameter_pixels >= 50:
                    score = 0.3
                    risk_level = 'low'
                    interpretation = f'Small lesion ({diameter_pixels} pixels)'
                else:
                    score = 0.1
                    risk_level = 'very_low'
                    interpretation = f'Very small lesion ({diameter_pixels} pixels)'

            # Clinical recommendations based on size
            recommendations = []
            if risk_level in ['high']:
                recommendations.extend([
                    'Immediate dermatologist consultation recommended',
                    'Document lesion size and monitor for growth',
                    'Consider dermoscopy or biopsy evaluation'
                ])
            elif risk_level == 'moderate':
                recommendations.extend([
                    'Monitor lesion size regularly',
                    'Schedule dermatologist appointment within 4-6 weeks',
                    'Take photographs for size comparison'
                ])
            else:
                recommendations.extend([
                    'Continue routine monitoring',
                    'Annual dermatological check-up',
                    'Watch for any size changes'
                ])

            return {
                'score': score,
                'risk_level': risk_level,
                'interpretation': interpretation,
                'recommendations': recommendations,
                'diameter_mm': diameter_mm,
                'diameter_pixels': diameter_pixels,
                'area_pixels': area_pixels,
                'confidence': 0.8
            }

        except Exception as e:
            logger.warning(f"⚠️ Diameter interpretation failed: {e}")
            return {
                'score': 0.5,
                'risk_level': 'unknown',
                'interpretation': 'Diameter analysis incomplete',
                'recommendations': ['Professional evaluation recommended'],
                'confidence': 0.3
            }

    def _calculate_overall_abcde_score(self, asymmetry_results: Dict, border_results: Dict,
                                     color_results: Dict, diameter_results: Dict, evolution_results: Dict) -> float:
        """Calculate overall ABCDE score"""
        try:
            # Extract scores from each component
            asymmetry_score = asymmetry_results.get('overall_score', 0.0) if isinstance(asymmetry_results, dict) else 0.0
            border_score = border_results.get('score', 0.0) if isinstance(border_results, dict) else 0.0
            color_score = color_results.get('overall_score', 0.0) if isinstance(color_results, dict) else 0.0
            diameter_score = diameter_results.get('score', 0.0) if isinstance(diameter_results, dict) else 0.0
            evolution_score = evolution_results.get('score', 0.0) if isinstance(evolution_results, dict) else 0.0

            # Weighted average (melanoma risk factors)
            weights = {
                'asymmetry': 0.25,
                'border': 0.25,
                'color': 0.20,
                'diameter': 0.15,
                'evolution': 0.15
            }

            overall_score = (
                asymmetry_score * weights['asymmetry'] +
                border_score * weights['border'] +
                color_score * weights['color'] +
                diameter_score * weights['diameter'] +
                evolution_score * weights['evolution']
            )

            return min(1.0, max(0.0, overall_score))

        except Exception as e:
            logger.warning(f"⚠️ Overall ABCDE score calculation failed: {e}")
            return 0.5

    def _assess_melanoma_risk(self, overall_score: float) -> Dict:
        """Assess melanoma risk based on overall ABCDE score"""
        try:
            # Risk level determination
            if overall_score >= 0.8:
                risk_level = 'very_high'
                urgency = 'immediate'
                description = 'Very high melanoma risk - immediate dermatologist consultation required'
            elif overall_score >= 0.6:
                risk_level = 'high'
                urgency = 'urgent'
                description = 'High melanoma risk - urgent dermatologist consultation within 1-2 weeks'
            elif overall_score >= 0.4:
                risk_level = 'moderate'
                urgency = 'soon'
                description = 'Moderate melanoma risk - dermatologist consultation within 4-6 weeks'
            elif overall_score >= 0.2:
                risk_level = 'low'
                urgency = 'routine'
                description = 'Low melanoma risk - routine monitoring recommended'
            else:
                risk_level = 'very_low'
                urgency = 'routine'
                description = 'Very low melanoma risk - continue regular skin checks'

            # Clinical recommendations
            recommendations = []
            if risk_level in ['very_high', 'high']:
                recommendations.extend([
                    'Immediate professional dermatological evaluation',
                    'Document lesion with photographs',
                    'Monitor for any changes in size, color, or symptoms',
                    'Avoid sun exposure to the lesion'
                ])
            elif risk_level == 'moderate':
                recommendations.extend([
                    'Schedule dermatologist appointment within 4-6 weeks',
                    'Monitor lesion for changes',
                    'Take photographs for comparison',
                    'Use sun protection'
                ])
            else:
                recommendations.extend([
                    'Continue regular skin self-examinations',
                    'Annual dermatological check-up',
                    'Use broad-spectrum sunscreen',
                    'Monitor for any new or changing lesions'
                ])

            return {
                'risk_level': risk_level,
                'urgency': urgency,
                'description': description,
                'recommendations': recommendations,
                'overall_score': overall_score,
                'confidence': 0.85
            }

        except Exception as e:
            logger.warning(f"⚠️ Melanoma risk assessment failed: {e}")
            return {
                'risk_level': 'unknown',
                'urgency': 'consult_professional',
                'description': 'Risk assessment incomplete - professional evaluation recommended',
                'recommendations': ['Consult dermatologist for proper evaluation'],
                'overall_score': overall_score,
                'confidence': 0.3
            }

    def _calculate_analysis_confidence(self, abcde_results: Dict) -> Dict:
        """Calculate overall confidence metrics for ABCDE analysis"""
        try:
            confidence_scores = []
            quality_factors = []

            # Extract confidence from each component
            asymmetry_conf = abcde_results.get('asymmetry', {}).get('confidence', 0.0)
            border_conf = abcde_results.get('border', {}).get('confidence', 0.0)
            color_conf = abcde_results.get('color', {}).get('confidence', 0.0)
            diameter_conf = abcde_results.get('diameter', {}).get('confidence', 0.0)
            evolution_conf = abcde_results.get('evolution', {}).get('confidence', 0.0)

            confidence_scores.extend([asymmetry_conf, border_conf, color_conf, diameter_conf, evolution_conf])

            # Calculate overall confidence
            valid_confidences = [c for c in confidence_scores if c > 0]
            if valid_confidences:
                overall_confidence = np.mean(valid_confidences)
            else:
                overall_confidence = 0.5  # Default moderate confidence

            # Assess quality factors
            # Image quality indicators
            image_quality = 0.8  # Default good quality

            # Analysis completeness
            components_analyzed = sum([
                1 if abcde_results.get('asymmetry', {}).get('score', 0) > 0 else 0,
                1 if abcde_results.get('border', {}).get('score', 0) > 0 else 0,
                1 if abcde_results.get('color', {}).get('overall_score', 0) > 0 else 0,
                1 if abcde_results.get('diameter', {}).get('score', 0) > 0 else 0,
                1 if abcde_results.get('evolution', {}).get('score', 0) > 0 else 0
            ])

            completeness_score = components_analyzed / 5.0

            # Consistency check
            scores = [
                abcde_results.get('asymmetry', {}).get('score', 0),
                abcde_results.get('border', {}).get('score', 0),
                abcde_results.get('color', {}).get('overall_score', 0),
                abcde_results.get('diameter', {}).get('score', 0),
                abcde_results.get('evolution', {}).get('score', 0)
            ]

            valid_scores = [s for s in scores if s > 0]
            if len(valid_scores) > 1:
                score_variance = np.var(valid_scores)
                consistency_score = max(0.0, 1.0 - score_variance)  # Lower variance = higher consistency
            else:
                consistency_score = 0.7  # Default moderate consistency

            # Calculate quality metrics
            analysis_quality_score = (image_quality + completeness_score + consistency_score) / 3.0

            # Determine quality level
            if analysis_quality_score >= 0.8:
                quality_level = 'high'
            elif analysis_quality_score >= 0.6:
                quality_level = 'medium'
            elif analysis_quality_score >= 0.4:
                quality_level = 'low'
            else:
                quality_level = 'poor'

            # Calculate reliability indicators
            reliability_factors = {
                'image_quality': image_quality,
                'analysis_completeness': completeness_score,
                'score_consistency': consistency_score,
                'component_agreement': min(1.0, len(valid_confidences) / 5.0)
            }

            # Overall reliability
            overall_reliability = np.mean(list(reliability_factors.values()))

            return {
                'overall_confidence': float(overall_confidence),
                'analysis_quality': quality_level,
                'analysis_quality_score': float(analysis_quality_score),
                'reliability_score': float(overall_reliability),
                'components_analyzed': int(components_analyzed),
                'completeness_percentage': float(completeness_score * 100),
                'confidence_breakdown': {
                    'asymmetry': float(asymmetry_conf),
                    'border': float(border_conf),
                    'color': float(color_conf),
                    'diameter': float(diameter_conf),
                    'evolution': float(evolution_conf)
                },
                'reliability_factors': {k: float(v) for k, v in reliability_factors.items()},
                'quality_indicators': {
                    'image_quality': 'good' if image_quality >= 0.7 else 'fair' if image_quality >= 0.5 else 'poor',
                    'analysis_complete': completeness_score >= 0.8,
                    'results_consistent': consistency_score >= 0.7,
                    'high_confidence': overall_confidence >= 0.7
                }
            }

        except Exception as e:
            logger.warning(f"⚠️ Analysis confidence calculation failed: {e}")
            return {
                'overall_confidence': 0.5,
                'analysis_quality': 'unknown',
                'analysis_quality_score': 0.5,
                'reliability_score': 0.5,
                'components_analyzed': 0,
                'completeness_percentage': 0.0,
                'confidence_breakdown': {
                    'asymmetry': 0.0,
                    'border': 0.0,
                    'color': 0.0,
                    'diameter': 0.0,
                    'evolution': 0.0
                },
                'reliability_factors': {
                    'image_quality': 0.5,
                    'analysis_completeness': 0.0,
                    'score_consistency': 0.5,
                    'component_agreement': 0.0
                },
                'quality_indicators': {
                    'image_quality': 'unknown',
                    'analysis_complete': False,
                    'results_consistent': False,
                    'high_confidence': False
                }
            }

    def _generate_clinical_recommendations(self, abcde_results: Dict) -> List[str]:
        """Generate comprehensive clinical recommendations based on ABCDE analysis"""
        try:
            recommendations = []

            # Extract overall score and risk assessment
            overall_score = abcde_results.get('overall_score', 0.0)
            melanoma_risk = abcde_results.get('melanoma_risk_assessment', {})
            risk_level = melanoma_risk.get('risk_level', 'unknown')

            # Primary recommendations based on overall risk
            if risk_level == 'very_high' or overall_score >= 0.8:
                recommendations.extend([
                    "🔴 URGENT: Immediate dermatologist consultation required",
                    "📞 Schedule appointment within 24-48 hours",
                    "📸 Document lesion with high-quality photographs",
                    "🚫 Avoid sun exposure to the affected area",
                    "📋 Prepare list of any recent changes in the lesion"
                ])
            elif risk_level == 'high' or overall_score >= 0.6:
                recommendations.extend([
                    "🟠 HIGH PRIORITY: Dermatologist consultation within 1-2 weeks",
                    "📸 Take photographs for monitoring changes",
                    "📝 Document any symptoms (itching, bleeding, pain)",
                    "🧴 Use broad-spectrum SPF 30+ sunscreen",
                    "👀 Monitor daily for any changes"
                ])
            elif risk_level == 'moderate' or overall_score >= 0.4:
                recommendations.extend([
                    "🟡 MODERATE: Schedule dermatologist appointment within 4-6 weeks",
                    "📸 Take monthly photographs for comparison",
                    "📏 Measure lesion size regularly",
                    "🧴 Apply sunscreen daily to prevent further damage",
                    "📅 Set reminders for regular self-examinations"
                ])
            else:
                recommendations.extend([
                    "🟢 LOW RISK: Continue routine monitoring",
                    "📅 Annual dermatological check-up recommended",
                    "👀 Perform monthly skin self-examinations",
                    "🧴 Use daily sun protection",
                    "📚 Learn ABCDE warning signs"
                ])

            # Component-specific recommendations
            asymmetry_score = abcde_results.get('asymmetry', {}).get('overall_score', 0.0)
            if asymmetry_score > 0.6:
                recommendations.append("⚠️ Significant asymmetry detected - key melanoma warning sign")

            border_score = abcde_results.get('border', {}).get('score', 0.0)
            if border_score > 0.6:
                recommendations.append("⚠️ Irregular borders noted - requires professional evaluation")

            color_score = abcde_results.get('color', {}).get('overall_score', 0.0)
            if color_score > 0.6:
                recommendations.append("⚠️ Significant color variation - concerning feature")

            diameter_score = abcde_results.get('diameter', {}).get('score', 0.0)
            if diameter_score > 0.6:
                recommendations.append("⚠️ Large diameter (>6mm) - exceeds melanoma threshold")

            # General skin health recommendations
            recommendations.extend([
                "🌞 Avoid peak sun hours (10 AM - 4 PM)",
                "👕 Wear protective clothing when outdoors",
                "🕶️ Use UV-blocking sunglasses",
                "🏠 Seek shade when possible"
            ])

            # Limit to most important recommendations
            return recommendations[:8]

        except Exception as e:
            logger.warning(f"⚠️ Clinical recommendations generation failed: {e}")
            return [
                "Professional dermatological evaluation recommended",
                "Monitor lesion for any changes",
                "Use sun protection measures",
                "Perform regular skin self-examinations"
            ]
