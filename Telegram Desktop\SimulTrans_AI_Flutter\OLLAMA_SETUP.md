# 🤖 Configuração do Ollama para SimulTrans AI

Este guia explica como configurar o Ollama para usar o SimulTrans AI completamente offline com o modelo Gemma.

## 📋 Pré-requisitos

- **Sistema Operacional**: Windows 10/11, macOS 10.15+, ou Linux
- **RAM**: <PERSON><PERSON>imo 8GB (recomendado 16GB+)
- **Espaço em Disco**: ~4GB para o modelo gemma3n:e2b
- **Conexão com Internet**: Apenas para download inicial do modelo

## 🚀 Instalação do Ollama

### Windows
1. Baixe o instalador: https://ollama.ai/download/windows
2. Execute o arquivo `.exe` baixado
3. <PERSON>ga as instruções do instalador
4. Abra o PowerShell ou Command Prompt

### macOS
1. Baixe o instalador: https://ollama.ai/download/mac
2. Abra o arquivo `.dmg` baixado
3. Arraste o Ollama para a pasta Applications
4. Abra o Terminal

### Linux
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

## ⚙️ Configuração

### 1. Iniciar o Ollama
```bash
ollama serve
```

**Importante**: Mantenha este comando rodando em um terminal separado. O Ollama precisa estar ativo para o app funcionar.

### 2. Baixar o Modelo Gemma
Em outro terminal, execute:
```bash
ollama pull gemma3n:e2b
```

**Nota**: O download pode demorar alguns minutos dependendo da sua conexão.

### 3. Verificar Instalação
```bash
ollama list
```

Você deve ver algo como:
```
NAME            ID              SIZE    MODIFIED
gemma3n:e2b     abc123def456    2.0GB   2 minutes ago
```

### 4. Testar o Modelo
```bash
ollama run gemma3n:e2b "Translate 'Hello world' to Portuguese"
```

Se funcionar, você verá uma resposta como: "Olá mundo"

## 🔧 Configuração do App

### 1. Arquivo .env
Certifique-se de que o arquivo `.env` na raiz do projeto contém:
```env
# Ollama Configuration
OLLAMA_MODEL_NAME=gemma3n:e2b
OLLAMA_HOST=http://localhost:11434
OLLAMA_TIMEOUT_SECONDS=600
OLLAMA_CONNECTION_TIMEOUT=30
OLLAMA_IMAGE_TIMEOUT=900

# Performance Settings
TEMPERATURE=0.7
TOP_P=0.9
MAX_NEW_TOKENS=512
```

### 2. Executar o App
```bash
flutter run
```

## 🧪 Testando a Configuração

### Teste de Texto
1. Abra o app
2. Digite "Hello world" no campo de texto
3. Selecione idioma de origem: Inglês
4. Selecione idioma de destino: Português
5. Clique em "Traduzir"
6. Deve aparecer: "Olá mundo"

### Teste de Imagem
1. Vá para a aba de tradução de imagem
2. Selecione uma imagem com texto
3. Escolha o idioma de destino
4. Clique em "Traduzir Imagem"
5. O app deve extrair e traduzir o texto da imagem

## 🔍 Solução de Problemas

### Erro: "Ollama server not responding"
**Solução**: 
1. Verifique se o Ollama está rodando: `ollama serve`
2. Teste a conexão: `curl http://localhost:11434/api/tags`

### Erro: "Model gemma3n:e2b not found"
**Solução**:
1. Baixe o modelo: `ollama pull gemma3n:e2b`
2. Verifique se foi instalado: `ollama list`

### Tradução muito lenta
**Soluções**:
1. **Aumentar RAM**: Feche outros programas
2. **Usar modelo menor**: `ollama pull gemma2:2b`
3. **Ajustar timeout**: Aumente `OLLAMA_TIMEOUT_SECONDS` no .env

### Erro de timeout
**Solução**:
1. Aumente os timeouts no arquivo `.env`:
```env
OLLAMA_TIMEOUT_SECONDS=1200
OLLAMA_CONNECTION_TIMEOUT=60
OLLAMA_IMAGE_TIMEOUT=1800
```

## 📊 Modelos Disponíveis

| Modelo | Tamanho | RAM Recomendada | Qualidade |
|--------|---------|-----------------|-----------|
| gemma3n:e2b | ~2GB | 8GB | ⭐⭐⭐⭐⭐ |
| gemma2:2b | ~1.4GB | 4GB | ⭐⭐⭐⭐ |
| gemma2:9b | ~5.5GB | 16GB | ⭐⭐⭐⭐⭐ |
| gemma2:27b | ~15GB | 32GB | ⭐⭐⭐⭐⭐ |

### Trocar de Modelo
1. Baixe o modelo desejado:
```bash
ollama pull gemma2:2b
```

2. Atualize o arquivo `.env`:
```env
OLLAMA_MODEL_NAME=gemma2:2b
```

3. Reinicie o app

## 🚀 Otimizações de Performance

### Para Hardware Limitado
```env
# Use modelo menor
OLLAMA_MODEL_NAME=gemma2:2b

# Reduza tokens
MAX_NEW_TOKENS=256

# Aumente timeouts
OLLAMA_TIMEOUT_SECONDS=1200
```

### Para Hardware Potente
```env
# Use modelo maior
OLLAMA_MODEL_NAME=gemma2:9b

# Mais tokens para respostas detalhadas
MAX_NEW_TOKENS=1024

# Timeouts menores
OLLAMA_TIMEOUT_SECONDS=300
```

## 🔒 Vantagens do Ollama

✅ **Totalmente Offline**: Funciona sem internet após setup inicial
✅ **Privacidade Total**: Seus dados nunca saem do seu computador
✅ **Sem Limites**: Use quantas vezes quiser, sem cotas
✅ **Rápido**: Processamento local, sem latência de rede
✅ **Gratuito**: Sem custos de API ou assinaturas

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs**: O app mostra mensagens detalhadas no console
2. **Teste o Ollama**: `ollama run gemma3n:e2b "test"`
3. **Reinicie tudo**: Pare o Ollama, reinicie, e tente novamente
4. **Verifique recursos**: Monitor de sistema para RAM/CPU

## 🔄 Atualizações

Para atualizar o modelo:
```bash
ollama pull gemma3n:e2b
```

Para atualizar o Ollama:
- **Windows/macOS**: Baixe nova versão do site
- **Linux**: `curl -fsSL https://ollama.ai/install.sh | sh`

---

**🎉 Pronto!** Agora você tem um tradutor multimodal completamente offline e privado!
