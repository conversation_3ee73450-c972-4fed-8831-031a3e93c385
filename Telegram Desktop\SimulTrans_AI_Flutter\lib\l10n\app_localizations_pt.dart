// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appTitle => 'SimulTrans AI';

  @override
  String get translate => 'Traduzir';

  @override
  String get settings => 'Configurações';

  @override
  String get history => 'Histórico';

  @override
  String get community => 'Comunidade';
}
