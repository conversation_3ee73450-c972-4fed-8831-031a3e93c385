#!/usr/bin/env python3
"""
Agno Teams Multi-Agent System for DermatoGemma Multi-Detection System v2.0
Autonomous agents specialized in dermatological analysis
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import base64
import io

try:
    from agno.agent import Agent
    from agno.team import Team
    from agno.models.openai import OpenAIChat
    from agno.tools.duckduckgo import DuckDuckGoTools
    from pydantic import BaseModel, Field
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    print("⚠️ Agno not available. Install with: pip install agno")
    # Create dummy BaseModel and Field for when pydantic is not available
    class BaseModel:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    def Field(**kwargs):
        return None

# Set up logging
logger = logging.getLogger(__name__)

class DermatologyAnalysisResult(BaseModel):
    """Structured output for dermatology analysis"""
    primary_diagnosis: str = Field(description="Most likely diagnosis")
    confidence_score: float = Field(description="Confidence level (0-1)")
    differential_diagnoses: List[str] = Field(description="Alternative diagnoses")
    key_features: List[str] = Field(description="Key visual features identified")
    risk_assessment: str = Field(description="Risk level: low, medium, high, critical")
    recommendations: List[str] = Field(description="Clinical recommendations")
    urgency_level: str = Field(description="Urgency: routine, urgent, emergency")
    follow_up_timeline: str = Field(description="Recommended follow-up timeline")
    additional_tests: List[str] = Field(description="Suggested additional tests")
    patient_education: List[str] = Field(description="Patient education points")

class ABCDEAnalysisResult(BaseModel):
    """ABCDE analysis structured output"""
    asymmetry_score: float = Field(description="Asymmetry score (0-1)")
    border_score: float = Field(description="Border irregularity score (0-1)")
    color_score: float = Field(description="Color variation score (0-1)")
    diameter_score: float = Field(description="Diameter score (0-1)")
    evolution_score: float = Field(description="Evolution score (0-1)")
    overall_abcde_score: float = Field(description="Overall ABCDE score (0-1)")
    melanoma_risk: str = Field(description="Melanoma risk: low, moderate, high")
    abcde_interpretation: str = Field(description="Clinical interpretation")

class ImageDataExtractionResult(BaseModel):
    """Image data extraction structured output"""
    visual_summary: str = Field(description="Comprehensive visual description")
    color_analysis: Dict[str, Any] = Field(description="Detailed color analysis")
    texture_analysis: Dict[str, Any] = Field(description="Surface texture analysis")
    morphology_analysis: Dict[str, Any] = Field(description="Shape and structure analysis")
    pattern_recognition: List[str] = Field(description="Identified patterns")
    measurement_estimates: Dict[str, float] = Field(description="Size and dimension estimates")
    clinical_features: List[str] = Field(description="Clinically relevant features")

class AgnoDermaTeam:
    """Agno Teams-based multi-agent system for dermatological analysis"""
    
    def __init__(self, ollama_url: str = "http://localhost:11434", model_name: str = "gemma3n:e4b"):
        """Initialize the Agno dermatology team"""
        self.ollama_url = ollama_url
        self.model_name = model_name
        self.initialized = False
        
        if not AGNO_AVAILABLE:
            raise ImportError("Agno is required. Install with: pip install agno")
        
        # Initialize team members
        self._create_specialized_agents()
        self._create_dermatology_team()
        
        logger.info("🤖 Agno Dermatology Team initialized")
    
    def _create_specialized_agents(self):
        """Create specialized dermatological agents"""
        
        # 1. ABCDE Analysis Specialist
        self.abcde_agent = Agent(
            name="ABCDE Analysis Specialist",
            role="Expert in ABCDE melanoma screening criteria analysis",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a dermatology specialist focused on ABCDE analysis for melanoma screening.",
                "Analyze skin lesions using the ABCDE criteria: Asymmetry, Border, Color, Diameter, Evolution.",
                "Provide detailed scores for each criterion and overall melanoma risk assessment.",
                "Use clinical terminology and evidence-based guidelines.",
                "Always include confidence levels and clinical reasoning."
            ],
            response_model=ABCDEAnalysisResult,
            markdown=True,
            show_tool_calls=True
        )
        
        # 2. Image Data Extraction Specialist
        self.image_extraction_agent = Agent(
            name="Image Data Extraction Specialist",
            role="Expert in extracting and interpreting visual data from dermatological images",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a medical imaging specialist focused on dermatological image analysis.",
                "Extract comprehensive visual data: colors, textures, patterns, morphology.",
                "Provide detailed measurements and clinical feature identification.",
                "Use precise medical terminology for visual descriptions.",
                "Focus on clinically relevant visual characteristics."
            ],
            response_model=ImageDataExtractionResult,
            markdown=True,
            show_tool_calls=True
        )
        
        # 3. Differential Diagnosis Specialist
        self.diagnosis_agent = Agent(
            name="Differential Diagnosis Specialist",
            role="Expert in dermatological differential diagnosis and clinical decision making",
            model=OpenAIChat(id="gpt-4o"),
            tools=[DuckDuckGoTools()],
            instructions=[
                "You are a senior dermatologist specializing in differential diagnosis.",
                "Analyze all available data to provide comprehensive diagnostic assessments.",
                "Consider multiple differential diagnoses with evidence-based reasoning.",
                "Provide risk stratification and clinical recommendations.",
                "Use current dermatological guidelines and best practices.",
                "Search for recent medical literature when needed for rare conditions."
            ],
            response_model=DermatologyAnalysisResult,
            markdown=True,
            show_tool_calls=True,
            add_datetime_to_instructions=True
        )
        
        # 4. Clinical Risk Assessment Specialist
        self.risk_agent = Agent(
            name="Clinical Risk Assessment Specialist",
            role="Expert in medical risk assessment and clinical decision support",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a clinical risk assessment specialist in dermatology.",
                "Evaluate urgency levels, follow-up timelines, and additional testing needs.",
                "Provide patient education recommendations and safety guidelines.",
                "Consider patient safety as the highest priority.",
                "Use evidence-based risk stratification protocols."
            ],
            markdown=True,
            show_tool_calls=True
        )
        
        # 5. Medical Research Specialist
        self.research_agent = Agent(
            name="Medical Research Specialist",
            role="Expert in medical literature research and evidence-based medicine",
            model=OpenAIChat(id="gpt-4o"),
            tools=[DuckDuckGoTools()],
            instructions=[
                "You are a medical research specialist focused on dermatology.",
                "Search for current medical literature and clinical guidelines.",
                "Provide evidence-based recommendations and recent research findings.",
                "Validate diagnoses against current medical knowledge.",
                "Focus on peer-reviewed sources and clinical guidelines."
            ],
            markdown=True,
            show_tool_calls=True,
            add_datetime_to_instructions=True
        )
    
    def _create_dermatology_team(self):
        """Create the coordinated dermatology team"""
        
        self.dermatology_team = Team(
            name="DermatoGemma Multi-Agent Analysis Team",
            mode="coordinate",  # Coordinate mode for comprehensive analysis
            model=OpenAIChat(id="gpt-4o"),
            members=[
                self.abcde_agent,
                self.image_extraction_agent,
                self.diagnosis_agent,
                self.risk_agent,
                self.research_agent
            ],
            instructions=[
                "You are the lead coordinator of a specialized dermatology analysis team.",
                "Coordinate with team members to provide comprehensive skin lesion analysis.",
                "First, have the Image Data Extraction Specialist analyze the visual features.",
                "Then, have the ABCDE Analysis Specialist perform melanoma screening.",
                "Next, have the Differential Diagnosis Specialist provide diagnostic assessment.",
                "Have the Clinical Risk Assessment Specialist evaluate urgency and recommendations.",
                "Finally, have the Medical Research Specialist validate findings with current literature.",
                "Synthesize all findings into a comprehensive clinical report.",
                "Prioritize patient safety and evidence-based medicine.",
                "Provide clear, actionable recommendations for healthcare providers."
            ],
            description="Autonomous multi-agent system for comprehensive dermatological analysis",
            show_tool_calls=True,
            show_members_responses=True,
            markdown=True,
            add_history_to_messages=True,
            num_history_runs=3
        )
        
        self.initialized = True
        logger.info("✅ Dermatology team created successfully")
    
    def analyze_lesion(self, image_data: str, patient_context: Optional[Dict] = None) -> Dict:
        """
        Perform comprehensive multi-agent analysis of skin lesion
        
        Args:
            image_data: Base64 encoded image data
            patient_context: Optional patient information
            
        Returns:
            Comprehensive analysis results from all agents
        """
        if not self.initialized:
            raise RuntimeError("Agno team not initialized")
        
        try:
            logger.info("🤖 Starting multi-agent dermatological analysis...")
            
            # Prepare analysis prompt with image and context
            analysis_prompt = self._create_analysis_prompt(image_data, patient_context)
            
            # Run the coordinated team analysis
            team_response = self.dermatology_team.run(analysis_prompt)
            
            # Extract and structure the results
            results = {
                'timestamp': datetime.now().isoformat(),
                'team_analysis': team_response.content,
                'agent_responses': self._extract_agent_responses(),
                'success': True,
                'analysis_type': 'multi_agent_autonomous'
            }
            
            logger.info("✅ Multi-agent analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"❌ Multi-agent analysis failed: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'success': False,
                'analysis_type': 'multi_agent_error'
            }
    
    def _create_analysis_prompt(self, image_data: str, patient_context: Optional[Dict]) -> str:
        """Create comprehensive analysis prompt for the team"""
        
        prompt = f"""
COMPREHENSIVE DERMATOLOGICAL ANALYSIS REQUEST

IMAGE DATA: [Base64 image provided - analyze the skin lesion in detail]

PATIENT CONTEXT:
{self._format_patient_context(patient_context)}

ANALYSIS REQUIREMENTS:
1. Perform detailed visual analysis of the skin lesion
2. Conduct ABCDE melanoma screening assessment
3. Provide differential diagnosis with evidence-based reasoning
4. Assess clinical risk and urgency level
5. Validate findings with current medical literature
6. Synthesize comprehensive clinical recommendations

TEAM COORDINATION:
- Each specialist should provide their expert analysis
- Coordinate findings for comprehensive assessment
- Prioritize patient safety and clinical accuracy
- Provide actionable recommendations for healthcare providers

Please proceed with coordinated multi-agent analysis.
"""
        
        return prompt
    
    def _format_patient_context(self, context: Optional[Dict]) -> str:
        """Format patient context for analysis"""
        if not context:
            return "No additional patient context provided"
        
        formatted = []
        for key, value in context.items():
            formatted.append(f"- {key.replace('_', ' ').title()}: {value}")
        
        return "\n".join(formatted) if formatted else "No additional patient context provided"
    
    def _extract_agent_responses(self) -> Dict:
        """Extract individual agent responses from team history"""
        try:
            # This would extract individual agent responses
            # Implementation depends on Agno's internal structure
            return {
                'abcde_analysis': "ABCDE analysis completed",
                'image_extraction': "Image data extraction completed",
                'differential_diagnosis': "Differential diagnosis completed",
                'risk_assessment': "Risk assessment completed",
                'research_validation': "Research validation completed"
            }
        except Exception as e:
            logger.warning(f"Could not extract individual agent responses: {e}")
            return {}
    
    def get_team_status(self) -> Dict:
        """Get current team status and capabilities"""
        return {
            'initialized': self.initialized,
            'team_name': self.dermatology_team.name if self.initialized else None,
            'agent_count': len(self.dermatology_team.members) if self.initialized else 0,
            'agents': [
                {
                    'name': agent.name,
                    'role': agent.role,
                    'model': str(agent.model)
                }
                for agent in self.dermatology_team.members
            ] if self.initialized else [],
            'capabilities': [
                'ABCDE melanoma screening',
                'Image data extraction',
                'Differential diagnosis',
                'Clinical risk assessment',
                'Medical research validation',
                'Coordinated team analysis'
            ]
        }
    
    def analyze_with_specific_agent(self, agent_name: str, prompt: str) -> Dict:
        """Run analysis with a specific agent"""
        if not self.initialized:
            raise RuntimeError("Agno team not initialized")
        
        agent_map = {
            'abcde': self.abcde_agent,
            'image': self.image_extraction_agent,
            'diagnosis': self.diagnosis_agent,
            'risk': self.risk_agent,
            'research': self.research_agent
        }
        
        agent = agent_map.get(agent_name.lower())
        if not agent:
            raise ValueError(f"Unknown agent: {agent_name}")
        
        try:
            response = agent.run(prompt)
            return {
                'agent_name': agent.name,
                'response': response.content,
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'agent_name': agent_name,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
