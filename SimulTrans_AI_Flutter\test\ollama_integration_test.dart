import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:simul_trans_ai/core/services/ollama_service.dart';
import 'package:simul_trans_ai/core/services/real_translation_service.dart';
import 'package:simul_trans_ai/core/config/api_keys.dart';
import 'dart:typed_data';
import 'dart:convert';

void main() {
  group('Ollama Integration Tests', () {
    setUpAll(() async {
      // Load environment variables for testing
      await dotenv.load(fileName: ".env");
      await ApiKeys.initialize();
    });

    group('OllamaService Tests', () {
      test('should initialize successfully', () async {
        try {
          await OllamaService.instance.initialize();
          expect(OllamaService.instance.isInitialized, true);
          print('✅ Ollama service initialized successfully');
        } catch (e) {
          print('❌ Ollama initialization failed: $e');
          print('💡 Make sure Ollama is running: ollama serve');
          print('💡 Make sure model is available: ollama pull gemma3n:e2b');
          fail('Ollama service initialization failed: $e');
        }
      });

      test('should translate text successfully', () async {
        try {
          await OllamaService.instance.initialize();
          
          final result = await OllamaService.instance.translateText(
            text: 'Hello world',
            sourceLanguage: 'en',
            targetLanguage: 'pt',
          );

          expect(result.originalText, 'Hello world');
          expect(result.translatedText.isNotEmpty, true);
          expect(result.sourceLanguage, 'en');
          expect(result.targetLanguage, 'pt');
          expect(result.confidence, greaterThan(0.0));
          
          print('✅ Text translation successful');
          print('   Original: ${result.originalText}');
          print('   Translated: ${result.translatedText}');
          print('   Confidence: ${result.confidence}');
        } catch (e) {
          print('❌ Text translation failed: $e');
          fail('Text translation failed: $e');
        }
      });

      test('should handle translation errors gracefully', () async {
        try {
          await OllamaService.instance.initialize();
          
          // Test with empty text
          final result = await OllamaService.instance.translateText(
            text: '',
            sourceLanguage: 'en',
            targetLanguage: 'pt',
          );

          expect(result.confidence, 0.0);
          expect(result.metadata?.containsKey('error') ?? false, true);
          
          print('✅ Error handling works correctly');
        } catch (e) {
          print('✅ Exception handling works correctly: $e');
        }
      });

      test('should translate image with text', () async {
        try {
          await OllamaService.instance.initialize();
          
          // Create a simple test image (1x1 pixel PNG)
          final testImageBytes = base64Decode(
            'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
          );
          
          final result = await OllamaService.instance.translateImage(
            imageBytes: testImageBytes,
            targetLanguage: 'pt',
            sourceLanguage: 'en',
          );

          expect(result.targetLanguage, 'pt');
          expect(result.metadata?.containsKey('service') ?? false, true);
          
          print('✅ Image translation completed');
          print('   Result: ${result.translatedText}');
        } catch (e) {
          print('⚠️ Image translation test failed (expected for test image): $e');
          // This is expected to fail with a test image, but we test the flow
        }
      });
    });

    group('RealTranslationService Tests', () {
      test('should initialize with Ollama backend', () async {
        try {
          await RealTranslationService.instance.initialize();
          expect(RealTranslationService.instance.isInitialized, true);
          
          final info = RealTranslationService.instance.getServiceInfo();
          expect(info['backend'], 'Ollama');
          expect(info['is_offline'], true);
          
          print('✅ RealTranslationService initialized with Ollama');
          print('   Model: ${info['model']}');
          print('   Supported languages: ${info['supported_languages']}');
        } catch (e) {
          print('❌ RealTranslationService initialization failed: $e');
          fail('RealTranslationService initialization failed: $e');
        }
      });

      test('should perform health check', () async {
        try {
          final isHealthy = await RealTranslationService.instance.isHealthy();
          expect(isHealthy, true);
          
          print('✅ Service health check passed');
        } catch (e) {
          print('❌ Health check failed: $e');
          fail('Health check failed: $e');
        }
      });

      test('should translate multiple languages', () async {
        final testCases = [
          {'text': 'Hello', 'from': 'en', 'to': 'es'},
          {'text': 'Bonjour', 'from': 'fr', 'to': 'en'},
          {'text': 'Hola', 'from': 'es', 'to': 'pt'},
        ];

        for (final testCase in testCases) {
          try {
            final result = await RealTranslationService.instance.translateText(
              text: testCase['text']!,
              sourceLanguage: testCase['from']!,
              targetLanguage: testCase['to']!,
            );

            expect(result.translatedText.isNotEmpty, true);
            expect(result.confidence, greaterThan(0.0));
            
            print('✅ ${testCase['from']} → ${testCase['to']}: "${testCase['text']}" → "${result.translatedText}"');
          } catch (e) {
            print('❌ Translation failed for ${testCase['from']} → ${testCase['to']}: $e');
          }
        }
      });

      test('should detect language', () async {
        try {
          final detectedLang = await RealTranslationService.instance.detectLanguage('Hello world');
          expect(detectedLang.isNotEmpty, true);
          
          print('✅ Language detection: "Hello world" → $detectedLang');
        } catch (e) {
          print('⚠️ Language detection failed: $e');
          // Language detection might not work perfectly, so we don't fail the test
        }
      });
    });

    group('Performance Tests', () {
      test('should translate within reasonable time', () async {
        final stopwatch = Stopwatch()..start();
        
        try {
          final result = await RealTranslationService.instance.translateText(
            text: 'This is a performance test for the translation service.',
            sourceLanguage: 'en',
            targetLanguage: 'pt',
          );
          
          stopwatch.stop();
          final duration = stopwatch.elapsedMilliseconds;
          
          expect(result.translatedText.isNotEmpty, true);
          expect(duration, lessThan(30000)); // Should complete within 30 seconds
          
          print('✅ Performance test passed');
          print('   Duration: ${duration}ms');
          print('   Translation: ${result.translatedText}');
        } catch (e) {
          stopwatch.stop();
          print('❌ Performance test failed after ${stopwatch.elapsedMilliseconds}ms: $e');
          fail('Performance test failed: $e');
        }
      });

      test('should handle concurrent translations', () async {
        final futures = <Future>[];
        
        for (int i = 0; i < 3; i++) {
          futures.add(
            RealTranslationService.instance.translateText(
              text: 'Concurrent test $i',
              sourceLanguage: 'en',
              targetLanguage: 'pt',
            )
          );
        }
        
        try {
          final results = await Future.wait(futures);
          expect(results.length, 3);
          
          for (int i = 0; i < results.length; i++) {
            final result = results[i] as dynamic;
            expect(result.translatedText.isNotEmpty, true);
            print('✅ Concurrent translation $i: ${result.translatedText}');
          }
          
          print('✅ Concurrent translations completed successfully');
        } catch (e) {
          print('❌ Concurrent translation test failed: $e');
          fail('Concurrent translation test failed: $e');
        }
      });
    });
  });
}

/// Helper function to run tests with proper setup
void runOllamaTests() {
  print('🚀 Starting Ollama Integration Tests');
  print('');
  print('Prerequisites:');
  print('1. Ollama must be running: ollama serve');
  print('2. Model must be available: ollama pull gemma3n:e2b');
  print('3. .env file must be configured');
  print('');
  print('Running tests...');
  print('');
}
