"""
Warning suppression module for DermatoGemma Multi-Detection System v2.0
This module must be imported before any other modules to suppress all warnings
"""

import os
import sys
import warnings
import logging

# Suppress all warnings at the highest level
warnings.filterwarnings('ignore')
warnings.simplefilter('ignore')

# Suppress TensorFlow warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'

# Suppress other ML library warnings
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['CUDA_VISIBLE_DEVICES'] = ''

# Suppress absl logging completely
try:
    import absl.logging
    absl.logging.set_verbosity(absl.logging.ERROR)
    absl.logging.set_stderrthreshold(absl.logging.ERROR)
    # Disable absl logging completely
    absl.logging._warn_preinit_stderr = False
except ImportError:
    pass

# Suppress MediaPipe warnings
try:
    import mediapipe as mp
    mp.solutions.drawing_utils.DrawingSpec(color=(0,0,0), thickness=1)
except:
    pass

# Configure Python logging to suppress warnings
logging.getLogger().setLevel(logging.ERROR)
logging.getLogger('tensorflow').setLevel(logging.ERROR)
logging.getLogger('absl').setLevel(logging.ERROR)
logging.getLogger('mediapipe').setLevel(logging.ERROR)

# Redirect stderr temporarily to suppress C++ warnings
class SuppressStderr:
    def __enter__(self):
        self.original_stderr = sys.stderr
        sys.stderr = open(os.devnull, 'w')
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stderr.close()
        sys.stderr = self.original_stderr

# Function to suppress warnings during imports
def suppress_warnings_during_import():
    """Suppress warnings during module imports"""
    with SuppressStderr():
        pass

# Apply suppression
suppress_warnings_during_import()
