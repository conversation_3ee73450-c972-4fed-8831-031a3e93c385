import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import '../config/api_keys.dart';
import 'ollama_service.dart';

/// Real translation service using Ollama for offline translation
class RealTranslationService {
  static final RealTranslationService _instance = RealTranslationService._internal();
  static RealTranslationService get instance => _instance;
  RealTranslationService._internal();

  bool _isInitialized = false;
  String _currentModel = '';

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;

  /// Initialize the translation service with Ollama
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize environment variables first
      await ApiKeys.initialize();

      // Initialize Ollama service
      await OllamaService.instance.initialize();
      _currentModel = ApiKeys.ollamaModel;
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Real translation service initialized with Ollama');
        print('🤖 Using $_currentModel for offline translations');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize translation service: $e');
        print('Please ensure Ollama is running and gemma3n:e2b model is available');
      }
      rethrow;
    }
  }

  /// Translate text using Ollama
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      return await OllamaService.instance.translateText(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

    } catch (e) {
      if (kDebugMode) {
        print('Translation failed: $e');
      }

      // Return error result
      return TranslationResult(
        originalText: text,
        translatedText: 'Erro na tradução: $e',
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString()},
      );
    }
  }

  /// Translate image using Ollama
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      return await OllamaService.instance.translateImage(
        imageBytes: imageBytes,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );

    } catch (e) {
      if (kDebugMode) {
        print('Image translation failed: $e');
      }

      return TranslationResult(
        originalText: 'Image content',
        translatedText: 'Erro na tradução de imagem: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'type': 'image'},
      );
    }
  }

  /// Detect language of text
  Future<String> detectLanguage(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Use a simple translation to auto-detect language
      final result = await OllamaService.instance.translateText(
        text: text,
        sourceLanguage: 'auto',
        targetLanguage: 'en',
      );
      
      return result.sourceLanguage;
    } catch (e) {
      if (kDebugMode) {
        print('Language detection failed: $e');
      }
      return 'auto';
    }
  }

  /// Get supported languages
  List<String> getSupportedLanguages() {
    return [
      'en', 'es', 'pt', 'fr', 'de', 'it', 'ru', 'ja', 'ko', 'zh',
      'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi', 'cs',
      'hu', 'ro', 'bg', 'hr', 'sk', 'sl', 'et', 'lv', 'lt', 'mt',
      'ga', 'cy', 'eu', 'ca', 'gl', 'is', 'mk', 'sq', 'sr', 'bs',
      'me', 'lv', 'lt', 'et', 'fi', 'hu', 'cs', 'sk', 'pl', 'uk',
      'be', 'ru', 'bg', 'mk', 'sr', 'hr', 'bs', 'me', 'sl', 'sq',
      'ro', 'hu', 'cs', 'sk', 'pl', 'de', 'nl', 'da', 'sv', 'no',
      'is', 'fi', 'et', 'lv', 'lt', 'mt', 'ga', 'cy', 'eu', 'ca',
      'gl', 'pt', 'es', 'fr', 'it', 'rm', 'lb', 'de', 'nl', 'en',
      'ga', 'cy', 'gd', 'br', 'co', 'sc', 'vec', 'lij', 'pms',
      'lmo', 'nap', 'scn', 'srd', 'fur', 'lld', 'rm', 'lb'
    ];
  }

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      
      // Test with a simple translation
      final result = await translateText(
        text: 'Hello',
        sourceLanguage: 'en',
        targetLanguage: 'es',
      );
      
      return result.confidence > 0.0;
    } catch (e) {
      return false;
    }
  }

  /// Get service information
  Map<String, dynamic> getServiceInfo() {
    return {
      'service_name': 'RealTranslationService',
      'backend': 'Ollama',
      'model': _currentModel,
      'is_initialized': _isInitialized,
      'is_offline': true,
      'supported_languages': getSupportedLanguages().length,
      'supports_images': true,
      'supports_audio': false,
      'supports_video': false,
    };
  }
}
