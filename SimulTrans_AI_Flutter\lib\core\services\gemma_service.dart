import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import '../app_config.dart';
import '../models/translation_result.dart';
import '../models/language_info.dart';
import '../utils/logger.dart';
import 'cache_service.dart';
import 'performance_service.dart';
import 'ollama_service.dart';

/// Enhanced Gemma service with Ollama backend for offline translation
class GemmaService {
  static final GemmaService _instance = GemmaService._internal();
  static GemmaService get instance => _instance;
  GemmaService._internal();

  bool _isInitialized = false;
  bool _isInitializing = false;
  String _currentModelName = AppConfig.defaultModelName;

  final StreamController<double> _downloadProgressController = StreamController<double>.broadcast();
  final StreamController<String> _statusController = StreamController<String>.broadcast();

  Stream<double> get downloadProgress => _downloadProgressController.stream;
  Stream<String> get status => _statusController.stream;

  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String get currentModelName => _currentModelName;

  /// Pre-warm the service (optional background initialization)
  void preWarm() {
    if (!_isInitialized && !_isInitializing) {
      _initializeInBackground();
    }
  }

  /// Initialize Gemma 3N model with optimizations
  Future<bool> initialize({
    String? modelName,
    String? huggingFaceToken,
    bool forceReinitialization = false,
  }) async {
    if (_isInitialized && !forceReinitialization) {
      return true;
    }
    
    if (_isInitializing) {
      // Wait for current initialization to complete
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }
    
    _isInitializing = true;
    _statusController.add('Inicializando Gemma com Ollama...');

    try {
      final targetModel = modelName ?? AppConfig.defaultModelName;
      _currentModelName = targetModel;

      _statusController.add('Conectando ao Ollama...');

      // Initialize Ollama service
      await OllamaService.instance.initialize();

      _statusController.add('Ollama conectado com sucesso!');
      _statusController.add('Modelo $_currentModelName pronto para uso');

      _isInitialized = true;

      Logger.info('Gemma service initialized successfully with Ollama model: $targetModel');

      // Record performance metrics
      PerformanceService.instance.recordModelInitialization(targetModel);

      return true;
      
    } catch (e) {
      Logger.error('Failed to initialize Gemma 3N: $e');
      _statusController.add('Erro na inicialização: $e');
      return false;
    } finally {
      _isInitializing = false;
    }
  }

  /// Translate text using Ollama backend
  Future<TranslationResult> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    Map<String, dynamic>? context,
  }) async {
    if (!_isInitialized) {
      throw Exception('Gemma service not initialized');
    }

    final startTime = DateTime.now();

    try {
      // Check cache first
      final cacheKey = _generateCacheKey(text, sourceLanguage, targetLanguage);
      final cachedResult = await CacheService.instance.getTranslation(cacheKey);

      if (cachedResult != null) {
        Logger.info('Translation served from cache');
        return cachedResult;
      }

      // Use Ollama service for translation
      final result = await OllamaService.instance.translateText(
        text: text,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        context: context?['context'],
        domain: context?['domain'],
      );

      // Cache the result
      await CacheService.instance.cacheTranslation(cacheKey, result);

      // Record metrics
      PerformanceService.instance.recordTranslation(
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        textLength: text.length,
        processingTime: result.processingTime ?? Duration.zero,
      );

      Logger.info('Text translation completed in ${result.processingTime?.inMilliseconds ?? 0}ms');

      return result;

    } catch (e) {
      Logger.error('Text translation failed: $e');
      rethrow;
    }
  }

  /// Translate image with OCR and context understanding
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      throw Exception('Gemma service not initialized');
    }
    
    try {
      // Use Ollama service for image translation
      final result = await OllamaService.instance.translateImage(
        imageBytes: imageBytes,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );

      Logger.info('Image translation completed');

      return result;
      
    } catch (e) {
      Logger.error('Image translation failed: $e');
      rethrow;
    }
  }

  /// Get supported languages with metadata
  List<LanguageInfo> getSupportedLanguages() {
    return LanguageInfo.getAllSupportedLanguages();
  }

  /// Check model health and performance
  Future<Map<String, dynamic>> getModelInfo() async {
    return {
      'model_name': _currentModelName,
      'is_initialized': _isInitialized,
      'supports_multimodal': true,
      'max_tokens': AppConfig.maxTokens,
      'context_window': AppConfig.contextWindow,
      'supported_languages': getSupportedLanguages().length,
      'cache_size': await CacheService.instance.getCacheSize(),
      'performance_metrics': PerformanceService.instance.getMetrics(),
    };
  }

  /// Dispose resources
  void dispose() {
    _downloadProgressController.close();
    _statusController.close();
    OllamaService.instance.dispose();
    _isInitialized = false;
  }

  // Private helper methods
  Future<void> _initializeInBackground() async {
    try {
      await initialize();
    } catch (e) {
      Logger.warning('Background initialization failed: $e');
    }
  }

  String _generateCacheKey(String text, String? sourceLanguage, String targetLanguage) {
    return '${sourceLanguage ?? 'auto'}_${targetLanguage}_${text.hashCode}';
  }
}
