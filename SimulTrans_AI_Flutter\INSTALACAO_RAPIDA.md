# 🚀 CrisisComm - Instalação Rápida

## ⚡ Instalação em 5 Passos

### 1️⃣ Instalar Dependências
```bash
pip install -r requirements.txt
```

### 2️⃣ Configurar Token do Hugging Face
1. Acesse: https://huggingface.co/settings/tokens
2. <PERSON><PERSON> um token (Read access)
3. Crie arquivo `.env` na raiz do projeto:
```bash
HUGGING_FACE_TOKEN=seu_token_aqui
```

### 3️⃣ Aceitar Termos do Modelo
Acesse: https://huggingface.co/google/gemma-3n-E2B-it
Clique em "Accept" para aceitar os termos de uso

### 4️⃣ Baixar Modelo Gemma 3n
```bash
python download_model.py
```
⏳ **Aguarde**: Download de ~5GB pode levar 10-30 minutos

### 5️⃣ Iniciar Aplicação
```bash
python run_crisiscomm.py
```

## 🌐 Acesso

Abra no navegador: **http://localhost:8501**

---

## 🐳 Instalação com Docker

### Opção 1: Docker Compose (Recomendado)
```bash
docker-compose up -d
```

### Opção 2: Docker Manual
```bash
# Build
docker build -t crisiscomm .

# Run
docker run -p 8501:8501 crisiscomm
```

---

## 🔧 Solução Rápida de Problemas

### ❌ Erro: "Token do Hugging Face não configurado"
- **Solução**: Configure o token no arquivo .env
- **Como**: HUGGING_FACE_TOKEN=seu_token_aqui

### ❌ Erro: "Modelo não encontrado"
- **Solução**: Execute `python download_model.py`
- **Tempo**: 10-30 minutos (dependendo da conexão)

### ❌ Erro: "accelerate required"
- **Solução**: `pip install accelerate bitsandbytes`

### ❌ Erro: "Memória insuficiente"
- **Solução**: Configure `DEVICE=cpu` no arquivo `.env`
- **Alternativa**: Feche outros programas

### ❌ Erro: "Streamlit não encontrado"
```bash
pip install streamlit
```

### ❌ Erro: "Transformers não encontrado"
```bash
pip install transformers torch
```

---

## 📋 Requisitos Mínimos

- **Python**: 3.8+
- **RAM**: 4GB (8GB recomendado)
- **Espaço**: 5GB livres
- **Internet**: Para download inicial do modelo

---

## 🎯 Teste Rápido

1. Acesse a aba **"📚 Exemplos de Demonstração"**
2. Copie um texto de exemplo
3. Cole na aba **"🗣️ Tradutor de Emergência"**
4. Clique em **"🔄 Traduzir"**

---

## 🆘 Suporte Rápido

- **GitHub Issues**: [Reportar Problema](https://github.com/seu-usuario/crisiscomm/issues)
- **Email**: <EMAIL>
- **Documentação**: [README_CRISISCOMM.md](README_CRISISCOMM.md)

---

**🚨 Pronto para salvar vidas em emergências!**
