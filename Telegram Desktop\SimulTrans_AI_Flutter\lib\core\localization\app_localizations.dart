import 'package:flutter/material.dart';

/// App localization delegate and strings
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('pt', 'BR'),
    Locale('en', 'US'),
    Locale('es', 'ES'),
  ];

  // Common strings
  String get appName => 'SimulTrans AI';
  String get appDescription => 'Tradutor Simultâneo Multimodal';
  
  // Navigation
  String get home => _localizedString('home', 'Início', 'Home', 'Inicio');
  String get settings => _localizedString('settings', 'Configurações', 'Settings', 'Configuración');
  String get history => _localizedString('history', 'Histórico', 'History', 'Historial');
  
  // Translation modes
  String get textTranslation => _localizedString('textTranslation', 'Tradução de Texto', 'Text Translation', 'Traducción de Texto');
  String get imageTranslation => _localizedString('imageTranslation', 'Tradução de Imagem', 'Image Translation', 'Traducción de Imagen');
  String get audioTranslation => _localizedString('audioTranslation', 'Tradução de Áudio', 'Audio Translation', 'Traducción de Audio');
  String get videoTranslation => _localizedString('videoTranslation', 'Tradução de Vídeo', 'Video Translation', 'Traducción de Video');
  
  // Actions
  String get translate => _localizedString('translate', 'Traduzir', 'Translate', 'Traducir');
  String get copy => _localizedString('copy', 'Copiar', 'Copy', 'Copiar');
  String get share => _localizedString('share', 'Compartilhar', 'Share', 'Compartir');
  String get save => _localizedString('save', 'Salvar', 'Save', 'Guardar');
  String get cancel => _localizedString('cancel', 'Cancelar', 'Cancel', 'Cancelar');
  String get clear => _localizedString('clear', 'Limpar', 'Clear', 'Limpiar');
  String get delete => _localizedString('delete', 'Excluir', 'Delete', 'Eliminar');
  
  // Status messages
  String get initializing => _localizedString('initializing', 'Inicializando...', 'Initializing...', 'Inicializando...');
  String get translating => _localizedString('translating', 'Traduzindo...', 'Translating...', 'Traduciendo...');
  String get processing => _localizedString('processing', 'Processando...', 'Processing...', 'Procesando...');
  String get completed => _localizedString('completed', 'Concluído', 'Completed', 'Completado');
  String get error => _localizedString('error', 'Erro', 'Error', 'Error');
  
  // Error messages
  String get networkError => _localizedString('networkError', 'Erro de conexão', 'Network error', 'Error de conexión');
  String get translationError => _localizedString('translationError', 'Erro na tradução', 'Translation error', 'Error de traducción');
  String get modelError => _localizedString('modelError', 'Erro no modelo', 'Model error', 'Error del modelo');
  
  // Success messages
  String get translationSuccess => _localizedString('translationSuccess', 'Tradução concluída!', 'Translation completed!', '¡Traducción completada!');
  String get copiedToClipboard => _localizedString('copiedToClipboard', 'Copiado para a área de transferência', 'Copied to clipboard', 'Copiado al portapapeles');
  String get savedToHistory => _localizedString('savedToHistory', 'Salvo no histórico', 'Saved to history', 'Guardado en el historial');
  
  // Languages
  String get sourceLanguage => _localizedString('sourceLanguage', 'Idioma de origem', 'Source language', 'Idioma de origen');
  String get targetLanguage => _localizedString('targetLanguage', 'Idioma de destino', 'Target language', 'Idioma de destino');
  String get autoDetect => _localizedString('autoDetect', 'Detectar automaticamente', 'Auto-detect', 'Detectar automáticamente');
  
  // Settings
  String get theme => _localizedString('theme', 'Tema', 'Theme', 'Tema');
  String get lightTheme => _localizedString('lightTheme', 'Claro', 'Light', 'Claro');
  String get darkTheme => _localizedString('darkTheme', 'Escuro', 'Dark', 'Oscuro');
  String get systemTheme => _localizedString('systemTheme', 'Sistema', 'System', 'Sistema');
  String get language => _localizedString('language', 'Idioma', 'Language', 'Idioma');
  String get privacy => _localizedString('privacy', 'Privacidade', 'Privacy', 'Privacidad');
  String get analytics => _localizedString('analytics', 'Analytics', 'Analytics', 'Analytics');
  String get crashReporting => _localizedString('crashReporting', 'Relatórios de erro', 'Crash reporting', 'Informes de errores');
  String get storage => _localizedString('storage', 'Armazenamento', 'Storage', 'Almacenamiento');
  String get cache => _localizedString('cache', 'Cache', 'Cache', 'Caché');
  String get clearCache => _localizedString('clearCache', 'Limpar cache', 'Clear cache', 'Limpiar caché');
  
  // Helper method to get localized string based on current locale
  String _localizedString(String key, String pt, String en, String es) {
    switch (locale.languageCode) {
      case 'pt':
        return pt;
      case 'en':
        return en;
      case 'es':
        return es;
      default:
        return en; // Default to English
    }
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales.any((supportedLocale) =>
        supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
