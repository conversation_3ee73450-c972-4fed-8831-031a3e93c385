# CrisisComm - Docker Compose
# Configuração para execução com Docker Compose

version: '3.8'

services:
  crisiscomm:
    build: .
    container_name: crisiscomm-app
    ports:
      - "8501:8501"
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - DEVICE=cpu  # Usar CPU por padrão (mude para 'cuda' se tiver GPU)
      - DEBUG=false
      - LOG_LEVEL=INFO
    volumes:
      # Cache persistente para modelos
      - ./cache:/app/cache
      # Logs persistentes
      - ./logs:/app/logs
      # Arquivos temporários
      - ./temp:/app/temp
      # Exportações
      - ./exports:/app/exports
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Serviço opcional para monitoramento
  # Descomente se quiser monitoramento com Prometheus
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: crisiscomm-prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #   restart: unless-stopped

networks:
  default:
    name: crisiscomm-network
