import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';

/// Enhanced text input field for translation with smart features
class TranslationInputField extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final String? hintText;
  final int maxLines;
  final Function(String)? onSubmitted;
  final Function(String)? onChanged;
  final bool enabled;

  const TranslationInputField({
    super.key,
    required this.controller,
    this.focusNode,
    this.hintText,
    this.maxLines = 5,
    this.onSubmitted,
    this.onChanged,
    this.enabled = true,
  });

  @override
  State<TranslationInputField> createState() => _TranslationInputFieldState();
}

class _TranslationInputFieldState extends State<TranslationInputField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _borderColorAnimation;
  
  bool _isFocused = false;
  int _characterCount = 0;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _borderColorAnimation = ColorTween(
      begin: Colors.grey[300],
      end: AppTheme.primaryColor,
    ).animate(_animationController);
    
    widget.controller.addListener(_updateCharacterCount);
    widget.focusNode?.addListener(_onFocusChanged);
    
    _characterCount = widget.controller.text.length;
  }

  void _updateCharacterCount() {
    setState(() {
      _characterCount = widget.controller.text.length;
    });
    widget.onChanged?.call(widget.controller.text);
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = widget.focusNode?.hasFocus ?? false;
    });
    
    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    widget.controller.removeListener(_updateCharacterCount);
    widget.focusNode?.removeListener(_onFocusChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _borderColorAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _borderColorAnimation.value ?? Colors.grey[300]!,
              width: _isFocused ? 2 : 1,
            ),
          ),
          child: Column(
            children: [
              TextField(
                controller: widget.controller,
                focusNode: widget.focusNode,
                enabled: widget.enabled,
                maxLines: widget.maxLines,
                textInputAction: TextInputAction.newline,
                onSubmitted: widget.onSubmitted,
                style: theme.textTheme.bodyLarge,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  counterText: '', // Hide default counter
                ),
              ),
              
              // Custom footer with character count and actions
              if (_isFocused || _characterCount > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Character count
                      Text(
                        '$_characterCount caracteres',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      
                      const Spacer(),
                      
                      // Quick actions
                      if (_characterCount > 0) ...[
                        _buildQuickAction(
                          icon: Icons.clear,
                          tooltip: 'Limpar',
                          onPressed: () {
                            widget.controller.clear();
                          },
                        ),
                        const SizedBox(width: 8),
                      ],
                      
                      _buildQuickAction(
                        icon: Icons.paste,
                        tooltip: 'Colar',
                        onPressed: _pasteFromClipboard,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(4),
          child: Icon(
            icon,
            size: 16,
            color: AppTheme.primaryColor,
          ),
        ),
      ),
    );
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        final currentText = widget.controller.text;
        final selection = widget.controller.selection;
        
        final newText = currentText.replaceRange(
          selection.start,
          selection.end,
          clipboardData!.text!,
        );
        
        widget.controller.text = newText;
        widget.controller.selection = TextSelection.collapsed(
          offset: selection.start + clipboardData.text!.length,
        );
      }
    } catch (e) {
      // Handle error silently or show a snackbar
      debugPrint('Error pasting from clipboard: $e');
    }
  }
}
