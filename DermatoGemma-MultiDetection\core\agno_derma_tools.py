#!/usr/bin/env python3
"""
Specialized tools for Agno dermatology agents
"""

import logging
import numpy as np
import cv2
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json

try:
    from agno.tools import Toolkit
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = logging.getLogger(__name__)

class DermatologyImageAnalysisTools(Toolkit):
    """Specialized image analysis tools for dermatology agents"""
    
    def __init__(self):
        super().__init__(name="dermatology_image_analysis")
        
    def analyze_lesion_symmetry(self, image_array: np.ndarray) -> Dict[str, Any]:
        """
        Analyze lesion symmetry for ABCDE assessment
        
        Args:
            image_array: Input image as numpy array
            
        Returns:
            Symmetry analysis results
        """
        try:
            # Convert to grayscale for analysis
            if len(image_array.shape) == 3:
                gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = image_array
            
            # Find lesion contours
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return {"error": "No lesion contours found"}
            
            # Get largest contour (assumed to be the lesion)
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculate moments and centroid
            moments = cv2.moments(largest_contour)
            if moments['m00'] == 0:
                return {"error": "Cannot calculate lesion centroid"}
            
            cx = int(moments['m10'] / moments['m00'])
            cy = int(moments['m01'] / moments['m00'])
            
            # Analyze symmetry by comparing left/right and top/bottom halves
            height, width = gray.shape
            
            # Vertical symmetry (left vs right)
            left_half = gray[:, :cx]
            right_half = cv2.flip(gray[:, cx:], 1)  # Flip horizontally
            
            # Resize to same dimensions for comparison
            min_width = min(left_half.shape[1], right_half.shape[1])
            left_resized = cv2.resize(left_half, (min_width, height))
            right_resized = cv2.resize(right_half, (min_width, height))
            
            # Calculate vertical symmetry score
            vertical_diff = cv2.absdiff(left_resized, right_resized)
            vertical_symmetry = 1.0 - (np.mean(vertical_diff) / 255.0)
            
            # Horizontal symmetry (top vs bottom)
            top_half = gray[:cy, :]
            bottom_half = cv2.flip(gray[cy:, :], 0)  # Flip vertically
            
            # Resize to same dimensions for comparison
            min_height = min(top_half.shape[0], bottom_half.shape[0])
            top_resized = cv2.resize(top_half, (width, min_height))
            bottom_resized = cv2.resize(bottom_half, (width, min_height))
            
            # Calculate horizontal symmetry score
            horizontal_diff = cv2.absdiff(top_resized, bottom_resized)
            horizontal_symmetry = 1.0 - (np.mean(horizontal_diff) / 255.0)
            
            # Overall asymmetry score (higher = more asymmetric)
            asymmetry_score = 1.0 - ((vertical_symmetry + horizontal_symmetry) / 2.0)
            
            return {
                "asymmetry_score": float(asymmetry_score),
                "vertical_symmetry": float(vertical_symmetry),
                "horizontal_symmetry": float(horizontal_symmetry),
                "centroid": {"x": cx, "y": cy},
                "interpretation": self._interpret_asymmetry(asymmetry_score)
            }
            
        except Exception as e:
            logger.error(f"Symmetry analysis failed: {e}")
            return {"error": str(e)}
    
    def analyze_border_irregularity(self, image_array: np.ndarray) -> Dict[str, Any]:
        """
        Analyze border irregularity for ABCDE assessment
        
        Args:
            image_array: Input image as numpy array
            
        Returns:
            Border analysis results
        """
        try:
            # Convert to grayscale
            if len(image_array.shape) == 3:
                gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = image_array
            
            # Edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return {"error": "No border contours found"}
            
            # Get largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculate perimeter and area
            perimeter = cv2.arcLength(largest_contour, True)
            area = cv2.contourArea(largest_contour)
            
            if area == 0:
                return {"error": "Invalid lesion area"}
            
            # Calculate circularity (4π*area/perimeter²)
            circularity = (4 * np.pi * area) / (perimeter ** 2)
            
            # Border irregularity score (1 - circularity)
            irregularity_score = 1.0 - circularity
            
            # Calculate convex hull for solidity
            hull = cv2.convexHull(largest_contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            
            # Analyze border smoothness
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)
            smoothness = len(approx) / len(largest_contour)
            
            return {
                "irregularity_score": float(irregularity_score),
                "circularity": float(circularity),
                "solidity": float(solidity),
                "smoothness": float(smoothness),
                "perimeter": float(perimeter),
                "area": float(area),
                "interpretation": self._interpret_border_irregularity(irregularity_score)
            }
            
        except Exception as e:
            logger.error(f"Border analysis failed: {e}")
            return {"error": str(e)}
    
    def analyze_color_variation(self, image_array: np.ndarray) -> Dict[str, Any]:
        """
        Analyze color variation for ABCDE assessment
        
        Args:
            image_array: Input image as numpy array
            
        Returns:
            Color analysis results
        """
        try:
            # Ensure RGB format
            if len(image_array.shape) != 3:
                return {"error": "Image must be in color (RGB)"}
            
            # Convert to different color spaces for analysis
            hsv = cv2.cvtColor(image_array, cv2.COLOR_RGB2HSV)
            lab = cv2.cvtColor(image_array, cv2.COLOR_RGB2LAB)
            
            # Analyze RGB channels
            rgb_means = np.mean(image_array, axis=(0, 1))
            rgb_stds = np.std(image_array, axis=(0, 1))
            
            # Analyze HSV channels
            hsv_means = np.mean(hsv, axis=(0, 1))
            hsv_stds = np.std(hsv, axis=(0, 1))
            
            # Calculate color variation metrics
            rgb_variation = np.mean(rgb_stds) / 255.0
            hue_variation = hsv_stds[0] / 180.0  # Hue is 0-180 in OpenCV
            saturation_variation = hsv_stds[1] / 255.0
            
            # Overall color variation score
            color_variation_score = (rgb_variation + hue_variation + saturation_variation) / 3.0
            
            # Detect dominant colors using k-means
            dominant_colors = self._extract_dominant_colors(image_array)
            
            # Calculate color diversity
            color_diversity = len(dominant_colors) / 10.0  # Normalize to 0-1
            
            return {
                "color_variation_score": float(color_variation_score),
                "rgb_variation": float(rgb_variation),
                "hue_variation": float(hue_variation),
                "saturation_variation": float(saturation_variation),
                "color_diversity": float(color_diversity),
                "dominant_colors": dominant_colors,
                "rgb_means": rgb_means.tolist(),
                "hsv_means": hsv_means.tolist(),
                "interpretation": self._interpret_color_variation(color_variation_score)
            }
            
        except Exception as e:
            logger.error(f"Color analysis failed: {e}")
            return {"error": str(e)}
    
    def estimate_lesion_diameter(self, image_array: np.ndarray, pixel_to_mm_ratio: float = 0.1) -> Dict[str, Any]:
        """
        Estimate lesion diameter for ABCDE assessment
        
        Args:
            image_array: Input image as numpy array
            pixel_to_mm_ratio: Conversion ratio from pixels to millimeters
            
        Returns:
            Diameter analysis results
        """
        try:
            # Convert to grayscale
            if len(image_array.shape) == 3:
                gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = image_array
            
            # Find lesion contours
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return {"error": "No lesion contours found"}
            
            # Get largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculate bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Calculate equivalent diameter (diameter of circle with same area)
            area = cv2.contourArea(largest_contour)
            equivalent_diameter_px = 2 * np.sqrt(area / np.pi)
            
            # Calculate maximum diameter (longest distance across lesion)
            max_diameter_px = max(w, h)
            
            # Convert to millimeters
            equivalent_diameter_mm = equivalent_diameter_px * pixel_to_mm_ratio
            max_diameter_mm = max_diameter_px * pixel_to_mm_ratio
            
            # Diameter score for ABCDE (>6mm is concerning)
            diameter_score = min(equivalent_diameter_mm / 6.0, 1.0)
            
            return {
                "equivalent_diameter_mm": float(equivalent_diameter_mm),
                "max_diameter_mm": float(max_diameter_mm),
                "equivalent_diameter_px": float(equivalent_diameter_px),
                "max_diameter_px": float(max_diameter_px),
                "diameter_score": float(diameter_score),
                "area_mm2": float(area * (pixel_to_mm_ratio ** 2)),
                "interpretation": self._interpret_diameter(equivalent_diameter_mm)
            }
            
        except Exception as e:
            logger.error(f"Diameter analysis failed: {e}")
            return {"error": str(e)}
    
    def _extract_dominant_colors(self, image_array: np.ndarray, k: int = 5) -> List[List[int]]:
        """Extract dominant colors using k-means clustering"""
        try:
            from sklearn.cluster import KMeans
            
            # Reshape image to be a list of pixels
            pixels = image_array.reshape(-1, 3)
            
            # Apply k-means clustering
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(pixels)
            
            # Get cluster centers (dominant colors)
            colors = kmeans.cluster_centers_.astype(int)
            
            return colors.tolist()
            
        except ImportError:
            # Fallback if sklearn not available
            return [[128, 128, 128]]  # Gray fallback
        except Exception as e:
            logger.warning(f"Color extraction failed: {e}")
            return [[128, 128, 128]]  # Gray fallback
    
    def _interpret_asymmetry(self, score: float) -> str:
        """Interpret asymmetry score"""
        if score < 0.2:
            return "Symmetric (low concern)"
        elif score < 0.5:
            return "Mildly asymmetric (moderate concern)"
        else:
            return "Highly asymmetric (high concern)"
    
    def _interpret_border_irregularity(self, score: float) -> str:
        """Interpret border irregularity score"""
        if score < 0.3:
            return "Regular borders (low concern)"
        elif score < 0.6:
            return "Irregular borders (moderate concern)"
        else:
            return "Highly irregular borders (high concern)"
    
    def _interpret_color_variation(self, score: float) -> str:
        """Interpret color variation score"""
        if score < 0.3:
            return "Uniform color (low concern)"
        elif score < 0.6:
            return "Moderate color variation (moderate concern)"
        else:
            return "High color variation (high concern)"
    
    def _interpret_diameter(self, diameter_mm: float) -> str:
        """Interpret diameter measurement"""
        if diameter_mm < 6.0:
            return f"Small lesion ({diameter_mm:.1f}mm - low concern)"
        elif diameter_mm < 10.0:
            return f"Medium lesion ({diameter_mm:.1f}mm - moderate concern)"
        else:
            return f"Large lesion ({diameter_mm:.1f}mm - high concern)"

class DermatologyKnowledgeTools(Toolkit):
    """Knowledge and reference tools for dermatology agents"""
    
    def __init__(self):
        super().__init__(name="dermatology_knowledge")
    
    def get_condition_info(self, condition_name: str) -> Dict[str, Any]:
        """Get information about a dermatological condition"""
        
        # Simplified condition database
        conditions_db = {
            "melanoma": {
                "description": "Malignant tumor of melanocytes",
                "key_features": ["Asymmetry", "Irregular borders", "Color variation", "Large diameter", "Evolution"],
                "risk_factors": ["UV exposure", "Fair skin", "Family history", "Multiple moles"],
                "urgency": "High",
                "treatment": "Surgical excision, possible adjuvant therapy"
            },
            "basal_cell_carcinoma": {
                "description": "Most common skin cancer, locally invasive",
                "key_features": ["Pearly appearance", "Rolled borders", "Central ulceration"],
                "risk_factors": ["UV exposure", "Fair skin", "Age"],
                "urgency": "Moderate",
                "treatment": "Surgical excision, Mohs surgery"
            },
            "seborrheic_keratosis": {
                "description": "Benign epidermal tumor",
                "key_features": ["Waxy appearance", "Stuck-on appearance", "Brown color"],
                "risk_factors": ["Age", "Genetics"],
                "urgency": "Low",
                "treatment": "Usually none required, cosmetic removal if desired"
            }
        }
        
        condition_key = condition_name.lower().replace(" ", "_")
        return conditions_db.get(condition_key, {"error": f"Condition '{condition_name}' not found"})
    
    def get_abcde_criteria(self) -> Dict[str, str]:
        """Get ABCDE criteria for melanoma screening"""
        return {
            "A": "Asymmetry - One half does not match the other half",
            "B": "Border - Irregular, scalloped, or poorly defined borders",
            "C": "Color - Varied from one area to another; shades of tan, brown, black, red, white, or blue",
            "D": "Diameter - Larger than 6mm (size of a pencil eraser)",
            "E": "Evolution - Changes in size, shape, color, elevation, or symptoms"
        }
    
    def get_risk_stratification_guidelines(self) -> Dict[str, Any]:
        """Get risk stratification guidelines"""
        return {
            "low_risk": {
                "criteria": ["Symmetric", "Regular borders", "Uniform color", "<6mm diameter", "No evolution"],
                "follow_up": "Routine skin examination in 12 months",
                "urgency": "Routine"
            },
            "moderate_risk": {
                "criteria": ["1-2 ABCDE criteria positive", "Atypical features"],
                "follow_up": "Dermatology referral within 2-4 weeks",
                "urgency": "Semi-urgent"
            },
            "high_risk": {
                "criteria": ["3+ ABCDE criteria positive", "Rapid changes", "Bleeding/ulceration"],
                "follow_up": "Urgent dermatology referral within 1 week",
                "urgency": "Urgent"
            }
        }
