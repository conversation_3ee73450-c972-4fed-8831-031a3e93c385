# SimulTrans AI - Tradutor Simultâneo Multimodal Offline

![SimulTrans AI](https://img.shields.io/badge/SimulTrans-AI-blue)
![Flutter](https://img.shields.io/badge/Flutter-3.8+-blue)
![Ollama](https://img.shields.io/badge/Ollama-Powered-green)
![<PERSON>](https://img.shields.io/badge/Gemma-3N:E2B-orange)
![Offline](https://img.shields.io/badge/100%25-Offline-red)
![License](https://img.shields.io/badge/License-MIT-yellow)

Um tradutor simultâneo multimodal **100% offline** powered by Ollama + Gemma, oferecendo tradução privada e segura para texto, imagem, áudio e vídeo em 140+ idiomas.

## 🌟 Características Principais

### 🚀 Tecnologia de Ponta
- **Ollama + Gemma**: Modelo de IA multimodal rodando localmente via Ollama
- **100% Offline**: Funciona completamente sem conexão com internet
- **Privacidade Total**: Seus dados nunca saem do seu computador
- **140+ Idiomas**: Suporte extensivo incluindo idiomas menos comuns
- **Multimodal**: Texto, imagem, áudio e vídeo em uma única aplicação

### 🎯 Funcionalidades Avançadas
- **Detecção Automática de Idioma**: Identifica automaticamente o idioma de origem
- **Fallback Hierárquico**: Sistema inteligente para idiomas não cobertos
- **Cache Inteligente**: Otimização de performance com cache LRU
- **Contribuição Comunitária**: Sistema para melhorias colaborativas
- **Analytics Avançado**: Monitoramento de performance e uso

### 🌍 Suporte Especializado
- **Idiomas Tonais**: Tratamento especial para vietnamita, tailandês, etc.
- **Scripts Complexos**: Suporte para árabe, devanagari, chinês, etc.
- **Conjugações Complexas**: Otimizado para finlandês, húngaro, etc.
- **Direita para Esquerda**: Suporte nativo para árabe, hebraico, etc.

## 📱 Modos de Tradução

### 📝 Tradução de Texto
- Interface intuitiva com detecção automática
- Histórico de traduções
- Cópia rápida e compartilhamento
- Suporte a textos longos

### 🖼️ Tradução de Imagem
- OCR avançado com Gemma via Ollama
- Suporte a múltiplos formatos (JPG, PNG, WebP, etc.)
- Análise de contexto visual offline
- Captura por câmera ou galeria

### 🎵 Tradução de Áudio (Em Desenvolvimento)
- Gravação em tempo real
- Suporte a múltiplos falantes
- Redução de ruído inteligente
- Formatos: MP3, WAV, M4A, FLAC

### 🎬 Tradução de Vídeo (Em Desenvolvimento)
- Legendas automáticas
- Análise de frames para contexto
- Sincronização temporal
- Formatos: MP4, AVI, MOV, MKV

## 🏗️ Arquitetura

### 📦 Estrutura do Projeto
```
lib/
├── core/                     # Núcleo da aplicação
│   ├── app_config.dart      # Configurações globais
│   ├── models/              # Modelos de dados
│   ├── services/            # Serviços principais
│   ├── theme/               # Temas e estilos
│   ├── providers/           # Gerenciamento de estado
│   └── utils/               # Utilitários
├── features/                # Funcionalidades por módulo
│   ├── home/               # Tela principal
│   ├── translation/        # Sistema de tradução
│   ├── settings/           # Configurações
│   ├── history/            # Histórico
│   └── splash/             # Tela inicial
└── main.dart               # Ponto de entrada
```

### 🔧 Serviços Principais

#### OllamaService
- Comunicação com servidor Ollama local
- Tradução multimodal via Gemma (texto, imagem)
- Processamento 100% offline
- Otimizações de performance

#### GemmaService
- Interface unificada para tradução
- Gerenciamento de cache e fallbacks
- Integração com OllamaService
- Compatibilidade com interface existente

#### CacheService
- Cache LRU com compressão
- Limpeza automática de entradas expiradas
- Controle de tamanho e performance
- Persistência local com Hive

#### PerformanceService
- Monitoramento em tempo real
- Métricas de tradução
- Análise de tendências
- Otimização automática

#### AnalyticsService
- Coleta de métricas de uso
- Relatórios de erro com Firebase
- Análise de performance
- Privacidade por design

## 🚀 Instalação e Configuração

### Pré-requisitos
- Flutter 3.8.0 ou superior
- Dart 3.0.0 ou superior
- Android SDK 21+ ou iOS 12+
- **Ollama instalado e rodando** (ver seção abaixo)
- Modelo Gemma baixado via Ollama

### 1. Instalar e Configurar Ollama

**⚠️ IMPORTANTE**: O Ollama deve estar instalado e rodando antes de usar o app.

#### Instalação Rápida:
```bash
# Windows/macOS: Baixe de https://ollama.ai/download
# Linux:
curl -fsSL https://ollama.ai/install.sh | sh
```

#### Configuração:
```bash
# 1. Iniciar Ollama (mantenha rodando)
ollama serve

# 2. Em outro terminal, baixar o modelo
ollama pull gemma3n:e2b

# 3. Verificar instalação
ollama list
```

📖 **Guia Completo**: Veja [OLLAMA_SETUP.md](OLLAMA_SETUP.md) para instruções detalhadas.

### 2. Clone o Repositório
```bash
git clone https://github.com/seu-usuario/simul-trans-ai.git
cd simul-trans-ai
```

### 2. Instale as Dependências
```bash
flutter pub get
```

### 3. Configure o Firebase (Opcional)
```bash
# Adicione seus arquivos de configuração:
# android/app/google-services.json
# ios/Runner/GoogleService-Info.plist
```

### 4. Configure Variáveis de Ambiente
```bash
# Crie um arquivo .env na raiz do projeto
HUGGING_FACE_TOKEN=seu_token_aqui
FIREBASE_PROJECT_ID=seu_projeto_firebase
```

### 5. Execute o Aplicativo
```bash
flutter run
```

## 🔧 Configuração Avançada

### Modelos Gemma 3N Suportados
- `gemma-3n-E2B-it`: Modelo base (2B parâmetros)
- `gemma-3n-E4B-it`: Modelo avançado (4B parâmetros) - Recomendado
- `gemma-3n-E2B`: Versão base sem instruction tuning
- `gemma-3n-E4B`: Versão avançada sem instruction tuning

### Configurações de Performance
```dart
// lib/core/app_config.dart
static const int maxTokens = 4096;
static const int contextWindow = 32768;
static const double defaultTemperature = 0.7;
static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
```

### Otimizações de Memória
- Cache inteligente com LRU
- Compressão automática de dados grandes
- Limpeza periódica de recursos
- Gerenciamento eficiente de modelos

## 📊 Performance e Métricas

### Benchmarks
- **Tradução de Texto**: ~500ms (média)
- **Análise de Imagem**: ~2-5s (dependendo do tamanho)
- **Uso de Memória**: ~200-500MB (com modelo carregado)
- **Tamanho do App**: ~50MB (sem modelos)

### Otimizações Implementadas
- ✅ Cache LRU com compressão
- ✅ Lazy loading de modelos
- ✅ Processamento em background
- ✅ Otimização de UI com AnimationController
- ✅ Gerenciamento eficiente de estado

## 🌐 Idiomas Suportados

### Idiomas Principais (Alta Prioridade)
- 🇺🇸 Inglês - English
- 🇪🇸 Espanhol - Español  
- 🇨🇳 Chinês - 中文 (Mandarim, Tonal)
- 🇮🇳 Hindi - हिन्दी (Script Complexo)
- 🇸🇦 Árabe - العربية (RTL, Script Complexo)
- 🇧🇷 Português - Português
- 🇧🇩 Bengali - বাংলা (Script Complexo)
- 🇷🇺 Russo - Русский
- 🇯🇵 Japonês - 日本語 (Script Complexo)
- 🇫🇷 Francês - Français

### Idiomas com Tratamento Especial
- **Tonais**: Vietnamita, Tailandês, Birmanês, Laosiano
- **Scripts Complexos**: Árabe, Hindi, Bengali, Tamil, Telugu
- **RTL**: Árabe, Hebraico, Persa, Urdu
- **Conjugações Complexas**: Finlandês, Húngaro, Turco

### Total: 140+ Idiomas Suportados

## 🤝 Contribuição

### Como Contribuir
1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

### Áreas de Contribuição
- 🌍 Adição de novos idiomas
- 🎨 Melhorias de UI/UX
- ⚡ Otimizações de performance
- 🐛 Correção de bugs
- 📚 Documentação
- 🧪 Testes automatizados

### Sistema de Contribuição Comunitária
- Feedback de qualidade de tradução
- Sugestões de melhorias
- Validação por falantes nativos
- Sistema de reputação

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🙏 Agradecimentos

- **Google**: Pelo modelo Gemma 3N
- **Flutter Team**: Pelo framework excepcional
- **Comunidade Open Source**: Por bibliotecas e ferramentas
- **Contribuidores**: Por melhorias e feedback

## 📞 Suporte

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/seu-usuario/simul-trans-ai/issues)
- 💬 Discussões: [GitHub Discussions](https://github.com/seu-usuario/simul-trans-ai/discussions)
- 📖 Wiki: [Documentação Completa](https://github.com/seu-usuario/simul-trans-ai/wiki)

## 🗺️ Roadmap

### Versão 1.1 (Q2 2025)
- ✅ Tradução de texto completa
- ✅ Tradução de imagem básica
- 🔄 Tradução de áudio (em desenvolvimento)
- 🔄 Tradução de vídeo (em desenvolvimento)

### Versão 1.2 (Q3 2025)
- 🔮 LoRA para especialização de domínios
- 🔮 Modo colaborativo em tempo real
- 🔮 API pública para desenvolvedores
- 🔮 Extensões para navegadores

### Versão 2.0 (Q4 2025)
- 🔮 Gemma 4N integration
- 🔮 Tradução em tempo real para chamadas
- 🔮 AR/VR translation overlay
- 🔮 Blockchain-based community validation

---

**Desenvolvido com ❤️ usando Flutter e Google Gemma 3N**
