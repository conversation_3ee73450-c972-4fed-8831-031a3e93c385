"""
Utilitários para o CrisisComm
"""

import os
import json
import hashlib
import tempfile
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import streamlit as st
from PIL import Image
import numpy as np
import cv2

logger = logging.getLogger(__name__)

class FileUtils:
    """Utilitários para manipulação de arquivos"""
    
    @staticmethod
    def validate_file_size(file, max_size_mb: int = 50) -> bool:
        """Valida o tamanho do arquivo"""
        if file is None:
            return False
        
        file_size_mb = len(file.getvalue()) / (1024 * 1024)
        return file_size_mb <= max_size_mb
    
    @staticmethod
    def validate_file_type(file, allowed_extensions: List[str]) -> bool:
        """Valida o tipo do arquivo"""
        if file is None:
            return False
        
        file_extension = file.name.split('.')[-1].lower()
        return file_extension in allowed_extensions
    
    @staticmethod
    def save_temp_file(file, prefix: str = "crisiscomm_") -> str:
        """Salva arquivo temporário e retorna o caminho"""
        try:
            with tempfile.NamedTemporaryFile(
                delete=False, 
                prefix=prefix,
                suffix=f".{file.name.split('.')[-1]}"
            ) as tmp_file:
                tmp_file.write(file.getvalue())
                return tmp_file.name
        except Exception as e:
            logger.error(f"Erro ao salvar arquivo temporário: {str(e)}")
            return None
    
    @staticmethod
    def cleanup_temp_file(file_path: str):
        """Remove arquivo temporário"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logger.error(f"Erro ao remover arquivo temporário: {str(e)}")

class ImageUtils:
    """Utilitários para processamento de imagem"""
    
    @staticmethod
    def resize_image(image: Image.Image, max_size: Tuple[int, int] = (1024, 1024)) -> Image.Image:
        """Redimensiona imagem mantendo proporção"""
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        return image
    
    @staticmethod
    def optimize_image_for_model(image: Image.Image) -> Image.Image:
        """Otimiza imagem para processamento pelo modelo"""
        # Converter para RGB se necessário
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Redimensionar para tamanhos suportados pelo modelo
        target_sizes = [(256, 256), (512, 512), (768, 768)]
        
        # Escolher o tamanho mais próximo
        original_size = image.size
        best_size = min(target_sizes, key=lambda x: abs(x[0] - original_size[0]) + abs(x[1] - original_size[1]))
        
        return image.resize(best_size, Image.Resampling.LANCZOS)
    
    @staticmethod
    def extract_image_metadata(image: Image.Image) -> Dict:
        """Extrai metadados da imagem"""
        return {
            "format": image.format,
            "mode": image.mode,
            "size": image.size,
            "has_transparency": image.mode in ('RGBA', 'LA') or 'transparency' in image.info
        }
    
    @staticmethod
    def detect_image_quality(image: Image.Image) -> str:
        """Detecta qualidade da imagem"""
        width, height = image.size
        total_pixels = width * height
        
        if total_pixels < 100000:  # < 0.1MP
            return "baixa"
        elif total_pixels < 1000000:  # < 1MP
            return "média"
        else:
            return "alta"

class AudioUtils:
    """Utilitários para processamento de áudio"""
    
    @staticmethod
    def validate_audio_duration(file_path: str, max_duration: int = 300) -> bool:
        """Valida duração do áudio"""
        try:
            import librosa
            duration = librosa.get_duration(path=file_path)
            return duration <= max_duration
        except Exception as e:
            logger.error(f"Erro ao validar duração do áudio: {str(e)}")
            return False
    
    @staticmethod
    def get_audio_info(file_path: str) -> Dict:
        """Obtém informações do arquivo de áudio"""
        try:
            import librosa
            y, sr = librosa.load(file_path, sr=None)
            duration = librosa.get_duration(y=y, sr=sr)
            
            return {
                "duration": duration,
                "sample_rate": sr,
                "channels": 1 if len(y.shape) == 1 else y.shape[0],
                "samples": len(y)
            }
        except Exception as e:
            logger.error(f"Erro ao obter informações do áudio: {str(e)}")
            return {}

class TextUtils:
    """Utilitários para processamento de texto"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Limpa e normaliza texto"""
        if not text:
            return ""
        
        # Remove espaços extras
        text = ' '.join(text.split())
        
        # Remove caracteres de controle
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        
        return text.strip()
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 5000) -> str:
        """Trunca texto se necessário"""
        if len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
    
    @staticmethod
    def detect_language_hints(text: str) -> List[str]:
        """Detecta possíveis idiomas baseado em caracteres"""
        hints = []
        
        # Detectar scripts
        if any('\u0600' <= char <= '\u06FF' for char in text):
            hints.append('ar')  # Árabe
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            hints.append('zh')  # Chinês
        if any('\u3040' <= char <= '\u309f' for char in text):
            hints.append('ja')  # Hiragana (Japonês)
        if any('\u30a0' <= char <= '\u30ff' for char in text):
            hints.append('ja')  # Katakana (Japonês)
        if any('\uac00' <= char <= '\ud7af' for char in text):
            hints.append('ko')  # Coreano
        if any('\u0900' <= char <= '\u097f' for char in text):
            hints.append('hi')  # Hindi
        if any('\u0400' <= char <= '\u04ff' for char in text):
            hints.append('ru')  # Russo
        
        return hints
    
    @staticmethod
    def format_emergency_message(template: str, data: Dict) -> str:
        """Formata mensagem de emergência usando template"""
        try:
            return template.format(**data)
        except KeyError as e:
            logger.warning(f"Campo faltando no template: {str(e)}")
            return template
        except Exception as e:
            logger.error(f"Erro ao formatar mensagem: {str(e)}")
            return template

class CacheUtils:
    """Utilitários para cache"""
    
    @staticmethod
    def generate_cache_key(*args) -> str:
        """Gera chave de cache baseada nos argumentos"""
        content = json.dumps(args, sort_keys=True, default=str)
        return hashlib.md5(content.encode()).hexdigest()
    
    @staticmethod
    def is_cache_valid(timestamp: datetime, ttl_hours: int = 24) -> bool:
        """Verifica se cache ainda é válido"""
        expiry_time = timestamp + timedelta(hours=ttl_hours)
        return datetime.now() < expiry_time

class UIUtils:
    """Utilitários para interface do usuário"""
    
    @staticmethod
    def show_success_message(message: str, icon: str = "✅"):
        """Exibe mensagem de sucesso"""
        st.success(f"{icon} {message}")
    
    @staticmethod
    def show_error_message(message: str, icon: str = "❌"):
        """Exibe mensagem de erro"""
        st.error(f"{icon} {message}")
    
    @staticmethod
    def show_warning_message(message: str, icon: str = "⚠️"):
        """Exibe mensagem de aviso"""
        st.warning(f"{icon} {message}")
    
    @staticmethod
    def show_info_message(message: str, icon: str = "ℹ️"):
        """Exibe mensagem informativa"""
        st.info(f"{icon} {message}")
    
    @staticmethod
    def create_download_link(data: str, filename: str, mime_type: str = "text/plain") -> str:
        """Cria link de download para dados"""
        import base64
        b64 = base64.b64encode(data.encode()).decode()
        return f'<a href="data:{mime_type};base64,{b64}" download="{filename}">📥 Baixar {filename}</a>'
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Formata tamanho de arquivo em formato legível"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def create_progress_bar(current: int, total: int, label: str = "") -> None:
        """Cria barra de progresso"""
        progress = current / total if total > 0 else 0
        st.progress(progress, text=f"{label} ({current}/{total})")

class ValidationUtils:
    """Utilitários para validação"""
    
    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """Valida coordenadas geográficas"""
        return -90 <= lat <= 90 and -180 <= lon <= 180
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """Validação básica de número de telefone"""
        import re
        pattern = r'^[\+]?[1-9][\d]{0,15}$'
        return bool(re.match(pattern, phone.replace(' ', '').replace('-', '')))
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validação básica de email"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Sanitiza entrada do usuário"""
        if not text:
            return ""
        
        # Remove caracteres perigosos
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        return text.strip()

class LogUtils:
    """Utilitários para logging"""
    
    @staticmethod
    def setup_logging(level: str = "INFO"):
        """Configura sistema de logging"""
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('crisiscomm.log')
            ]
        )
    
    @staticmethod
    def log_user_action(action: str, details: Dict = None):
        """Registra ação do usuário"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details or {}
        }
        logger.info(f"User action: {json.dumps(log_data)}")
    
    @staticmethod
    def log_error(error: Exception, context: str = ""):
        """Registra erro com contexto"""
        logger.error(f"Error in {context}: {str(error)}", exc_info=True)

class PerformanceUtils:
    """Utilitários para monitoramento de performance"""
    
    @staticmethod
    def measure_time(func):
        """Decorator para medir tempo de execução"""
        import time
        from functools import wraps
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            execution_time = end_time - start_time
            logger.info(f"Function {func.__name__} took {execution_time:.2f} seconds")
            
            return result
        return wrapper
    
    @staticmethod
    def get_memory_usage() -> Dict:
        """Obtém informações de uso de memória"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss": memory_info.rss,
                "vms": memory_info.vms,
                "percent": process.memory_percent(),
                "available": psutil.virtual_memory().available
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}
