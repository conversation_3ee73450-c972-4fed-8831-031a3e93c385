#ifndef _USER_SETTINGS_H
#define _USER_SETTINGS_H

/////////////////////////////  DRIVER CAPABILITIES  ////////////////////////////

//  The Cg compiler uses different "profiles" with different capabilities.
//  This shader requires a Cg compilation profile >= arbfp1, but a few options
//  require higher profiles like fp30 or fp40.  The shader can't detect profile
//  or driver capabilities, so instead you must comment or uncomment the lines
//  below with "//" before "#define."  Disable an option if you get compilation
//  errors resembling those listed.  Generally speaking, all of these options
//  will run on nVidia cards, but only _DRIVERS_ALLOW_TEX2DBIAS (if that) is
//  likely to run on ATI/AMD, due to the Cg compiler's profile limitations.

//  Derivatives: Unsupported on fp20, ps_1_1, ps_1_2, ps_1_3, and arbfp1.
//  Among other things, derivatives help us fix anisotropic filtering artifacts
//  with curved manually tiled phosphor mask coords.  Related errors:
//  error C3004: function "float2 ddx(float2);" not supported in this profile
//  error C3004: function "float2 ddy(float2);" not supported in this profile
#ifndef _DRIVERS_ALLOW_DERIVATIVES
    #define _DRIVERS_ALLOW_DERIVATIVES 0
#endif

//  Fine derivatives: Unsupported on older ATI cards.
//  Fine derivatives enable 2x2 fragment block communication, letting us perform
//  fast single-pass blur operations.  If your card uses coarse derivatives and
//  these are enabled, blurs could look broken.  Derivatives are a prerequisite.
#if _DRIVERS_ALLOW_DERIVATIVES
    #define _DRIVERS_ALLOW_FINE_DERIVATIVES
#endif

//  Dynamic looping: Requires an fp30 or newer profile.
//  This makes phosphor mask resampling faster in some cases.  Related errors:
//  error C5013: profile does not support "for" statements and "for" could not
//  be unrolled
#ifndef _DRIVERS_ALLOW_DYNAMIC_BRANCHES
    #define _DRIVERS_ALLOW_DYNAMIC_BRANCHES 0
#endif

//  Without _DRIVERS_ALLOW_DYNAMIC_BRANCHES, we need to use unrollable loops.
//  Using one static loop avoids overhead if the user is right, but if the user
//  is wrong (loops are allowed), breaking a loop into if-blocked pieces with a
//  binary search can potentially save some iterations.  However, it may fail:
//  error C6001: Temporary register limit of 32 exceeded; 35 registers
//  needed to compile program
#ifndef _ACCOMODATE_POSSIBLE_DYNAMIC_LOOPS
    #define _ACCOMODATE_POSSIBLE_DYNAMIC_LOOPS 0
#endif

//  tex2Dlod: Requires an fp40 or newer profile.  This can be used to disable
//  anisotropic filtering, thereby fixing related artifacts.  Related errors:
//  error C3004: function "float4 tex2Dlod(sampler2D, float4);" not supported in
//  this profile
// #ifndef _DRIVERS_ALLOW_TEX2DLOD
//     #define _DRIVERS_ALLOW_TEX2DLOD 1
// #endif

//  tex2Dbias: Requires an fp30 or newer profile.  This can be used to alleviate
//  artifacts from anisotropic filtering and mipmapping.  Related errors:
//  error C3004: function "float4 tex2Dbias(sampler2D, float4);" not supported
//  in this profile
// #ifndef _DRIVERS_ALLOW_TEX2DBIAS
//     #define _DRIVERS_ALLOW_TEX2DBIAS 0
// #endif

//  Integrated graphics compatibility: Integrated graphics like Intel HD 4000
//  impose stricter limitations on register counts and instructions.  Enable
//  _INTEGRATED_GRAPHICS_COMPATIBILITY_MODE if you still see error C6001 or:
//  error C6002: Instruction limit of 1024 exceeded: 1523 instructions needed
//  to compile program.
//  Enabling integrated graphics compatibility mode will automatically disable:
//  1.) _PHOSPHOR_MASK_MANUALLY_RESIZE: The phosphor mask will be softer.
//      (This may be reenabled in a later release.)
//  2.) _RUNTIME_GEOMETRY_MODE
//  3.) The high-quality 4x4 Gaussian resize for the bloom approximation
#ifndef _INTEGRATED_GRAPHICS_COMPATIBILITY_MODE
    #define _INTEGRATED_GRAPHICS_COMPATIBILITY_MODE 0
#endif


////////////////////////////  USER CODEPATH OPTIONS  ///////////////////////////

//  To disable a #define option, turn its line into a comment with "//."

//  RUNTIME VS. COMPILE-TIME OPTIONS (Major Performance Implications):
//  Enable runtime shader parameters in the Retroarch (etc.) GUI?  They override
//  many of the options in this file and allow real-time tuning, but many of
//  them are slower.  Disabling them and using this text file will boost FPS.
#ifndef _RUNTIME_SHADER_PARAMS_ENABLE
    #define _RUNTIME_SHADER_PARAMS_ENABLE 1
#endif
//  Specify the phosphor bloom sigma at runtime?  This option is 10% slower, but
//  it's the only way to do a wide-enough full bloom with a runtime dot pitch.
#ifndef _RUNTIME_PHOSPHOR_BLOOM_SIGMA
    #define _RUNTIME_PHOSPHOR_BLOOM_SIGMA 1
#endif
//  Specify antialiasing weight parameters at runtime?  (Costs ~20% with cubics)
#ifndef _RUNTIME_ANTIALIAS_WEIGHTS
    #define _RUNTIME_ANTIALIAS_WEIGHTS 1
#endif
//  Specify subpixel offsets at runtime? (WARNING: EXTREMELY EXPENSIVE!)
#ifndef _RUNTIME_ANTIALIAS_SUBPIXEL_OFFSETS
    #define _RUNTIME_ANTIALIAS_SUBPIXEL_OFFSETS 0
#endif
//  Make beam_horiz_filter and beam_horiz_linear_rgb_weight into runtime shader
//  parameters?  This will require more math or dynamic branching.
#ifndef _RUNTIME_SCANLINES_HORIZ_FILTER_COLORSPACE
    #define _RUNTIME_SCANLINES_HORIZ_FILTER_COLORSPACE 1
#endif
//  Specify the tilt at runtime?  This makes things about 3% slower.
//    akgunter:
//    This is used in crt-royale-geometry-aa-last-pass.fxh.
//    I've hard-coded it to 1 and hidden it from the UI in the ReShade version because
//    I don't know a good way to port that logic. If anyone ever does figure that
//    out, we can uncomment and port that logic and then unhide this definition.
#define _RUNTIME_GEOMETRY_TILT 1

//  Specify the geometry mode at runtime?
#ifndef _RUNTIME_GEOMETRY_MODE
    #define _RUNTIME_GEOMETRY_MODE 1
#endif
//  Specify the phosphor mask type (aperture grille, slot mask, shadow mask) and
//  mode (Lanczos-resize, hardware resize, or tile 1:1) at runtime, even without
//  dynamic branches?  This is cheap if mask_resize_viewport_scale is small.
// #ifndef FORCE_RUNTIME_PHOSPHOR_MASK_MODE_TYPE_SELECT
//     #define FORCE_RUNTIME_PHOSPHOR_MASK_MODE_TYPE_SELECT 1
// #endif

//  PHOSPHOR MASK:
//  Choose between a 64x64 or 512x512 source for the phosphor mask
//  Mainly affects Sample Mode 1
// #ifndef USE_LARGE_PHOSPHOR_MASK
//     #define USE_LARGE_PHOSPHOR_MASK 1
// #endif

//  Manually resize the phosphor mask for best results (slower)?  Disabling this
//  removes the option to do so, but it may be faster without dynamic branches.
#ifndef _PHOSPHOR_MASK_MANUALLY_RESIZE
    #define _PHOSPHOR_MASK_MANUALLY_RESIZE 1
#endif
//  If we sinc-resize the mask, should we Lanczos-window it (slower but better)?
// #ifndef PHOSPHOR_MASK_RESIZE_LANCZOS_WINDOW
//     #define PHOSPHOR_MASK_RESIZE_LANCZOS_WINDOW 1
// #endif
//  Larger blurs are expensive, but we need them to blur larger triads.  We can
//  detect the right blur if the triad size is static or our profile allows
//  dynamic branches, but otherwise we use the largest blur the user indicates
//  they might need:

#define _PHOSPHOR_BLOOM_TRIADS_LARGER_THAN_3_PIXELS 1
#define _PHOSPHOR_BLOOM_TRIADS_LARGER_THAN_6_PIXELS 2
#define _PHOSPHOR_BLOOM_TRIADS_LARGER_THAN_9_PIXELS 3
#define _PHOSPHOR_BLOOM_TRIADS_LARGER_THAN_12_PIXELS 4

#if !_RUNTIME_PHOSPHOR_BLOOM_SIGMA
    #ifndef PHOSPHOR_BLOOM_TRIAD_SIZE_MODE
        #define PHOSPHOR_BLOOM_TRIAD_SIZE_MODE _PHOSPHOR_BLOOM_TRIADS_LARGER_THAN_3_PIXELS  // [0 - 4]
    #endif
#endif

//  Here's a helpful chart:
//  MaxTriadSize    BlurSize    MinTriadCountsByResolution
//  3.0             9.0         480/640/960/1920 triads at 1080p/1440p/2160p/4320p, 4:3 aspect
//  6.0             17.0        240/320/480/960 triads at 1080p/1440p/2160p/4320p, 4:3 aspect
//  9.0             25.0        160/213/320/640 triads at 1080p/1440p/2160p/4320p, 4:3 aspect
//  12.0            31.0        120/160/240/480 triads at 1080p/1440p/2160p/4320p, 4:3 aspect
//  18.0            43.0        80/107/160/320 triads at 1080p/1440p/2160p/4320p, 4:3 aspect

///////////////////////////////  USER PARAMETERS  //////////////////////////////

//  Note: Many of these static parameters are overridden by runtime shader
//  parameters when those are enabled.  However, many others are static codepath
//  options that were cleaner or more convert to code as static constants.

//  GAMMA:
static const float crt_gamma_static = 2.5;                  //  range [1, 5]
static const float lcd_gamma_static = 2.2;                  //  range [1, 5]

//  LEVELS MANAGEMENT:
//  Control the final multiplicative image contrast:
static const float levels_contrast_static = 1.0;            //  range [0, 4)
//  We auto-dim to avoid clipping between passes and restore brightness
//  later.  Control the dim factor here: Lower values clip less but crush
//  blacks more (static only for now).
static const float levels_autodim_temp = 0.5;               //  range (0, 1] default is 0.5 but that was unnecessarily dark for me, so I set it to 1.0

//  HALATION/DIFFUSION/BLOOM:
//  Halation weight: How much energy should be lost to electrons bounding
//  around under the CRT glass and exciting random phosphors?
static const float halation_weight_static = 0.0;            //  range [0, 1]
//  Refractive diffusion weight: How much light should spread/diffuse from
//  refracting through the CRT glass?
static const float diffusion_weight_static = 0.075;         //  range [0, 1]
//  Underestimate brightness: Bright areas bloom more, but we can base the
//  bloom brightpass on a lower brightness to sharpen phosphors, or a higher
//  brightness to soften them.  Low values clip, but >= 0.8 looks okay.
static const float bloom_underestimate_levels_static = 0.8; //  range [0, 5]
//  Blur all colors more than necessary for a softer phosphor bloom?
static const float bloom_excess_static = 0.0;               //  range [0, 1]
//  The BLOOM_APPROX pass approximates a phosphor blur early on with a small
//  blurred resize of the input (convergence offsets are applied as well).
//  There are three filter options (static option only for now):
//  0.) Bilinear resize: A fast, close approximation to a 4x4 resize
//      if min_allowed_viewport_triads and the BLOOM_APPROX resolution are sane
//      and gaussian_beam_max_sigma is low.
//  1.) 3x3 resize blur: Medium speed, soft/smeared from bilinear blurring,
//      always uses a static sigma regardless of gaussian_beam_max_sigma or
//      mask_num_triads_across.
//  2.) True 4x4 Gaussian resize: Slowest, technically correct.
//  These options are more pronounced for the fast, unbloomed shader version.
#ifndef RADEON_FIX
    #define RADEON_FIX 0
#endif

#if !RADEON_FIX
    static const float bloom_approx_filter_static = 2.0;
#else
    static const float bloom_approx_filter_static = 1.0;
#endif

//  ELECTRON BEAM SCANLINE DISTRIBUTION:
//  How many scanlines should contribute light to each pixel?  Using more
//  scanlines is slower (especially for a generalized Gaussian) but less
//  distorted with larger beam sigmas (especially for a pure Gaussian).  The
//  max_beam_sigma at which the closest unused weight is guaranteed <
//  1.0/255.0 (for a 3x antialiased pure Gaussian) is:
//      2 scanlines: max_beam_sigma = 0.2089; distortions begin ~0.34; 141.7 FPS pure, 131.9 FPS generalized
//      3 scanlines, max_beam_sigma = 0.3879; distortions begin ~0.52; 137.5 FPS pure; 123.8 FPS generalized
//      4 scanlines, max_beam_sigma = 0.5723; distortions begin ~0.70; 134.7 FPS pure; 117.2 FPS generalized
//      5 scanlines, max_beam_sigma = 0.7591; distortions begin ~0.89; 131.6 FPS pure; 112.1 FPS generalized
//      6 scanlines, max_beam_sigma = 0.9483; distortions begin ~1.08; 127.9 FPS pure; 105.6 FPS generalized
static const float beam_num_scanlines = 3.0;                //  range [2, 6]
//  A generalized Gaussian beam varies shape with color too, now just width.
//  It's slower but more flexible (static option only for now).
static const bool beam_generalized_gaussian = true;
//  What kind of scanline antialiasing do you want?
//  0: Sample weights at 1x; 1: Sample weights at 3x; 2: Compute an integral
//  Integrals are slow (especially for generalized Gaussians) and rarely any
//  better than 3x antialiasing (static option only for now).
static const float beam_antialias_level = 1.0;              //  range [0, 2]
//  Min/max standard deviations for scanline beams: Higher values widen and
//  soften scanlines.  Depending on other options, low min sigmas can alias.
static const float gaussian_beam_min_sigma_static = 0.02;            //  range (0, 1]
static const float gaussian_beam_max_sigma_static = 0.3;             //  range (0, 1]
//  Beam width varies as a function of color: A power function (0) is more
//  configurable, but a spherical function (1) gives the widest beam
//  variability without aliasing (static option only for now).
static const float beam_spot_shape_function = 0.0;
//  Spot shape power: Powers <= 1 give smoother spot shapes but lower
//  sharpness.  Powers >= 1.0 are awful unless mix/max sigmas are close.
static const float gaussian_beam_spot_power_static = 1.0/3.0;    //  range (0, 16]
//  Generalized Gaussian max shape parameters: Higher values give flatter
//  scanline plateaus and steeper dropoffs, simultaneously widening and
//  sharpening scanlines at the cost of aliasing.  2.0 is pure Gaussian, and
//  values > ~40.0 cause artifacts with integrals.
static const float gaussian_beam_min_shape_static = 2.0;         //  range [2, 32]
static const float gaussian_beam_max_shape_static = 4.0;         //  range [2, 32]
//  Generalized Gaussian shape power: Affects how quickly the distribution
//  changes shape from Gaussian to steep/plateaued as color increases from 0
//  to 1.0.  Higher powers appear softer for most colors, and lower powers
//  appear sharper for most colors.
static const float gaussian_beam_shape_power_static = 1.0/4.0;   //  range (0, 16]
//  What filter should be used to sample scanlines horizontally?
//  0: Quilez (fast), 1: Gaussian (configurable), 2: Lanczos2 (sharp)
static const float beam_horiz_filter_static = 0.0;
//  Standard deviation for horizontal Gaussian resampling:
static const float beam_horiz_sigma_static = 0.35;      //  range (0, 2/3]
//  Do horizontal scanline sampling in linear RGB (correct light mixing),
//  gamma-encoded RGB (darker, hard spot shape, may better match bandwidth-
//  limiting circuitry in some CRT's), or a weighted avg.?
static const float beam_horiz_linear_rgb_weight_static = 1.0;   //  range [0, 1]
//  Simulate scanline misconvergence?  This needs 3x horizontal texture
//  samples and 3x texture samples of BLOOM_APPROX and HALATION_BLUR in
//  later passes (static option only for now).
static const bool beam_misconvergence = true;
//  Convergence offsets in x/y directions for R/G/B scanline beams in units
//  of scanlines.  Positive offsets go right/down; ranges [-2, 2]
static const float2 convergence_offsets_r_static = float2(0.1, 0.2);
static const float2 convergence_offsets_g_static = float2(0.3, 0.4);
static const float2 convergence_offsets_b_static = float2(0.5, 0.6);
//  Detect interlacing (static option only for now)?
static const bool interlace_detect = true;
//  Assume 1080-line sources are interlaced?
static const bool interlace_1080i_static = false;
//  For interlaced sources, assume TFF (top-field first) or BFF order?
//  (Whether this matters depends on the nature of the interlaced input.)
static const bool interlace_back_field_first_static = false;

//  ANTIALIASING:
//  What AA level do you want for curvature/overscan/subpixels?  Options:
//  0x (none), 1x (sample subpixels), 4x, 5x, 6x, 7x, 8x, 12x, 16x, 20x, 24x
//  (Static option only for now)
#ifndef antialias_level
    #define antialias_level 0.0
#endif
// static const float aa_level = 12.0;                     //  range [0, 24]
// static const float aa_level = 0.0;                     //  range [0, 24]
//  What antialiasing filter do you want (static option only)?  Options:
//  0: Box (separable), 1: Box (cylindrical),
//  2: Tent (separable), 3: Tent (cylindrical),
//  4: Gaussian (separable), 5: Gaussian (cylindrical),
//  6: Cubic* (separable), 7: Cubic* (cylindrical, poor)
//  8: Lanczos Sinc (separable), 9: Lanczos Jinc (cylindrical, poor)
//      * = Especially slow with _RUNTIME_ANTIALIAS_WEIGHTS
#ifndef antialias_filter
    #define antialias_filter 6
#endif
static const float aa_filter = antialias_filter;                     //  range [0, 9]
//  Flip the sample grid on odd/even frames (static option only for now)?
#ifndef antialias_temporal
    #define antialias_temporal false
#endif
static const bool aa_temporal = antialias_temporal;
//  Use RGB subpixel offsets for antialiasing?  The pixel is at green, and
//  the blue offset is the negative r offset; range [0, 0.5]
static const float2 aa_subpixel_r_offset_static = float2(-1.0/3.0, 0.0);//float2(0.0);
//  Cubics: See http://www.imagemagick.org/Usage/filter/#mitchell
//  1.) "Keys cubics" with B = 1 - 2C are considered the highest quality.
//  2.) C = 0.5 (default) is Catmull-Rom; higher C's apply sharpening.
//  3.) C = 1.0/3.0 is the Mitchell-Netravali filter.
//  4.) C = 0.0 is a soft spline filter.
static const float aa_cubic_c_static = 0.5;             //  range [0, 4]
//  Standard deviation for Gaussian antialiasing: Try 0.5/aa_pixel_diameter.
static const float aa_gauss_sigma_static = 0.5;     //  range [0.0625, 1.0]

//  PHOSPHOR MASK:
//  Mask type: 0 = aperture grille, 1 = slot mask, 2 = shadow mask
//  3 = lowres grille, 4 = lowres slot, 5 = lowres shadow
static const float mask_type_static = 4.0;                  //  range [0, 5]
//  We can sample the mask three ways.  Pick 2/3 from: Pretty/Fast/Flexible.
//  0.) Sinc-resize to the desired dot pitch manually (pretty/slow/flexible).
//      This requires _PHOSPHOR_MASK_MANUALLY_RESIZE to be #defined.
//  1.) Hardware-resize to the desired dot pitch (ugly/fast/flexible).  This
//      is halfway decent with LUT mipmapping but atrocious without it.
//  2.) Tile it without resizing at a 1:1 texel:pixel ratio for flat coords
//      (pretty/fast/inflexible).  Each input LUT has a fixed dot pitch.
//      This mode reuses the same masks, so triads will be enormous unless
//      you change the mask LUT filenames in your .cgp file.
static const float mask_sample_mode_static = 0.0;           //  range [0, 2]
//  Prefer setting the triad size (0.0) or number on the screen (1.0)?
//  If _RUNTIME_PHOSPHOR_BLOOM_SIGMA isn't #defined, the specified triad size
//  will always be used to calculate the full bloom sigma statically.
static const float mask_size_param_static = 0.0;    //  range [0, 1]
//  Specify the phosphor triad size, in pixels.  Each tile (usually with 8
//  triads) will be rounded to the nearest integer tile size and clamped to
//  obey minimum size constraints (imposed to reduce downsize taps) and
//  maximum size constraints (imposed to have a sane MASK_RESIZE FBO size).
//  To increase the size limit, double the viewport-relative scales for the
//  two MASK_RESIZE passes in crt-royale.cgp and user-cgp-contants.h.
//      range [1, mask_texture_small_size/mask_triads_per_tile]
static const float mask_triad_width_static = 24.0 / 8.0;
//  If mask_size_param is 1.0/true, we'll go by this instead (the
//  final size will be rounded and constrained as above); default 480.0
static const float mask_num_triads_across_static = 480.0;
//  How many lobes should the sinc/Lanczos resizer use?  More lobes require
//  more samples and avoid moire a bit better, but some is unavoidable
//  depending on the destination size (static option for now).
static const float mask_sinc_lobes = 3.0;                   //  range [2, 4]
//  The mask is resized using a variable number of taps in each dimension,
//  but some Cg profiles always fetch a constant number of taps no matter
//  what (no dynamic branching).  We can limit the maximum number of taps if
//  we statically limit the minimum phosphor triad size.  Larger values are
//  faster, but the limit IS enforced (static option only, forever);
//      range [1, mask_texture_small_size/mask_triads_per_tile]
//  TODO: Make this 1.0 and compensate with smarter sampling!
static const float mask_min_allowed_triad_size = 2.0;

//  GEOMETRY:
//  Geometry mode:
//  0: Off (default), 1: Spherical mapping (like cgwg's),
//  2: Alt. spherical mapping (more bulbous), 3: Cylindrical/Trinitron
static const float geom_mode_static = 0.0;      //  range [0, 3]
//  Radius of curvature: Measured in units of your viewport's diagonal size.
static const float geom_radius_static = 2.0;    //  range [1/(2*pi), 1024]
//  View dist is the distance from the player to their physical screen, in
//  units of the viewport's diagonal size.  It controls the field of view.
static const float geom_view_dist_static = 2.0; //  range [0.5, 1024]
//  Tilt angle in radians (clockwise around up and right vectors):
static const float2 geom_tilt_angle_static = float2(0.0, 0.0);  //  range [-pi, pi]
//  Aspect ratio: When the true viewport size is unknown, this value is used
//  to help convert between the phosphor triad size and count, along with
//  the mask_resize_viewport_scale constant from user-cgp-constants.h.  Set
//  this equal to Retroarch's display aspect ratio (DAR) for best results;
//  range [1, geom_max_aspect_ratio from user-cgp-constants.h];
//  default (256/224)*(54/47) = 1.313069909 (see below)
static const float geom_aspect_ratio_static = 1.313069909;
//  Before getting into overscan, here's some general aspect ratio info:
//  - DAR = display aspect ratio = SAR * PAR; as in your Retroarch setting
//  - SAR = storage aspect ratio = DAR / PAR; square pixel emulator frame AR
//  - PAR = pixel aspect ratio   = DAR / SAR; holds regardless of cropping
//  Geometry processing has to "undo" the screen-space 2D DAR to calculate
//  3D view vectors, then reapplies the aspect ratio to the simulated CRT in
//  uv-space.  To ensure the source SAR is intended for a ~4:3 DAR, either:
//  a.) Enable Retroarch's "Crop Overscan"
//  b.) Readd horizontal padding: Set overscan to e.g. N*(1.0, 240.0/224.0)
//  Real consoles use horizontal black padding in the signal, but emulators
//  often crop this without cropping the vertical padding; a 256x224 [S]NES
//  frame (8:7 SAR) is intended for a ~4:3 DAR, but a 256x240 frame is not.
//  The correct [S]NES PAR is 54:47, found by blargg and NewRisingSun:
//      http://board.zsnes.com/phpBB3/viewtopic.php?f=22&t=11928&start=50
//      http://forums.nesdev.com/viewtopic.php?p=24815#p24815
//  For flat output, it's okay to set DAR = [existing] SAR * [correct] PAR
//  without doing a. or b., but horizontal image borders will be tighter
//  than vertical ones, messing up curvature and overscan.  Fixing the
//  padding first corrects this.
//  Overscan: Amount to "zoom in" before cropping.  You can zoom uniformly
//  or adjust x/y independently to e.g. readd horizontal padding, as noted
//  above: Values < 1.0 zoom out; range (0, inf)
static const float2 geom_overscan_static = float2(1.0, 1.0);// * 1.005 * (1.0, 240/224.0)
//  Compute a proper pixel-space to texture-space matrix even without ddx()/
//  ddy()?  This is ~8.5% slower but improves antialiasing/subpixel filtering
//  with strong curvature (static option only for now).
static const bool geom_force_correct_tangent_matrix = true;

//  BORDERS:
//  Rounded border size in texture uv coords:
static const float border_size_static = 0.015;           //  range [0, 0.5]
//  Border darkness: Moderate values darken the border smoothly, and high
//  values make the image very dark just inside the border:
static const float border_darkness_static = 2.0;        //  range [0, inf)
//  Border compression: High numbers compress border transitions, narrowing
//  the dark border area.
static const float border_compress_static = 2.5;        //  range [1, inf)

// TODO: Nuke this
#define mask_size_xy float2(512, 512)

#endif  //  _USER_SETTINGS_H