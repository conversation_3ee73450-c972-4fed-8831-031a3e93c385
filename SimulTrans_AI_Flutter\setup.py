#!/usr/bin/env python3
"""
Script de configuração para CrisisComm
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Exibe banner do CrisisComm"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🚨 CrisisComm - Comunicador de Emergência Multilíngue    ║
    ║                                                              ║
    ║    Sistema de comunicação para situações de crise           ║
    ║    Powered by Google Gemma 3n + Streamlit                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Verifica versão do Python"""
    print("🔍 Verificando versão do Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário. Versão atual:", sys.version)
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detectado")

def check_system_requirements():
    """Verifica requisitos do sistema"""
    print("\n🔍 Verificando requisitos do sistema...")
    
    # Verificar sistema operacional
    os_name = platform.system()
    print(f"📱 Sistema operacional: {os_name}")
    
    # Verificar memória (se psutil estiver disponível)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"💾 Memória RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  Aviso: Recomendado pelo menos 4GB de RAM")
    except ImportError:
        print("💾 Memória RAM: Não foi possível verificar")
    
    # Verificar GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🎮 GPU detectada: {gpu_name}")
        else:
            print("🎮 GPU: Não detectada (CPU será usado)")
    except ImportError:
        print("🎮 GPU: PyTorch não instalado ainda")

def create_directories():
    """Cria diretórios necessários"""
    print("\n📁 Criando diretórios...")
    
    directories = [
        "cache",
        "cache/huggingface",
        "temp",
        "logs",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}/")

def install_requirements():
    """Instala dependências"""
    print("\n📦 Instalando dependências...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependências instaladas com sucesso!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        sys.exit(1)

def create_env_file():
    """Cria arquivo .env se não existir"""
    print("\n⚙️  Configurando variáveis de ambiente...")
    
    env_file = Path(".env")
    
    if not env_file.exists():
        env_content = """# CrisisComm - Configurações de Ambiente

# Hugging Face Token (OBRIGATÓRIO para Gemma 3n)
# Obtenha em: https://huggingface.co/settings/tokens
HUGGING_FACE_TOKEN=

# Configurações de dispositivo
DEVICE=auto  # auto, cpu, cuda
USE_GPU=True

# Configurações de desenvolvimento
DEBUG=False
LOG_LEVEL=INFO

# Configurações de cache
CACHE_DIR=./cache
TEMP_DIR=./temp
HF_CACHE_DIR=./cache/huggingface

# Configurações de segurança
MAX_FILE_SIZE_MB=50
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Configurações do modelo
MODEL_NAME=google/gemma-3n-e2b-it
MAX_NEW_TOKENS=500
TEMPERATURE=0.1
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ Arquivo .env criado")
    else:
        print("✅ Arquivo .env já existe")

def test_installation():
    """Testa a instalação"""
    print("\n🧪 Testando instalação...")
    
    try:
        # Testar imports principais
        import streamlit
        print("✅ Streamlit importado")
        
        import torch
        print("✅ PyTorch importado")
        
        import transformers
        print("✅ Transformers importado")
        
        import PIL
        print("✅ PIL importado")
        
        # Testar módulos do projeto
        from config import CrisisCommConfig
        print("✅ Configurações carregadas")
        
        from utils import FileUtils
        print("✅ Utilitários carregados")
        
        print("\n🎉 Instalação testada com sucesso!")
        
    except ImportError as e:
        print(f"❌ Erro na importação: {e}")
        return False
    
    return True

def show_next_steps():
    """Mostra próximos passos"""
    print("\n" + "="*60)
    print("🚀 INSTALAÇÃO CONCLUÍDA!")
    print("="*60)
    
    print("\n📋 Próximos passos:")
    print("1. Execute o aplicativo:")
    print("   streamlit run crisiscomm_app.py")
    print("\n2. Acesse no navegador:")
    print("   http://localhost:8501")
    print("\n3. Na primeira execução:")
    print("   - O modelo Gemma 3n será baixado automaticamente")
    print("   - Isso pode levar alguns minutos")
    print("   - Certifique-se de ter conexão com internet")
    
    print("\n💡 Dicas:")
    print("- Use GPU para melhor performance")
    print("- Configure HUGGING_FACE_TOKEN no .env se necessário")
    print("- Consulte README_CRISISCOMM.md para documentação completa")
    
    print("\n🆘 Suporte:")
    print("- GitHub Issues: https://github.com/seu-usuario/crisiscomm/issues")
    print("- Email: <EMAIL>")

def main():
    """Função principal"""
    print_banner()
    
    try:
        check_python_version()
        check_system_requirements()
        create_directories()
        create_env_file()
        install_requirements()
        
        if test_installation():
            show_next_steps()
        else:
            print("\n❌ Instalação falhou. Verifique os erros acima.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Instalação cancelada pelo usuário.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
