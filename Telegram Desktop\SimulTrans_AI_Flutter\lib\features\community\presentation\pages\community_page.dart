import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/community_service.dart';
import '../../../../core/models/community_feedback.dart';
import '../../../../core/models/translation_improvement.dart';
import '../../../../core/models/user_contribution.dart';
import '../widgets/feedback_submission_dialog.dart';
import '../widgets/improvement_card.dart';
import '../widgets/user_profile_card.dart';
import '../widgets/leaderboard_widget.dart';

/// Community page for collaborative translation improvements
class CommunityPage extends ConsumerStatefulWidget {
  const CommunityPage({super.key});

  @override
  ConsumerState<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends ConsumerState<CommunityPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<TranslationImprovement> _improvements = [];
  List<UserContribution> _leaderboard = [];
  Map<String, dynamic> _userStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCommunityData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCommunityData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load improvements
      final improvements = await CommunityService.instance.getTranslationImprovements(
        sourceLanguage: 'en',
        targetLanguage: 'pt',
        limit: 20,
      );

      // Load leaderboard
      final leaderboard = await CommunityService.instance.getLeaderboard(
        period: 'month',
        limit: 10,
      );

      // Load user stats
      final userStats = await CommunityService.instance.getUserStats();

      setState(() {
        _improvements = improvements;
        _leaderboard = leaderboard;
        _userStats = userStats;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Erro ao carregar dados da comunidade: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comunidade'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.lightbulb), text: 'Melhorias'),
            Tab(icon: Icon(Icons.leaderboard), text: 'Ranking'),
            Tab(icon: Icon(Icons.person), text: 'Perfil'),
            Tab(icon: Icon(Icons.trending_up), text: 'Tendências'),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _showFeedbackDialog,
            icon: const Icon(Icons.add_comment),
            tooltip: 'Enviar Feedback',
          ),
          IconButton(
            onPressed: _loadCommunityData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Atualizar',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildImprovementsTab(),
                _buildLeaderboardTab(),
                _buildProfileTab(),
                _buildTrendsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showFeedbackDialog,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
        tooltip: 'Contribuir',
      ),
    );
  }

  Widget _buildImprovementsTab() {
    if (_improvements.isEmpty) {
      return _buildEmptyState(
        icon: Icons.lightbulb_outline,
        title: 'Nenhuma melhoria encontrada',
        subtitle: 'Seja o primeiro a contribuir com melhorias!',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCommunityData,
      child: AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _improvements.length,
          itemBuilder: (context, index) {
            final improvement = _improvements[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: ImprovementCard(
                    improvement: improvement,
                    onVote: (isUpvote) => _voteOnImprovement(improvement.id, isUpvote),
                    onReport: () => _reportImprovement(improvement.id),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLeaderboardTab() {
    return RefreshIndicator(
      onRefresh: _loadCommunityData,
      child: LeaderboardWidget(
        users: _leaderboard,
        currentUserId: CommunityService.instance.userId,
      ),
    );
  }

  Widget _buildProfileTab() {
    return RefreshIndicator(
      onRefresh: _loadCommunityData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            UserProfileCard(
              userStats: _userStats,
              onEditProfile: _editProfile,
            ),
            const SizedBox(height: 16),
            _buildContributionHistory(),
            const SizedBox(height: 16),
            _buildAchievements(),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendsTab() {
    return RefreshIndicator(
      onRefresh: _loadCommunityData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTrendingLanguagePairs(),
            const SizedBox(height: 16),
            _buildPopularImprovements(),
            const SizedBox(height: 16),
            _buildCommunityStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContributionHistory() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Histórico de Contribuições',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            // Placeholder for contribution chart
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text('Gráfico de contribuições em desenvolvimento'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievements() {
    final theme = Theme.of(context);
    final badges = _userStats['badges'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conquistas',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            if (badges.isEmpty)
              Text(
                'Nenhuma conquista ainda. Continue contribuindo!',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: badges.map((badge) {
                  return Chip(
                    label: Text(badge.toString()),
                    backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                    labelStyle: TextStyle(color: AppTheme.primaryColor),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendingLanguagePairs() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pares de Idiomas em Alta',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            // Placeholder for trending pairs
            const Text('Carregando tendências...'),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularImprovements() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Melhorias Populares',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            // Show top improvements
            if (_improvements.isNotEmpty)
              ..._improvements.take(3).map((improvement) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                    child: Text(
                      improvement.voteScore.toString(),
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    improvement.improvedTranslation,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    '${improvement.sourceLanguage} → ${improvement.targetLanguage}',
                  ),
                  trailing: Icon(
                    improvement.isVerified ? Icons.verified : Icons.thumb_up,
                    color: improvement.isVerified ? Colors.green : Colors.grey,
                  ),
                );
              }).toList()
            else
              const Text('Nenhuma melhoria disponível'),
          ],
        ),
      ),
    );
  }

  Widget _buildCommunityStats() {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Estatísticas da Comunidade',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Contribuições',
                    '${_improvements.length}',
                    Icons.edit,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Contribuidores',
                    '${_leaderboard.length}',
                    Icons.people,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Idiomas',
                    '140+',
                    Icons.language,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _showFeedbackDialog() {
    showDialog(
      context: context,
      builder: (context) => FeedbackSubmissionDialog(
        onSubmit: _submitFeedback,
      ),
    );
  }

  Future<void> _submitFeedback(CommunityFeedback feedback) async {
    try {
      final success = await CommunityService.instance.submitTranslationFeedback(
        originalText: feedback.originalText,
        translatedText: feedback.translatedText,
        sourceLanguage: feedback.sourceLanguage,
        targetLanguage: feedback.targetLanguage,
        feedbackType: feedback.feedbackType,
        improvedTranslation: feedback.improvedTranslation,
        comment: feedback.comment,
        rating: feedback.rating,
      );

      if (success) {
        _showSuccessSnackBar('Feedback enviado com sucesso!');
        _loadCommunityData(); // Refresh data
      } else {
        _showInfoSnackBar('Feedback salvo e será enviado quando possível');
      }

    } catch (e) {
      _showErrorSnackBar('Erro ao enviar feedback: $e');
    }
  }

  Future<void> _voteOnImprovement(String improvementId, bool isUpvote) async {
    try {
      final success = await CommunityService.instance.voteOnImprovement(
        improvementId: improvementId,
        isUpvote: isUpvote,
      );

      if (success) {
        _showSuccessSnackBar('Voto registrado!');
        _loadCommunityData(); // Refresh to show updated votes
      } else {
        _showErrorSnackBar('Erro ao registrar voto');
      }

    } catch (e) {
      _showErrorSnackBar('Erro ao votar: $e');
    }
  }

  Future<void> _reportImprovement(String improvementId) async {
    // Show report dialog
    final reason = await _showReportDialog();
    if (reason == null) return;

    try {
      final success = await CommunityService.instance.reportContent(
        contentId: improvementId,
        contentType: 'improvement',
        reason: reason,
      );

      if (success) {
        _showSuccessSnackBar('Conteúdo reportado');
      } else {
        _showErrorSnackBar('Erro ao reportar conteúdo');
      }

    } catch (e) {
      _showErrorSnackBar('Erro ao reportar: $e');
    }
  }

  Future<String?> _showReportDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reportar Conteúdo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Conteúdo inadequado'),
              onTap: () => Navigator.of(context).pop('inappropriate'),
            ),
            ListTile(
              title: const Text('Spam'),
              onTap: () => Navigator.of(context).pop('spam'),
            ),
            ListTile(
              title: const Text('Tradução incorreta'),
              onTap: () => Navigator.of(context).pop('incorrect'),
            ),
            ListTile(
              title: const Text('Outro'),
              onTap: () => Navigator.of(context).pop('other'),
            ),
          ],
        ),
      ),
    );
  }

  void _editProfile() {
    _showInfoSnackBar('Edição de perfil em desenvolvimento');
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
