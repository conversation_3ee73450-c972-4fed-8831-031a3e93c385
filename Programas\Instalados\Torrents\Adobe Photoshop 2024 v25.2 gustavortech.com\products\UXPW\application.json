{"Name": "UXP WebView Support", "SAPCode": "UXPW", "CodexVersion": "1.1.0", "AssetGuid": "b68eeac2-90d5-46e7-ba65-ac70aa49e7e3", "ProductVersion": "********", "BaseVersion": "1.0.0", "LbsUrl": "/AdobeProducts/UXPW/1_1/win64/AAMmetadataLS20/CreativeCloudSet-Up.exe", "Platform": "win64", "SupportedLanguages": {"Language": [{"value": "", "locale": "mul"}]}, "LanguageSet": "LS20", "AMTConfig": {"appID": "{UXPW-1.1.0-64-ADBEADBEADBEADBEADBEAD}"}, "Packages": {"Package": [{"Type": "core", "PackageName": "AdobeUXPWebViewSupport_x64-mul", "PackageVersion": "********", "DownloadSize": 120125959, "ExtractSize": 342373450, "Path": "/AdobeProducts/UXPW/********/win64/b68eeac2-90d5-46e7-ba65-ac70aa49e7e3/AdobeUXPWebViewSupport_x64-mul.zip", "Features": {"Feature": []}, "Format": "zip", "ValidationURL": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/32101", "packageHashKey": "82fa682b4b175b14aac4727d16e6edca", "DeltaPackages": [], "ValidationURLs": {"TYPE2": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/32101?algorithm=TYPE2", "TYPE1": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/32101?algorithm=TYPE1"}, "ProcessorFamily": "64-bit", "InstallSequenceNumber": 1001, "fullPackageName": "AdobeUXPWebViewSupport_x64-mul.zip", "AliasPackageName": "1", "PackageScheme": "hd-standard"}]}, "SystemRequirement": {"OsVersion": {"min": "10.0.0.14392"}, "CheckCompatibility": {"Content": "var minOSVersion=\"10.0.0.14392\";var minOSVersionString=\"Minimum supported OS version is Windows 10 1809 (10.0.0.14392)\";function compareVersion(f,d){if(f===d){return 0}var j=f.split(\".\");var e=d.split(\".\");var c=Math.max(j.length,e.length);for(var h=0;h<c;h++){var k=0;var g=0;if(h<j.length){k=parseInt(j[h])}if(h<e.length){g=parseInt(e[h])}if(k>g){return 1}if(k<g){return -1}}return 0}function checkCompatibility(i,d){var b={};b.checkResult=\"success\";b.failingList=[];var g,a;try{g=JSON.parse(i);a=JSON.parse(d)}catch(f){b.failingList.push({type:\"generic\",message:\"Error parsing JSON\"});return JSON.stringify(b)}var c=a.os;var h=c.version+\".\"+c.buildNumber;if(h&&compareVersion(h,minOSVersion)<0){b.failingList.push({type:\"OS\",message:minOSVersionString});b.checkResult=\"fail\"}return JSON.stringify(b)};"}}, "version": "1.1.0", "AppLineage": "UXP WebView Support", "FamilyName": "UXP WebView Support", "BuildGuid": "97fa51c4-6b14-49bc-84a2-36609d65497e", "HDBuilderVersion": "4.0.27", "IsSTI": true, "Cdn": {"Secure": "https://ccmdls.adobe.com", "NonSecure": "http://ccmdl.adobe.com"}, "WhatsNewUrl": {"Prod": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}, "Stage": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}}, "TutorialUrl": {"Prod": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}, "Stage": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}}, "InstallDir": {"maxPath": "WidevineCdm\\_platform_specific\\win_x64\\widevinecdm.dll.sig", "value": "[AdobeCommon]/Microsoft/EdgeWebView", "isFixed": "1"}, "MoreInfoUrl": {"Prod": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}, "Stage": {"Language": [{"locale": "mul", "value": "https://www.adobe.com/go/uxp-webview-support"}]}}, "AddRemoveInfo": {"URLInfoAbout": {"Language": [{"locale": "en_US", "value": "https://www.adobe.com/go/uxp-webview-support"}]}, "DisplayName": {"Language": [{"locale": "en_US", "value": "UXP WebView Support"}]}, "DisplayIcon": "[INSTALLDIR]/icons/icon_pkg.ico", "DisplayVersion": {"Language": [{"locale": "en_US", "value": "1.1.0"}]}}, "AutoUpdate": "true", "AppsPanelPreviousVersionConfig": {"ListInPreviousVersion": false, "BrandingName": "CC"}, "ProductDescription": {"Tagline": {"Language": [{"locale": "en_US", "value": "UXP WebView Support"}]}, "DetailedDescription": {"Language": [{"locale": "en_US", "value": "UXP WebView Support"}]}}, "IsNonCCProduct": false, "CompressionType": "Zip-Lzma2", "IsFreeProduct": true, "MinimumSupportedClientVersion": "4.5.0.300"}