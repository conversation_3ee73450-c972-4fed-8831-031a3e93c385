# 🏥 DermatoGemma Multi-Detection System v2.0

Revolutionary AI-powered dermatological analysis system with modern interface for multi-condition skin lesion detection and assessment.

## 🎯 Key Features

- **🔬 Multi-Condition Analysis**: Simultaneous detection of 14 dermatological conditions
- **🎨 Modern Professional UI**: Clean, intuitive interface with medical design standards
- **🤖 AI-Powered Analysis**: Advanced analysis using Gemma 3n architecture
- **📊 ABCDE Methodology**: Comprehensive melanoma screening
- **⚡ Real-time Processing**: Fast analysis with progress tracking
- **🏥 Medical Compliance**: Designed for research and clinical screening

## 🏥 Supported Conditions

### 🔴 **High Priority (Malignant)**
- **Melanoma** - Most aggressive skin cancer
- **Basal Cell Carcinoma** - Most common skin cancer
- **Squamous Cell Carcinoma** - Second most common skin cancer

### 🟡 **Medium Priority (Precancerous/Infectious)**
- **Actinic Keratoses** - Precancerous lesions
- **Monkeypox** - Viral infection
- **Measles** - Viral exanthem
- **Chickenpox** - Varicella infection
- **Cowpox** - Poxvirus infection
- **Hand, Foot & Mouth Disease** - Enteroviral infection

### 🟢 **Low Priority (Benign)**
- **Melanocytic Nevi** - Common moles
- **Benign Keratosis-like Lesions** - Benign growths
- **Dermatofibroma** - Fibrous skin lesions
- **Vascular Lesions** - Blood vessel abnormalities
- **Healthy Skin Assessment** - Normal skin evaluation

## 🚀 Quick Start

### System Requirements
- **Python**: 3.8 to 3.11
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 2GB free space
- **OS**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

### Installation

#### Option 1: Automated Installation (Recommended)
```bash
git clone <repository-url>
cd DermatoGemma-MultiDetection
python install.py
```

#### Option 2: Manual Installation
```bash
# Create virtual environment
python -m venv venv

# Activate environment
venv\Scripts\activate          # Windows
source venv/bin/activate       # Linux/Mac

# Install dependencies
pip install -r requirements.txt
```

### Running the Application
```bash
# Launch modern UI (recommended)
python main.py --modern

# Auto-detection mode
python main.py

# Show help
python main.py --help
```

## 🎨 User Interface Guide

### 🎯 **Analysis Target Selection**
Choose your analysis approach:

#### 🔬 **Comprehensive Analysis**
- Analyzes all 14 conditions simultaneously
- Complete medical assessment
- Best for general screening

#### 🎯 **Targeted Analysis**
- Focus on specific condition
- Select from categorized disease list
- Optimized for known concerns

### 📋 **Workflow**
1. **📸 Load Image**: Select dermatological image (JPEG, PNG, BMP, TIFF)
2. **🎯 Choose Mode**: Comprehensive or Targeted analysis
3. **⚙️ Configure**: Enable ABCDE analysis, AI features
4. **🚀 Analyze**: Start unified analysis process
5. **📊 Review**: Detailed medical assessment and recommendations

## 🔧 Technical Architecture

### Core Components
```
┌─────────────────────────────────────┐
│         Modern UI Layer             │
├─────────────────────────────────────┤
│      Multi-Condition Engine         │
├─────────────────────────────────────┤
│  Gemma Handler │ ABCDE │ Skin       │
│     (AI)       │ Analyzer│ Detector │
├─────────────────────────────────────┤
│        Medical Validation           │
└─────────────────────────────────────┘
```

### Key Dependencies
- **Computer Vision**: OpenCV, Pillow, scikit-image
- **Machine Learning**: scikit-learn
- **GUI Framework**: CustomTkinter
- **Scientific Computing**: NumPy, SciPy
- **Utilities**: Requests, PyYAML, tqdm, psutil

## 📊 Performance Specifications

| Metric | Specification |
|--------|---------------|
| **Analysis Time** | 30-60 seconds per image |
| **Memory Usage** | 2-4GB during analysis |
| **Image Formats** | JPEG, PNG, BMP, TIFF |
| **Resolution Range** | 224x224 to 2048x2048 pixels |
| **Concurrent Analysis** | Single image processing |

## 🏥 Medical Compliance & Safety

### ⚠️ **Important Medical Disclaimers**
- **Research Use Only**: Not FDA approved for clinical diagnosis
- **Physician Oversight Required**: All results require medical professional review
- **Screening Tool**: Assists but does not replace medical examination
- **Privacy Compliant**: All processing happens locally (no data transmission)

### 🔒 **Data Security**
- Local processing ensures patient privacy
- No internet connection required for analysis
- Compliant with HIPAA privacy requirements
- Audit trails for medical record keeping

## 🛠️ Development & Testing

### Project Structure
```
DermatoGemma-MultiDetection/
├── 📄 main.py                    # Application entry point
├── 🎨 ui_modern.py               # Modern UI interface
├── 📦 requirements.txt           # Dependencies
├── 🚀 install.py                 # Automated installer
├── 📁 core/                      # Core analysis modules
├── 📁 data/                      # Configurations & test data
├── 📁 models/                    # AI models (local storage)
├── 📁 results/                   # Analysis outputs
└── 📁 logs/                      # Application logs
```

### Testing Commands
```bash
# Test installation
python test_sidebar_structure.py

# Interactive UI test
python test_new_sidebar.py

# Full system test
python main.py --modern
```

## 📈 Version History

### v2.0 - Modern UI Edition (Current)
- ✅ **Sidebar Redesign**: Eliminated redundancies, direct disease selection
- ✅ **Medical Categorization**: Diseases organized by clinical priority
- ✅ **Enhanced UX**: Intuitive interface with visual priority indicators
- ✅ **Unified Analysis**: Single-button comprehensive processing
- ✅ **Performance Optimization**: Improved loading and analysis speed

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork** the repository
2. **Create** feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Open** Pull Request

## 📄 License

Licensed under the Medical Research License. See LICENSE file for details.

## 🆘 Support & Resources

- **📚 Documentation**: Comprehensive inline documentation
- **🐛 Bug Reports**: GitHub Issues
- **💬 Discussions**: GitHub Discussions
- **🏥 Medical Questions**: Consult qualified healthcare professionals

## 🙏 Acknowledgments

- **Google Gemma Team**: For the foundational AI model architecture
- **Medical Professionals**: For clinical guidance and validation
- **Open Source Community**: For tools, libraries, and frameworks

---

**⚠️ MEDICAL DISCLAIMER**: This software is for research and educational purposes only. It is not intended for use as a medical device or for primary clinical diagnosis. Always consult qualified healthcare professionals for medical decisions and treatment planning.
