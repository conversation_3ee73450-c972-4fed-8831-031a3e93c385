# CrisisComm - Arquivo de Configuração de Ambiente
# Copie este arquivo para .env e configure suas variáveis

# ========================================
# HUGGING FACE (OBRIGATÓRIO)
# ========================================
# Token do Hugging Face para acessar o modelo Gemma 3n
# Obtenha em: https://huggingface.co/settings/tokens
HUGGING_FACE_TOKEN=*************************************

# ========================================
# CONFIGURAÇÕES DE DISPOSITIVO
# ========================================
# Dispositivo para executar o modelo
DEVICE=auto  # auto, cpu, cuda

# Usar GPU se disponível
USE_GPU=True

# ========================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# ========================================
# Modo debug
DEBUG=False

# Nível de log
LOG_LEVEL=INFO

# ========================================
# CONFIGURAÇÕES DE CACHE
# ========================================
# Diretório de cache geral
CACHE_DIR=./cache

# Diretório temporário
TEMP_DIR=./temp

# Cache do Hugging Face
HF_CACHE_DIR=./cache/huggingface

# ========================================
# CONFIGURAÇÕES DO MODELO
# ========================================
# Nome do modelo
MODEL_NAME=google/gemma-3n-e2b-it

# Máximo de tokens para geração
MAX_NEW_TOKENS=500

# Temperatura para geração
TEMPERATURE=0.1

# ========================================
# CONFIGURAÇÕES DE SEGURANÇA
# ========================================
# Tamanho máximo de arquivo (MB)
MAX_FILE_SIZE_MB=50

# Limite de requisições por minuto
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Timeout de sessão (minutos)
SESSION_TIMEOUT_MINUTES=30

# ========================================
# CONFIGURAÇÕES DE INTERFACE
# ========================================
# Porta do Streamlit
STREAMLIT_SERVER_PORT=8501

# Endereço do servidor
STREAMLIT_SERVER_ADDRESS=0.0.0.0

# Modo headless
STREAMLIT_SERVER_HEADLESS=true

# Coleta de estatísticas
STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
