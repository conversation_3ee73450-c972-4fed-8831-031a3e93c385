#!/usr/bin/env python3
"""
Script para baixar e verificar o modelo Gemma 3n
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def print_banner():
    """Exibe banner"""
    print("\n" + "="*60)
    print("📥 CrisisComm - Download do Modelo Gemma 3n")
    print("="*60)

def check_token():
    """Verifica se o token do HF está configurado"""
    load_dotenv()
    
    token = os.getenv('HUGGING_FACE_TOKEN') or os.getenv('HF_TOKEN')
    
    if not token:
        print("❌ Token do Hugging Face não encontrado!")
        print("\n🔑 Para configurar:")
        print("1. Acesse: https://huggingface.co/settings/tokens")
        print("2. Crie um token (Read access é suficiente)")
        print("3. Adicione ao arquivo .env:")
        print("   HUGGING_FACE_TOKEN=seu_token_aqui")
        return False
    
    print(f"✅ Token encontrado: {token[:8]}...")
    return token

def setup_huggingface():
    """Configura autenticação do Hugging Face"""
    try:
        from huggingface_hub import login, whoami
        
        token = check_token()
        if not token:
            return False
        
        print("🔐 Fazendo login no Hugging Face...")
        login(token=token, add_to_git_credential=False)
        
        # Verificar se login funcionou
        user_info = whoami()
        print(f"✅ Login realizado como: {user_info['name']}")
        return True
        
    except ImportError:
        print("❌ huggingface_hub não instalado")
        print("Instale com: pip install huggingface_hub")
        return False
    except Exception as e:
        print(f"❌ Erro no login: {e}")
        return False

def check_model_access():
    """Verifica acesso ao modelo"""
    try:
        from huggingface_hub import model_info
        
        model_name = "google/gemma-3n-e2b-it"
        print(f"🔍 Verificando acesso ao modelo {model_name}...")
        
        info = model_info(model_name)
        print(f"✅ Modelo encontrado: {info.modelId}")
        print(f"📊 Tamanho aproximado: {info.safetensors['total'] / (1024**3):.1f} GB")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao acessar modelo: {e}")
        print("\n💡 Possíveis soluções:")
        print("1. Verifique se aceitou os termos em: https://huggingface.co/google/gemma-3n-e2b-it")
        print("2. Verifique seu token do Hugging Face")
        print("3. Certifique-se de ter conexão com internet")
        return False

def download_model():
    """Baixa o modelo"""
    try:
        from transformers import AutoProcessor, Gemma3nForConditionalGeneration
        
        model_name = "google/gemma-3n-e2b-it"
        cache_dir = "./cache/huggingface"
        
        # Criar diretório de cache
        Path(cache_dir).mkdir(parents=True, exist_ok=True)
        
        print(f"📥 Baixando modelo {model_name}...")
        print("⏳ Isso pode levar 10-30 minutos dependendo da sua conexão...")
        
        # Baixar processor primeiro (menor)
        print("📝 Baixando processor...")
        processor = AutoProcessor.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True
        )
        print("✅ Processor baixado!")
        
        # Baixar modelo
        print("🧠 Baixando modelo (pode demorar)...")
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True,
            torch_dtype="auto",
            device_map=None  # Não carregar na GPU ainda
        )
        print("✅ Modelo baixado!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no download: {e}")
        return False

def verify_installation():
    """Verifica se o modelo foi baixado corretamente"""
    try:
        from transformers import AutoProcessor, Gemma3nForConditionalGeneration
        
        model_name = "google/gemma-3n-e2b-it"
        cache_dir = "./cache/huggingface"
        
        print("🔍 Verificando instalação...")
        
        # Tentar carregar sem baixar
        processor = AutoProcessor.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            local_files_only=True
        )
        
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            local_files_only=True,
            torch_dtype="auto",
            device_map=None
        )
        
        print("✅ Modelo verificado e pronto para uso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False

def show_usage_info():
    """Mostra informações de uso"""
    print("\n" + "="*60)
    print("🎉 MODELO PRONTO PARA USO!")
    print("="*60)
    
    print("\n📋 Próximos passos:")
    print("1. Execute o CrisisComm:")
    print("   python run_crisiscomm.py")
    print("\n2. Ou diretamente:")
    print("   streamlit run crisiscomm_app.py")
    
    print("\n💡 Dicas:")
    print("- O modelo está salvo em ./cache/huggingface")
    print("- Não precisa baixar novamente")
    print("- Use GPU para melhor performance")
    
    print("\n🔧 Configurações no .env:")
    print("- USE_GPU=True (para usar GPU)")
    print("- USE_GPU=False (para usar apenas CPU)")

def main():
    """Função principal"""
    print_banner()
    
    try:
        # Verificar dependências
        print("🔍 Verificando dependências...")
        try:
            import torch
            import transformers
            import huggingface_hub
            print("✅ Dependências OK")
        except ImportError as e:
            print(f"❌ Dependência faltando: {e}")
            print("Execute: pip install -r requirements.txt")
            return
        
        # Configurar HF
        if not setup_huggingface():
            return
        
        # Verificar acesso ao modelo
        if not check_model_access():
            return
        
        # Perguntar se deseja baixar
        print("\n" + "="*60)
        response = input("📥 Deseja baixar o modelo agora? (s/N): ").lower()
        
        if response in ['s', 'sim', 'y', 'yes']:
            if download_model():
                if verify_installation():
                    show_usage_info()
                else:
                    print("❌ Falha na verificação do modelo")
            else:
                print("❌ Falha no download do modelo")
        else:
            print("👋 Download cancelado. Execute novamente quando quiser baixar.")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Download cancelado pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")

if __name__ == "__main__":
    main()
