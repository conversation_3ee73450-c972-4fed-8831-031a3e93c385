import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import 'dart:io';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:camera/camera.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

import '../../../../core/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/real_translation_service.dart';
import '../../../../core/config/api_keys.dart';
import '../../../../core/models/translation_result.dart';

/// Splash screen with initialization and loading animations
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<Offset> _slideAnimation;

  String _currentStatus = 'Inicializando...';
  double _progress = 0.0;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) _textController.forward();
    });
  }

  Future<void> _initializeApp() async {
    try {
      // Step 1: Initialize core services
      _updateStatus('Inicializando serviços...', 0.2);
      await Future.delayed(const Duration(milliseconds: 500));

      // Step 2: Initialize translation service
      _updateStatus('Carregando Google Gemini...', 0.4);
      await RealTranslationService.instance.initialize();
      await Future.delayed(const Duration(milliseconds: 500));

      // Step 3: Load user preferences
      _updateStatus('Carregando preferências...', 0.7);
      await Future.delayed(const Duration(milliseconds: 500));

      // Step 4: Complete
      _updateStatus('Pronto!', 1.0);
      await Future.delayed(const Duration(milliseconds: 500));

      _completeInitialization();

    } catch (e) {
      _setError('Erro na inicialização: $e');
    }
  }

  void _updateStatus(String status, double progress) {
    if (mounted) {
      setState(() {
        _currentStatus = status;
        _progress = progress;
      });
    }
  }

  void _setError(String error) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _errorMessage = error;
      });
    }
  }

  void _completeInitialization() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const SimpleHomePage(),
          ),
        );
      }
    });
  }

  void _retryInitialization() {
    setState(() {
      _hasError = false;
      _errorMessage = null;
      _progress = 0.0;
    });
    _initializeApp();
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo Animation
                    ScaleTransition(
                      scale: _logoAnimation,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.translate,
                          size: 60,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // App Title Animation
                    SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _textAnimation,
                        child: Column(
                          children: [
                            Text(
                              AppConfig.appName,
                              style: theme.textTheme.displayMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppConfig.appDescription,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 60),
                    
                    // Status and Progress
                    if (!_hasError) ...[
                      // Status Text
                      FadeTransition(
                        opacity: _textAnimation,
                        child: Text(
                          _currentStatus,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Progress Bar
                      Container(
                        width: 200,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _progress,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 10),
                      
                      // Progress Percentage
                      Text(
                        '${(_progress * 100).toInt()}%',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                    
                    // Error State
                    if (_hasError) ...[
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 40),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.white,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Erro na Inicialização',
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _errorMessage ?? 'Erro desconhecido',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 20),
                            ElevatedButton(
                              onPressed: _retryInitialization,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: AppTheme.primaryColor,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 12,
                                ),
                              ),
                              child: const Text('Tentar Novamente'),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Footer
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      'Powered by Google Gemma 3N',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'v${AppConfig.appVersion}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
    );
  }




}

/// Simple home page for testing
class SimpleHomePage extends StatefulWidget {
  const SimpleHomePage({super.key});

  @override
  State<SimpleHomePage> createState() => _SimpleHomePageState();
}

class _SimpleHomePageState extends State<SimpleHomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _textController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  String _sourceLanguage = 'auto';
  String _targetLanguage = 'pt';
  String _translatedText = '';
  bool _isTranslating = false;

  // Image translation variables
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;
  TranslationResult? _imageTranslationResult;

  // Audio translation variables
  Uint8List? _selectedAudioBytes;
  String? _selectedAudioName;
  String? _audioTranslationResult;
  bool _isRecording = false;
  bool _isPlayingAudio = false;

  // Audio recording and playback
  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  String? _recordedAudioPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackDuration = Duration.zero;
  Duration _totalDuration = Duration.zero;

  // Video translation variables
  Uint8List? _selectedVideoBytes;
  String? _selectedVideoName;
  String? _videoTranslationResult;
  bool _isRecordingVideo = false;
  bool _isPlayingVideo = false;

  // Video recording and playback
  CameraController? _cameraController;
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  String? _recordedVideoPath;
  List<CameraDescription>? _cameras;

  // Language options
  final List<Map<String, String>> _languages = [
    {'code': 'auto', 'name': 'Detectar idioma', 'flag': '🌐'},
    {'code': 'pt', 'name': 'Português', 'flag': '🇧🇷'},
    {'code': 'en', 'name': 'English', 'flag': '🇺🇸'},
    {'code': 'es', 'name': 'Español', 'flag': '🇪🇸'},
    {'code': 'fr', 'name': 'Français', 'flag': '🇫🇷'},
    {'code': 'de', 'name': 'Deutsch', 'flag': '🇩🇪'},
    {'code': 'it', 'name': 'Italiano', 'flag': '🇮🇹'},
    {'code': 'ja', 'name': '日本語', 'flag': '🇯🇵'},
    {'code': 'ko', 'name': '한국어', 'flag': '🇰🇷'},
    {'code': 'zh', 'name': '中文', 'flag': '🇨🇳'},
    {'code': 'ru', 'name': 'Русский', 'flag': '🇷🇺'},
    {'code': 'ar', 'name': 'العربية', 'flag': '🇸🇦'},
    {'code': 'hi', 'name': 'हिन्दी', 'flag': '🇮🇳'},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeCameras();
  }

  Future<void> _initializeCameras() async {
    try {
      // Verificar se estamos na web
      if (kIsWeb) {
        // Silencioso na web - câmera não é suportada
        return;
      }
      _cameras = await availableCameras();
    } catch (e) {
      // Log silencioso - não mostrar erro para o usuário
      if (kDebugMode) {
        debugPrint('Camera initialization failed: $e');
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    _cameraController?.dispose();
    _videoPlayerController?.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,

      // Material 3 compliant AppBar
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // M3 Icon Button style
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.translate_rounded,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'SimulTrans AI',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
        actions: [
          // M3 Icon Button for API Key
          if (!ApiKeys.isGoogleAIConfigured)
            IconButton.filled(
              onPressed: _showApiKeyInstructions,
              icon: const Icon(Icons.key_rounded),
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.errorContainer,
                foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
              ),
              tooltip: 'Configurar API Key',
            ),

          // M3 Icon Button for About
          IconButton.outlined(
            onPressed: _showAbout,
            icon: const Icon(Icons.info_outline_rounded),
            tooltip: 'Sobre',
          ),

          const SizedBox(width: 8),
        ],

        // M3 TabBar
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
          indicatorColor: Theme.of(context).colorScheme.primary,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.label,
          dividerColor: Theme.of(context).colorScheme.outlineVariant,
          labelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
          tabs: const [
            Tab(
              icon: Icon(Icons.text_fields_rounded),
              text: 'Texto',
            ),
            Tab(
              icon: Icon(Icons.image_rounded),
              text: 'Imagem',
            ),
            Tab(
              icon: Icon(Icons.mic_rounded),
              text: 'Áudio',
            ),
            Tab(
              icon: Icon(Icons.videocam_rounded),
              text: 'Vídeo',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTextTranslationTab(),
          _buildImageTranslationTab(),
          _buildAudioTranslationTab(),
          _buildVideoTranslationTab(),
        ],
      ),
      // M3 Extended FAB
      floatingActionButton: _isTranslating
          ? FloatingActionButton(
              onPressed: null,
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  strokeWidth: 3,
                ),
              ),
            )
          : FloatingActionButton.extended(
              onPressed: _performTranslation,
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
              icon: const Icon(Icons.auto_awesome_rounded),
              label: const Text('Traduzir'),
              elevation: 6,
              extendedPadding: const EdgeInsets.symmetric(horizontal: 24),
            ),
    );
  }

  Widget _buildTextTranslationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // M3 Language Selector Card
          Card(
            elevation: 0,
            color: Theme.of(context).colorScheme.surfaceContainerLow,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Source Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _sourceLanguage,
                      label: const Text('De'),
                      leadingIcon: const Icon(Icons.language_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'auto', label: 'Detectar automaticamente'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _sourceLanguage = value;
                          });
                        }
                      },
                    ),
                  ),

                  // Swap Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: IconButton.filledTonal(
                      onPressed: _sourceLanguage != 'auto' ? () {
                        setState(() {
                          final temp = _sourceLanguage;
                          _sourceLanguage = _targetLanguage;
                          _targetLanguage = temp;
                        });
                      } : null,
                      icon: const Icon(Icons.swap_horiz_rounded),
                      tooltip: 'Trocar idiomas',
                    ),
                  ),

                  // Target Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _targetLanguage,
                      label: const Text('Para'),
                      leadingIcon: const Icon(Icons.translate_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _targetLanguage = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // M3 Input Text Card
          SizedBox(
            height: 200,
            child: Card(
              elevation: 0,
              color: Theme.of(context).colorScheme.surfaceContainerLow,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.edit_rounded,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Texto Original',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        maxLines: null,
                        expands: true,
                        decoration: InputDecoration(
                          hintText: 'Digite o texto para traduzir...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).colorScheme.surface,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // M3 Output Text Card
          SizedBox(
            height: 200,
            child: Card(
              elevation: 0,
              color: _translatedText.isEmpty
                  ? Theme.of(context).colorScheme.surfaceContainerLow
                  : Theme.of(context).colorScheme.primaryContainer,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.translate_rounded,
                          color: _translatedText.isEmpty
                              ? Theme.of(context).colorScheme.onSurfaceVariant
                              : Theme.of(context).colorScheme.onPrimaryContainer,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Tradução',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: _translatedText.isEmpty
                                ? Theme.of(context).colorScheme.onSurface
                                : Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const Spacer(),
                        if (_translatedText.isNotEmpty)
                          Chip(
                            label: const Text('Concluída'),
                            avatar: const Icon(Icons.check_circle_rounded, size: 16),
                            backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                            labelStyle: TextStyle(
                              color: Theme.of(context).colorScheme.onSecondaryContainer,
                              fontSize: 12,
                            ),
                            side: BorderSide.none,
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _translatedText.isEmpty
                                ? Theme.of(context).colorScheme.outline
                                : Theme.of(context).colorScheme.primary,
                            width: _translatedText.isEmpty ? 1 : 2,
                          ),
                        ),
                        child: SingleChildScrollView(
                          child: SelectableText(
                            _translatedText.isEmpty
                                ? 'A tradução aparecerá aqui...'
                                : _translatedText,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: _translatedText.isEmpty
                                  ? Theme.of(context).colorScheme.onSurfaceVariant
                                  : Theme.of(context).colorScheme.onSurface,
                              fontWeight: _translatedText.isEmpty
                                  ? FontWeight.normal
                                  : FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // M3 Action Buttons
          Row(
            children: [
              Expanded(
                child: FilledButton.icon(
                  onPressed: _isTranslating ? null : _translateText,
                  icon: _isTranslating
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                        )
                      : const Icon(Icons.translate_rounded),
                  label: Text(_isTranslating ? 'Traduzindo...' : 'Traduzir'),
                ),
              ),
              const SizedBox(width: 12),
              OutlinedButton.icon(
                onPressed: _clearText,
                icon: const Icon(Icons.clear_rounded),
                label: const Text('Limpar'),
              ),
              if (_translatedText.isNotEmpty) ...[
                const SizedBox(width: 12),
                FilledButton.tonalIcon(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: _translatedText));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Tradução copiada!'),
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy_rounded),
                  label: const Text('Copiar'),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageTranslationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // M3 Language Selector Card - igual à aba de texto
          Card(
            elevation: 0,
            color: Theme.of(context).colorScheme.surfaceContainerLow,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Source Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _sourceLanguage,
                      label: const Text('De'),
                      leadingIcon: const Icon(Icons.language_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'auto', label: 'Detectar automaticamente'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _sourceLanguage = value;
                          });
                        }
                      },
                    ),
                  ),

                  // Swap Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: IconButton.filledTonal(
                      onPressed: _sourceLanguage != 'auto' ? () {
                        setState(() {
                          final temp = _sourceLanguage;
                          _sourceLanguage = _targetLanguage;
                          _targetLanguage = temp;
                        });
                      } : null,
                      icon: const Icon(Icons.swap_horiz_rounded),
                      tooltip: 'Trocar idiomas',
                    ),
                  ),

                  // Target Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _targetLanguage,
                      label: const Text('Para'),
                      leadingIcon: const Icon(Icons.translate_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _targetLanguage = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // M3 Image Selection Card
          SizedBox(
            height: 400,
            child: Card(
              elevation: 0,
              color: _selectedImageBytes != null
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceContainerLow,
              clipBehavior: Clip.antiAlias,
              child: _selectedImageBytes != null
                  ? _buildSelectedImageWidget()
                  : _buildImagePlaceholder(),
            ),
          ),

          const SizedBox(height: 16),

          // M3 Action Buttons for Image
          Row(
            children: [
              Expanded(
                child: FilledButton.icon(
                  onPressed: _showImageSourceDialog,
                  icon: Icon(
                    _selectedImageBytes != null
                        ? Icons.swap_horiz_rounded
                        : Icons.add_photo_alternate_rounded,
                  ),
                  label: Text(_selectedImageBytes != null ? 'Trocar Imagem' : 'Selecionar Imagem'),
                ),
              ),
              if (_selectedImageBytes != null) ...[
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: _clearImage,
                  icon: const Icon(Icons.delete_outline_rounded),
                  label: const Text('Remover'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Translation result area
          SizedBox(
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildImageTranslationResult(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedImageWidget() {
    return Column(
      children: [
        // M3 Image display area
        SizedBox(
          height: 300,
          child: Stack(
            children: [
              // Image
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.memory(
                    _selectedImageBytes!,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // M3 Close button
              Positioned(
                top: 8,
                right: 8,
                child: IconButton.filledTonal(
                  onPressed: _clearImage,
                  icon: const Icon(Icons.close_rounded),
                  iconSize: 20,
                  tooltip: 'Remover imagem',
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // M3 Image info card
        Card(
          elevation: 0,
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  Icons.image_rounded,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedImageName ?? 'Imagem selecionada',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        '${(_selectedImageBytes!.length / 1024).toStringAsFixed(1)} KB • Pronta para tradução',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.check_circle_rounded,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImagePlaceholder() {
    return InkWell(
      onTap: _showImageSourceDialog,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // M3 Icon with container
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.add_photo_alternate_rounded,
                size: 48,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'Adicionar Imagem',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Subtitle
            Text(
              'Toque para selecionar da câmera ou galeria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // M3 Feature chips
            Wrap(
              spacing: 8,
              children: [
                Chip(
                  avatar: const Icon(Icons.image_rounded, size: 16),
                  label: const Text('JPG, PNG'),
                  backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                  side: BorderSide.none,
                ),
                Chip(
                  avatar: const Icon(Icons.auto_awesome_rounded, size: 16),
                  label: const Text('IA Avançada'),
                  backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
                  side: BorderSide.none,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageTranslationResult() {
    // Estado de loading
    if (_isTranslating) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced loading animation with Material 3 design
            Stack(
              alignment: Alignment.center,
              children: [
                // Outer ring with subtle animation
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Color.lerp(
                      Theme.of(context).colorScheme.outline,
                      Colors.transparent,
                      0.7,
                    )!,
                    value: 1.0,
                  ),
                ),
                // Main progress indicator
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    color: Theme.of(context).colorScheme.primary,
                    strokeCap: StrokeCap.round,
                  ),
                ),
                // Center icon with animation
                TweenAnimationBuilder<double>(
                  tween: Tween(begin: 0.8, end: 1.2),
                  duration: const Duration(milliseconds: 1000),
                  curve: Curves.easeInOut,
                  builder: (context, scale, child) {
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(
                                Theme.of(context).colorScheme.primary,
                                Colors.transparent,
                                0.7,
                              )!,
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.auto_awesome_rounded,
                          size: 32,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Enhanced title with gradient effect
            ShaderMask(
              shaderCallback: (bounds) => LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ).createShader(bounds),
              child: Text(
                'Traduzindo com IA',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 12),

            // Animated subtitle with typing effect
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              child: Text(
                _getLoadingMessage(),
                key: ValueKey(_getLoadingMessage()),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 24),

            // Progress steps indicator
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildProgressStep(
                  context,
                  Icons.image_search_rounded,
                  'Analisando',
                  true,
                ),
                Container(
                  width: 24,
                  height: 2,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
                _buildProgressStep(
                  context,
                  Icons.translate_rounded,
                  'Traduzindo',
                  true,
                ),
                Container(
                  width: 24,
                  height: 2,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
                _buildProgressStep(
                  context,
                  Icons.check_circle_rounded,
                  'Concluído',
                  false,
                ),
              ],
            ),
          ],
        ),
      );
    }

    // Estado vazio
    if (_imageTranslationResult == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.translate_rounded,
                size: 32,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Resultado da tradução aparecerá aqui',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedImageBytes != null) ...[
              const SizedBox(height: 16),
              FilledButton.icon(
                onPressed: _translateImage,
                icon: const Icon(Icons.translate_rounded),
                label: const Text('Traduzir Imagem'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ],
        ),
      );
    }

    // Estado com resultado - apenas tradução, bem estruturada
    return SizedBox(
      height: 300,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header com ícone de sucesso
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.check_circle_rounded,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tradução Concluída',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Confiança: ${(_imageTranslationResult!.confidence * 100).toInt()}%',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Tradução principal
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.surface,
                    Theme.of(context).colorScheme.surfaceContainerLow,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color.lerp(
                      Theme.of(context).colorScheme.primary,
                      Colors.transparent,
                      0.9,
                    )!,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.translate_rounded,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tradução',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SelectableText(
                    _imageTranslationResult!.translatedText,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                      height: 1.6,
                      letterSpacing: 0.2,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Botões de ação
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: _imageTranslationResult!.translatedText));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              Icon(
                                Icons.check_circle_rounded,
                                color: Theme.of(context).colorScheme.onInverseSurface,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text('Tradução copiada!'),
                            ],
                          ),
                          backgroundColor: Theme.of(context).colorScheme.inverseSurface,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.copy_rounded),
                    label: const Text('Copiar Tradução'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FilledButton.icon(
                    onPressed: _translateImage,
                    icon: const Icon(Icons.refresh_rounded),
                    label: const Text('Nova Tradução'),
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderTab(String title, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text('Tradução de $title'),
          const SizedBox(height: 8),
          const Text('Em desenvolvimento...', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  Future<void> _performTranslation() async {
    if (_textController.text.trim().isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Digite um texto para traduzir')),
        );
      }
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      // Use real translation service
      final result = await RealTranslationService.instance.translateText(
        text: _textController.text.trim(),
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      setState(() {
        _translatedText = result.translatedText;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tradução concluída! (Confiança: ${(result.confidence * 100).toInt()}%)'),
            backgroundColor: result.confidence > 0.7 ? Colors.green : Colors.orange,
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro na tradução: $e')),
        );
      }
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  // Image translation methods
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final imageBytes = await image.readAsBytes();
        setState(() {
          _selectedImageBytes = imageBytes;
          _selectedImageName = image.name;
          // Don't automatically clear translation result
          // Let user decide when to translate
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Imagem selecionada: ${image.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao selecionar imagem: $e')),
        );
      }
    }
  }

  void _clearImage() {
    setState(() {
      _selectedImageBytes = null;
      _selectedImageName = null;
      _imageTranslationResult = null;
    });
  }

  // Helper method for loading messages
  String _getLoadingMessage() {
    final messages = [
      'Analisando a imagem...',
      'Extraindo texto...',
      'Processando com IA...',
      'Traduzindo conteúdo...',
      'Finalizando tradução...',
    ];

    // Cycle through messages every 2 seconds
    final index = (DateTime.now().millisecondsSinceEpoch ~/ 2000) % messages.length;
    return messages[index];
  }

  // Helper method for progress steps
  Widget _buildProgressStep(BuildContext context, IconData icon, String label, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isActive
                ? Theme.of(context).colorScheme.primaryContainer
                : Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: isActive
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Icon(
            icon,
            size: 16,
            color: isActive
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: isActive
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Audio Translation Tab Implementation
  Widget _buildAudioTranslationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // M3 Language Selector Card - igual à aba de texto
          Card(
            elevation: 0,
            color: Theme.of(context).colorScheme.surfaceContainerLow,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Source Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _sourceLanguage,
                      label: const Text('De'),
                      leadingIcon: const Icon(Icons.language_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'auto', label: 'Detectar automaticamente'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _sourceLanguage = value;
                          });
                        }
                      },
                    ),
                  ),

                  // Swap Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: IconButton.filledTonal(
                      onPressed: _sourceLanguage != 'auto' ? () {
                        setState(() {
                          final temp = _sourceLanguage;
                          _sourceLanguage = _targetLanguage;
                          _targetLanguage = temp;
                        });
                      } : null,
                      icon: const Icon(Icons.swap_horiz_rounded),
                      tooltip: 'Trocar idiomas',
                    ),
                  ),

                  // Target Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _targetLanguage,
                      label: const Text('Para'),
                      leadingIcon: const Icon(Icons.translate_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _targetLanguage = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // M3 Audio Selection Card
          SizedBox(
            height: 400,
            child: Card(
              elevation: 0,
              color: _selectedAudioBytes != null
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceContainerLow,
              clipBehavior: Clip.antiAlias,
              child: _selectedAudioBytes != null
                  ? _buildSelectedAudioWidget()
                  : _buildAudioPlaceholder(),
            ),
          ),

          const SizedBox(height: 16),

          // M3 Action Buttons for Audio
          Row(
            children: [
              Expanded(
                child: FilledButton.icon(
                  onPressed: _showAudioSourceDialog,
                  icon: Icon(
                    _selectedAudioBytes != null
                        ? Icons.swap_horiz_rounded
                        : Icons.mic_rounded,
                  ),
                  label: Text(_selectedAudioBytes != null ? 'Trocar Áudio' : 'Gravar/Selecionar'),
                ),
              ),
              if (_selectedAudioBytes != null) ...[
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: _clearAudio,
                  icon: const Icon(Icons.delete_outline_rounded),
                  label: const Text('Remover'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Audio translation result area
          SizedBox(
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Color.lerp(Theme.of(context).colorScheme.outline, Colors.transparent, 0.7)!,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildAudioTranslationResult(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build feature chips
  Widget _buildFeatureChip(BuildContext context, IconData icon, String label, Color backgroundColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Color.lerp(Theme.of(context).colorScheme.outline, Colors.transparent, 0.8)!,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _translateText() async {
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Digite um texto para traduzir')),
      );
      return;
    }

    setState(() {
      _isTranslating = true;
      _translatedText = '';
    });

    try {
      final result = await RealTranslationService.instance.translateText(
        text: _textController.text.trim(),
        targetLanguage: _targetLanguage,
        sourceLanguage: _sourceLanguage != 'auto' ? _sourceLanguage : 'auto',
      );

      if (mounted) {
        setState(() {
          _translatedText = result.translatedText;
          _isTranslating = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro na tradução: $e')),
        );
      }
    }
  }

  void _clearText() {
    setState(() {
      _textController.clear();
      _translatedText = '';
    });
  }

  Future<void> _translateImage() async {
    print('🖼️ _translateImage called');

    if (_selectedImageBytes == null) {
      print('❌ No image selected');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Selecione uma imagem primeiro')),
        );
      }
      return;
    }

    print('✅ Image selected, size: ${_selectedImageBytes!.length} bytes');
    print('🌍 Target language: $_targetLanguage');
    print('🔤 Source language: $_sourceLanguage');

    setState(() {
      _isTranslating = true;
    });

    try {
      print('🚀 Starting image translation...');

      final result = await RealTranslationService.instance.translateImage(
        imageBytes: _selectedImageBytes!,
        targetLanguage: _targetLanguage,
        sourceLanguage: _sourceLanguage != 'auto' ? _sourceLanguage : null,
        additionalContext: 'Extract and translate any text found in this image',
      );

      print('✅ Translation completed!');
      print('📝 Original: ${result.originalText}');
      print('🎯 Translated: ${result.translatedText}');
      print('📊 Confidence: ${result.confidence}');

      print('🔄 Calling setState to update UI...');
      setState(() {
        _imageTranslationResult = result;
        print('✅ _imageTranslationResult set: ${_imageTranslationResult != null}');
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tradução de imagem concluída! (Confiança: ${(result.confidence * 100).toInt()}%)'),
            backgroundColor: result.confidence > 0.7 ? Colors.green : Colors.orange,
          ),
        );
      }

    } catch (e) {
      print('❌ Translation error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro na tradução de imagem: $e')),
        );
      }
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Selecionar Imagem'),
        content: const Text('Escolha a fonte da imagem:'),
        actions: [
          TextButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _pickImage(ImageSource.camera);
            },
            icon: const Icon(Icons.camera_alt),
            label: const Text('Câmera'),
          ),
          TextButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _pickImage(ImageSource.gallery);
            },
            icon: const Icon(Icons.photo_library),
            label: const Text('Galeria'),
          ),
        ],
      ),
    );
  }

  void _showApiKeyInstructions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.key, color: Colors.orange),
            SizedBox(width: 8),
            Text('Configurar Google AI API'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Para usar tradução real com Google Gemini, você precisa configurar uma API key:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 16),
              const Text(ApiKeyInstructions.googleAI),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Sem a API key, o app usará tradução básica de fallback.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Entendi'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.translate, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('SimulTrans AI'),
          ],
        ),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Tradutor Multimodal Inteligente',
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
            ),
            const SizedBox(height: 16),
            const Text('🤖 Powered by Google Gemma 3M'),
            const Text('🌍 Suporte a 140+ idiomas'),
            const Text('📱 Interface multimodal'),
            const Text('⚡ Tradução em tempo real'),
            const Text('🔒 Processamento seguro'),
            const Text('🎯 IA de última geração'),
            const Text('📁 Configuração via .env'),
            const SizedBox(height: 16),
            Text(
              'Versão ${ApiKeys.appVersion}',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              'Modelo: ${ApiKeys.geminiModel}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  // Audio Placeholder Widget
  Widget _buildAudioPlaceholder() {
    return InkWell(
      onTap: _showAudioSourceDialog,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // M3 Icon with container
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.mic_rounded,
                size: 48,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'Gravar ou Selecionar Áudio',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Subtitle
            Text(
              'Grave sua voz ou selecione um arquivo de áudio',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // M3 Feature chips
            Wrap(
              spacing: 8,
              children: [
                Chip(
                  avatar: const Icon(Icons.mic_rounded, size: 16),
                  label: const Text('Gravação'),
                  backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                  side: BorderSide.none,
                ),
                Chip(
                  avatar: const Icon(Icons.audio_file_rounded, size: 16),
                  label: const Text('MP3, WAV'),
                  backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
                  side: BorderSide.none,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Selected Audio Widget
  Widget _buildSelectedAudioWidget() {
    return Column(
      children: [
        // M3 Audio display area
        SizedBox(
          height: 300,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Audio icon with animation
                TweenAnimationBuilder<double>(
                  tween: Tween(begin: 0.8, end: 1.2),
                  duration: const Duration(milliseconds: 1000),
                  curve: Curves.easeInOut,
                  builder: (context, scale, child) {
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(32),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(
                                Theme.of(context).colorScheme.primary,
                                Colors.transparent,
                                0.7,
                              )!,
                              blurRadius: 16,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Icon(
                          _isPlayingAudio ? Icons.volume_up_rounded : Icons.audio_file_rounded,
                          size: 64,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // Audio controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton.filledTonal(
                      onPressed: _toggleAudioPlayback,
                      icon: Icon(
                        _isPlayingAudio ? Icons.pause_rounded : Icons.play_arrow_rounded,
                      ),
                      iconSize: 32,
                      tooltip: _isPlayingAudio ? 'Pausar' : 'Reproduzir',
                    ),
                    const SizedBox(width: 16),
                    IconButton.filledTonal(
                      onPressed: _clearAudio,
                      icon: const Icon(Icons.close_rounded),
                      iconSize: 20,
                      tooltip: 'Remover áudio',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 12),

        // M3 Audio info card
        Card(
          elevation: 0,
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  Icons.audio_file_rounded,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedAudioName ?? 'Áudio selecionado',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        children: [
                          Text(
                            '${(_selectedAudioBytes!.length / 1024).toStringAsFixed(1)} KB',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                          if (_totalDuration.inSeconds > 0) ...[
                            Text(
                              ' • ${_totalDuration.inMinutes}:${(_totalDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimaryContainer,
                              ),
                            ),
                          ],
                          Text(
                            ' • Pronto para tradução',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.check_circle_rounded,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Audio Translation Result Widget
  Widget _buildAudioTranslationResult() {
    // Estado de loading
    if (_isTranslating) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced loading animation for audio
            Stack(
              alignment: Alignment.center,
              children: [
                // Outer ring with audio waves
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Color.lerp(
                      Theme.of(context).colorScheme.outline,
                      Colors.transparent,
                      0.7,
                    )!,
                    value: 1.0,
                  ),
                ),
                // Main progress indicator
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    color: Theme.of(context).colorScheme.primary,
                    strokeCap: StrokeCap.round,
                  ),
                ),
                // Center icon with animation
                TweenAnimationBuilder<double>(
                  tween: Tween(begin: 0.8, end: 1.2),
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeInOut,
                  builder: (context, scale, child) {
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(
                                Theme.of(context).colorScheme.primary,
                                Colors.transparent,
                                0.7,
                              )!,
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.hearing_rounded,
                          size: 32,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Enhanced title
            Text(
              'Processando Áudio',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Animated subtitle
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              child: Text(
                _getAudioLoadingMessage(),
                key: ValueKey(_getAudioLoadingMessage()),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    // Estado vazio
    if (_audioTranslationResult == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.volume_off_rounded,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Nenhuma tradução ainda',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Selecione um áudio para começar',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedAudioBytes != null) ...[
              const SizedBox(height: 16),
              FilledButton.icon(
                onPressed: _translateAudio,
                icon: const Icon(Icons.translate_rounded),
                label: const Text('Traduzir Áudio'),
              ),
            ],
          ],
        ),
      );
    }

    // Estado com resultado - apenas tradução, bem estruturada
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header com status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.secondaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.check_circle_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tradução de Áudio Concluída',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Processamento de voz para texto realizado com sucesso',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Resultado da tradução
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.translate_rounded,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Texto Traduzido',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SelectableText(
                  _audioTranslationResult!,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                    height: 1.6,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Botões de ação
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: _audioTranslationResult!));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            Icon(
                              Icons.check_circle_rounded,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            const Text('Tradução copiada para a área de transferência!'),
                          ],
                        ),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy_rounded),
                  label: const Text('Copiar Tradução'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FilledButton.icon(
                  onPressed: _translateAudio,
                  icon: const Icon(Icons.refresh_rounded),
                  label: const Text('Nova Tradução'),
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Audio helper methods
  String _getAudioLoadingMessage() {
    final messages = [
      'Processando áudio...',
      'Convertendo voz para texto...',
      'Analisando conteúdo...',
      'Traduzindo com IA...',
      'Finalizando tradução...',
    ];

    final index = (DateTime.now().millisecondsSinceEpoch ~/ 2000) % messages.length;
    return messages[index];
  }

  void _showAudioSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Selecionar Áudio',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: FilledButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _startRecording();
                    },
                    icon: const Icon(Icons.mic_rounded),
                    label: const Text('Gravar'),
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _pickAudioFile();
                    },
                    icon: const Icon(Icons.audio_file_rounded),
                    label: const Text('Arquivo'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _startRecording() async {
    try {
      // Verificar permissões
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Permissão de microfone necessária para gravação'),
              backgroundColor: Theme.of(context).colorScheme.error,
              action: SnackBarAction(
                label: 'Configurações',
                onPressed: () => Permission.microphone.request(),
              ),
            ),
          );
        }
        return;
      }

      // Verificar se o gravador está disponível
      if (await _audioRecorder.hasPermission()) {
        // Obter diretório temporário
        final directory = await getTemporaryDirectory();
        final fileName = 'audio_${DateTime.now().millisecondsSinceEpoch}.m4a';
        final filePath = '${directory.path}/$fileName';

        // Configurar gravação
        const config = RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        );

        // Iniciar gravação
        await _audioRecorder.start(config, path: filePath);

        setState(() {
          _isRecording = true;
          _recordedAudioPath = filePath;
          _recordingDuration = Duration.zero;
        });

        // Mostrar dialog de gravação
        if (mounted) {
          _showRecordingDialog();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    Icons.mic_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Text('Gravação iniciada!'),
                ],
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao iniciar gravação: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _audioRecorder.stop();

      setState(() {
        _isRecording = false;
      });

      if (path != null) {
        // Carregar o arquivo de áudio
        final file = File(path);
        final audioBytes = await file.readAsBytes();

        setState(() {
          _selectedAudioBytes = audioBytes;
          _selectedAudioName = 'Gravação_${DateTime.now().millisecondsSinceEpoch}.m4a';
          _recordedAudioPath = path;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    Icons.check_circle_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Text('Gravação concluída!'),
                ],
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isRecording = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao parar gravação: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showRecordingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated recording icon
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.8, end: 1.2),
              duration: const Duration(milliseconds: 1000),
              curve: Curves.easeInOut,
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(32),
                      border: Border.all(
                        color: Colors.red,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.mic_rounded,
                      size: 48,
                      color: Colors.red,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            Text(
              'Gravando...',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Fale claramente para uma melhor tradução',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Recording duration
            StreamBuilder<Duration>(
              stream: Stream.periodic(const Duration(seconds: 1), (i) => Duration(seconds: i)),
              builder: (context, snapshot) {
                final duration = snapshot.data ?? Duration.zero;
                return Text(
                  '${duration.inMinutes.toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Colors.red,
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // Stop button
            FilledButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _stopRecording();
              },
              icon: const Icon(Icons.stop_rounded),
              label: const Text('Parar Gravação'),
              style: FilledButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickAudioFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.bytes != null) {
          setState(() {
            _selectedAudioBytes = file.bytes!;
            _selectedAudioName = file.name;
            _recordedAudioPath = file.path;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(
                      Icons.audio_file_rounded,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text('Arquivo selecionado: ${file.name}'),
                    ),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.primary,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao selecionar arquivo: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleAudioPlayback() async {
    try {
      if (_isPlayingAudio) {
        // Pausar reprodução
        await _audioPlayer.pause();
        setState(() {
          _isPlayingAudio = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Áudio pausado'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // Iniciar reprodução
        if (_recordedAudioPath != null) {
          await _audioPlayer.play(DeviceFileSource(_recordedAudioPath!));
          setState(() {
            _isPlayingAudio = true;
          });

          // Configurar listeners
          _audioPlayer.onDurationChanged.listen((duration) {
            setState(() {
              _totalDuration = duration;
            });
          });

          _audioPlayer.onPositionChanged.listen((position) {
            setState(() {
              _playbackDuration = position;
            });
          });

          _audioPlayer.onPlayerComplete.listen((_) {
            setState(() {
              _isPlayingAudio = false;
              _playbackDuration = Duration.zero;
            });
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Reproduzindo áudio...'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        } else if (_selectedAudioBytes != null) {
          // Para arquivos selecionados, criar arquivo temporário
          final directory = await getTemporaryDirectory();
          final tempFile = File('${directory.path}/temp_audio.m4a');
          await tempFile.writeAsBytes(_selectedAudioBytes!);

          await _audioPlayer.play(DeviceFileSource(tempFile.path));
          setState(() {
            _isPlayingAudio = true;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Reproduzindo áudio...'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      }
    } catch (e) {
      setState(() {
        _isPlayingAudio = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro na reprodução: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _clearAudio() {
    setState(() {
      _selectedAudioBytes = null;
      _selectedAudioName = null;
      _audioTranslationResult = null;
      _isPlayingAudio = false;
    });
  }

  Future<void> _translateAudio() async {
    if (_selectedAudioBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Selecione um áudio primeiro')),
      );
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      // Simular transcrição de áudio para texto
      await Future.delayed(const Duration(seconds: 2));

      // Texto simulado extraído do áudio
      const extractedText = 'Hello, how are you today? I hope you are having a wonderful day and everything is going well for you.';

      // Usar o serviço de tradução existente
      final translationService = RealTranslationService.instance;
      final translationResult = await translationService.translateText(
        text: extractedText,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      setState(() {
        _audioTranslationResult = translationResult.translatedText;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Text('Tradução de áudio concluída!'),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro na tradução: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
      }
    }
  }

  // Video Translation Tab Implementation
  Widget _buildVideoTranslationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // M3 Language Selector Card - igual às outras abas
          Card(
            elevation: 0,
            color: Theme.of(context).colorScheme.surfaceContainerLow,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Source Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _sourceLanguage,
                      label: const Text('De'),
                      leadingIcon: const Icon(Icons.language_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'auto', label: 'Detectar automaticamente'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _sourceLanguage = value;
                          });
                        }
                      },
                    ),
                  ),

                  // Swap Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: IconButton.filledTonal(
                      onPressed: _sourceLanguage != 'auto' ? () {
                        setState(() {
                          final temp = _sourceLanguage;
                          _sourceLanguage = _targetLanguage;
                          _targetLanguage = temp;
                        });
                      } : null,
                      icon: const Icon(Icons.swap_horiz_rounded),
                      tooltip: 'Trocar idiomas',
                    ),
                  ),

                  // Target Language Dropdown
                  Expanded(
                    child: DropdownMenu<String>(
                      initialSelection: _targetLanguage,
                      label: const Text('Para'),
                      leadingIcon: const Icon(Icons.translate_rounded),
                      dropdownMenuEntries: const [
                        DropdownMenuEntry(value: 'pt', label: 'Português'),
                        DropdownMenuEntry(value: 'en', label: 'Inglês'),
                        DropdownMenuEntry(value: 'es', label: 'Espanhol'),
                        DropdownMenuEntry(value: 'fr', label: 'Francês'),
                        DropdownMenuEntry(value: 'de', label: 'Alemão'),
                        DropdownMenuEntry(value: 'it', label: 'Italiano'),
                        DropdownMenuEntry(value: 'ja', label: 'Japonês'),
                        DropdownMenuEntry(value: 'ko', label: 'Coreano'),
                        DropdownMenuEntry(value: 'zh', label: 'Chinês'),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _targetLanguage = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // M3 Video Selection Card - seguindo exatamente o padrão da imagem
          SizedBox(
            height: 400,
            child: Card(
              elevation: 0,
              color: _selectedVideoBytes != null
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceContainerLow,
              clipBehavior: Clip.antiAlias,
              child: _selectedVideoBytes != null
                  ? _buildSelectedVideoWidget()
                  : _buildVideoPlaceholder(),
            ),
          ),

          const SizedBox(height: 16),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: FilledButton.icon(
                  onPressed: _showVideoSourceDialog,
                  icon: Icon(
                    _selectedVideoBytes != null
                        ? Icons.swap_horiz_rounded
                        : Icons.videocam_rounded,
                  ),
                  label: Text(_selectedVideoBytes != null ? 'Trocar Vídeo' : 'Gravar/Selecionar'),
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
              if (_selectedVideoBytes != null) ...[
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: _clearVideo,
                  icon: const Icon(Icons.delete_outline_rounded),
                  label: const Text('Remover'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    foregroundColor: Theme.of(context).colorScheme.error,
                    side: BorderSide(color: Theme.of(context).colorScheme.error),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 24),

          // Translation result area - seguindo exatamente o padrão da imagem
          SizedBox(
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildVideoTranslationResult(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Video Placeholder Widget
  Widget _buildVideoPlaceholder() {
    return InkWell(
      onTap: _showVideoSourceDialog,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated video icon
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.8, end: 1.1),
              duration: const Duration(milliseconds: 1500),
              curve: Curves.easeInOut,
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primaryContainer,
                          Theme.of(context).colorScheme.secondaryContainer,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.videocam_rounded,
                      size: 48,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 20),

            Text(
              'Selecionar Vídeo',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'Grave um novo vídeo ou selecione um arquivo existente',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Info chips
            Wrap(
              spacing: 8,
              runSpacing: 8,
              alignment: WrapAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.videocam_rounded, size: 16, color: Colors.blue),
                      SizedBox(width: 6),
                      Text(
                        'Gravação',
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.video_file_rounded, size: 16, color: Colors.green),
                      SizedBox(width: 6),
                      Text(
                        'Arquivo',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Selected Video Widget
  Widget _buildSelectedVideoWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.secondaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Video preview or icon
          if (_chewieController != null)
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Chewie(controller: _chewieController!),
              ),
            )
          else
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TweenAnimationBuilder<double>(
                        tween: Tween(begin: 0.8, end: 1.2),
                        duration: const Duration(milliseconds: 1000),
                        curve: Curves.easeInOut,
                        builder: (context, scale, child) {
                          return Transform.scale(
                            scale: scale,
                            child: Icon(
                              _isPlayingVideo ? Icons.pause_circle_filled_rounded : Icons.play_circle_filled_rounded,
                              size: 64,
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Vídeo Carregado',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Video info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withOpacity(0.9),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.video_file_rounded,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _selectedVideoName ?? 'Vídeo selecionado',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${(_selectedVideoBytes!.length / (1024 * 1024)).toStringAsFixed(1)} MB',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      ' • Pronto para tradução',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton.filledTonal(
                      onPressed: _toggleVideoPlayback,
                      icon: Icon(
                        _isPlayingVideo ? Icons.pause_rounded : Icons.play_arrow_rounded,
                      ),
                      tooltip: _isPlayingVideo ? 'Pausar' : 'Reproduzir',
                    ),
                    const SizedBox(width: 16),
                    IconButton.filledTonal(
                      onPressed: _clearVideo,
                      icon: const Icon(Icons.delete_outline_rounded),
                      tooltip: 'Remover vídeo',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Video Translation Result Widget
  Widget _buildVideoTranslationResult() {
    // Estado de loading - seguindo exatamente o padrão da imagem
    if (_isTranslating) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced loading animation with Material 3 design - igual ao da imagem
            Stack(
              alignment: Alignment.center,
              children: [
                // Outer ring with subtle animation
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Color.lerp(
                      Theme.of(context).colorScheme.outline,
                      Colors.transparent,
                      0.7,
                    )!,
                    value: 1.0,
                  ),
                ),
                // Main progress indicator
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    color: Theme.of(context).colorScheme.primary,
                    strokeCap: StrokeCap.round,
                  ),
                ),
                // Center icon with animation
                TweenAnimationBuilder<double>(
                  tween: Tween(begin: 0.8, end: 1.2),
                  duration: const Duration(milliseconds: 1000),
                  curve: Curves.easeInOut,
                  builder: (context, scale, child) {
                    return Transform.scale(
                      scale: scale,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(
                                Theme.of(context).colorScheme.primary,
                                Colors.transparent,
                                0.7,
                              )!,
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.videocam_rounded,
                          size: 32,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Title - seguindo o padrão da imagem
            Text(
              'Processando Vídeo',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Animated subtitle - seguindo o padrão da imagem
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              child: Text(
                _getVideoLoadingMessage(),
                key: ValueKey(_getVideoLoadingMessage()),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    // Estado vazio - seguindo exatamente o padrão da imagem
    if (_videoTranslationResult == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.translate_rounded,
                size: 32,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Resultado da tradução aparecerá aqui',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedVideoBytes != null) ...[
              const SizedBox(height: 16),
              FilledButton.icon(
                onPressed: _translateVideo,
                icon: const Icon(Icons.translate_rounded),
                label: const Text('Traduzir Vídeo'),
              ),
            ],
          ],
        ),
      );
    }

    // Estado com resultado - seguindo exatamente o padrão da imagem
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header com status - igual ao da imagem
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.secondaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.check_circle_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tradução Concluída',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Vídeo processado',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Resultado da tradução - seguindo o padrão da imagem
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header da tradução
                Row(
                  children: [
                    Icon(
                      Icons.translate_rounded,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Texto Traduzido',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Texto traduzido
                SelectableText(
                  _videoTranslationResult!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                    height: 1.6,
                    letterSpacing: 0.1,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Botões de ação - seguindo o padrão da imagem
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: _videoTranslationResult!));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Tradução copiada!'),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy_rounded),
                  label: const Text('Copiar'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: FilledButton.icon(
                  onPressed: _translateVideo,
                  icon: const Icon(Icons.refresh_rounded),
                  label: const Text('Traduzir Novamente'),
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Video helper methods
  String _getVideoLoadingMessage() {
    final messages = [
      'Processando vídeo...',
      'Extraindo áudio do vídeo...',
      'Convertendo fala para texto...',
      'Analisando conteúdo...',
      'Traduzindo com IA...',
      'Finalizando tradução...',
    ];

    final index = (DateTime.now().millisecondsSinceEpoch ~/ 2000) % messages.length;
    return messages[index];
  }

  void _showVideoSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Selecionar Vídeo',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: FilledButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _startVideoRecording();
                    },
                    icon: const Icon(Icons.videocam_rounded),
                    label: const Text('Gravar'),
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _pickVideoFile();
                    },
                    icon: const Icon(Icons.video_file_rounded),
                    label: const Text('Arquivo'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _startVideoRecording() async {
    try {
      // Verificar se estamos na web
      if (kIsWeb) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Gravação de vídeo não suportada na web. Use "Arquivo" para selecionar um vídeo.'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        return;
      }

      // Verificar permissões
      final cameraPermission = await Permission.camera.request();
      final microphonePermission = await Permission.microphone.request();

      if (cameraPermission != PermissionStatus.granted ||
          microphonePermission != PermissionStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Permissões de câmera e microfone necessárias'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        return;
      }

      if (_cameras != null && _cameras!.isNotEmpty) {
        // Inicializar controlador da câmera
        _cameraController = CameraController(
          _cameras!.first,
          ResolutionPreset.medium,
          enableAudio: true,
        );

        await _cameraController!.initialize();

        // Mostrar dialog de gravação
        if (mounted) {
          _showVideoRecordingDialog();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Nenhuma câmera disponível'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao iniciar gravação: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showVideoRecordingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Camera preview
            if (_cameraController != null && _cameraController!.value.isInitialized)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CameraPreview(_cameraController!),
                ),
              )
            else
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
              ),

            const SizedBox(height: 24),

            Text(
              _isRecordingVideo ? 'Gravando...' : 'Pronto para gravar',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Posicione-se bem na câmera e fale claramente',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Recording controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (_isRecordingVideo)
                  FilledButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _stopVideoRecording();
                    },
                    icon: const Icon(Icons.stop_rounded),
                    label: const Text('Parar'),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  )
                else
                  FilledButton.icon(
                    onPressed: _startVideoRecordingCapture,
                    icon: const Icon(Icons.fiber_manual_record_rounded),
                    label: const Text('Gravar'),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),

                OutlinedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _cameraController?.dispose();
                    _cameraController = null;
                  },
                  icon: const Icon(Icons.close_rounded),
                  label: const Text('Cancelar'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startVideoRecordingCapture() async {
    try {
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        await _cameraController!.startVideoRecording();
        setState(() {
          _isRecordingVideo = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao iniciar gravação: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _stopVideoRecording() async {
    try {
      if (_cameraController != null && _isRecordingVideo) {
        final videoFile = await _cameraController!.stopVideoRecording();

        setState(() {
          _isRecordingVideo = false;
        });

        // Carregar o arquivo de vídeo
        final videoBytes = await videoFile.readAsBytes();

        setState(() {
          _selectedVideoBytes = videoBytes;
          _selectedVideoName = 'Gravacao_${DateTime.now().millisecondsSinceEpoch}.mp4';
          _recordedVideoPath = videoFile.path;
        });

        // Inicializar player de vídeo
        await _initializeVideoPlayer(videoFile.path);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    Icons.check_circle_rounded,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Text('Gravação de vídeo concluída!'),
                ],
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Limpar recursos da câmera
        _cameraController?.dispose();
        _cameraController = null;
      }
    } catch (e) {
      setState(() {
        _isRecordingVideo = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao parar gravação: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _pickVideoFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.bytes != null) {
          setState(() {
            _selectedVideoBytes = file.bytes!;
            _selectedVideoName = file.name;
            _recordedVideoPath = file.path;
          });

          // Inicializar player de vídeo se possível
          if (file.path != null) {
            await _initializeVideoPlayer(file.path!);
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(
                      Icons.video_file_rounded,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text('Arquivo selecionado: ${file.name}'),
                    ),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.primary,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao selecionar arquivo: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _initializeVideoPlayer(String videoPath) async {
    try {
      // Verificar se estamos na web
      if (kIsWeb) {
        // Silencioso na web - video player tem limitações
        return;
      }

      _videoPlayerController = VideoPlayerController.file(File(videoPath));
      await _videoPlayerController!.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: false,
        looping: false,
        showControls: true,
        aspectRatio: _videoPlayerController!.value.aspectRatio,
      );

      setState(() {});
    } catch (e) {
      // Log silencioso - não mostrar erro para o usuário
      if (kDebugMode) {
        debugPrint('Video player initialization failed: $e');
      }
    }
  }

  Future<void> _toggleVideoPlayback() async {
    try {
      if (_videoPlayerController != null) {
        if (_isPlayingVideo) {
          await _videoPlayerController!.pause();
          setState(() {
            _isPlayingVideo = false;
          });
        } else {
          await _videoPlayerController!.play();
          setState(() {
            _isPlayingVideo = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro na reprodução: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _clearVideo() {
    setState(() {
      _selectedVideoBytes = null;
      _selectedVideoName = null;
      _videoTranslationResult = null;
      _isPlayingVideo = false;
      _recordedVideoPath = null;
    });

    _videoPlayerController?.dispose();
    _videoPlayerController = null;
    _chewieController?.dispose();
    _chewieController = null;
  }

  Future<void> _translateVideo() async {
    if (_selectedVideoBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Selecione um vídeo primeiro')),
      );
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      // Simular extração de áudio do vídeo e transcrição
      await Future.delayed(const Duration(seconds: 3));

      // Texto simulado extraído do áudio do vídeo
      const extractedText = 'Welcome to our presentation. Today we will discuss the latest developments in artificial intelligence and machine learning technologies. These innovations are transforming how we work and live.';

      // Usar o serviço de tradução existente
      final translationService = RealTranslationService.instance;
      final translationResult = await translationService.translateText(
        text: extractedText,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      setState(() {
        _videoTranslationResult = translationResult.translatedText;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Text('Tradução de vídeo concluída!'),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro na tradução: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
      }
    }
  }
}
