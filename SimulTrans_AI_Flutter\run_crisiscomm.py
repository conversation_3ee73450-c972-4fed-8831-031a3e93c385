#!/usr/bin/env python3
"""
Script de execução para CrisisComm
Facilita o lançamento da aplicação com verificações de pré-requisitos
"""

import os
import sys
import subprocess
import platform
import webbrowser
import time
from pathlib import Path

def print_banner():
    """Exibe banner do CrisisComm"""
    print("\n" + "="*60)
    print("🚨 CrisisComm - Comunicador de Emergência Multilíngue")
    print("="*60)
    print("Sistema de comunicação para situações de crise")
    print("Powered by Google Gemma 3n + Streamlit")
    print("="*60 + "\n")

def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    print("🔍 Verificando dependências...")
    
    required_packages = [
        "streamlit",
        "torch", 
        "transformers",
        "PIL",
        "numpy"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Pacotes faltando: {', '.join(missing_packages)}")
        print("Execute: pip install -r requirements.txt")
        return False
    
    print("✅ Todas as dependências estão instaladas!")
    return True

def check_files():
    """Verifica se os arquivos necessários existem"""
    print("\n🔍 Verificando arquivos do projeto...")
    
    required_files = [
        "crisiscomm_app.py",
        "config.py",
        "utils.py",
        "demo_examples.py",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  Arquivos faltando: {', '.join(missing_files)}")
        return False
    
    print("✅ Todos os arquivos necessários estão presentes!")
    return True

def create_directories():
    """Cria diretórios necessários se não existirem"""
    print("\n📁 Verificando diretórios...")
    
    directories = [
        "cache",
        "cache/huggingface", 
        "temp",
        "logs",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}/")

def check_system_resources():
    """Verifica recursos do sistema"""
    print("\n🔍 Verificando recursos do sistema...")

    try:
        import psutil

        # Verificar memória
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"💾 Memória RAM: {memory_gb:.1f} GB")

        if memory_gb < 4:
            print("⚠️  Aviso: Recomendado pelo menos 4GB de RAM")

        # Verificar espaço em disco
        disk_usage = psutil.disk_usage('.')
        free_gb = disk_usage.free / (1024**3)
        print(f"💽 Espaço livre: {free_gb:.1f} GB")

        if free_gb < 5:
            print("⚠️  Aviso: Recomendado pelo menos 5GB de espaço livre")

    except ImportError:
        print("💾 Não foi possível verificar recursos (psutil não instalado)")

    # Verificar GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🎮 GPU detectada: {gpu_name}")
        else:
            print("🎮 GPU: Não detectada (CPU será usado)")
    except ImportError:
        print("🎮 GPU: PyTorch não instalado")

def check_huggingface_setup():
    """Verifica configuração do Hugging Face"""
    print("\n🔍 Verificando configuração do Hugging Face...")

    from dotenv import load_dotenv
    load_dotenv()

    token = os.getenv('HUGGING_FACE_TOKEN') or os.getenv('HF_TOKEN')

    if not token:
        print("❌ Token do Hugging Face não configurado")
        print("💡 Configure no arquivo .env: HUGGING_FACE_TOKEN=seu_token")
        print("🔗 Obtenha em: https://huggingface.co/settings/tokens")
        return False
    else:
        print(f"✅ Token HF configurado: {token[:8]}...")
        return True

def check_model_downloaded():
    """Verifica se o modelo foi baixado"""
    print("\n🔍 Verificando modelo Gemma 3n...")

    cache_dir = Path("./cache/huggingface")
    model_dir = cache_dir / "models--google--gemma-3n-e2b-it"

    if model_dir.exists():
        print("✅ Modelo Gemma 3n encontrado no cache")
        return True
    else:
        print("❌ Modelo Gemma 3n não encontrado")
        print("💡 Execute: python download_model.py")
        return False

def get_streamlit_command():
    """Retorna o comando correto do Streamlit"""
    # Tentar diferentes comandos
    commands = ["streamlit", "python -m streamlit", "python3 -m streamlit"]
    
    for cmd in commands:
        try:
            result = subprocess.run(
                f"{cmd} --version", 
                shell=True, 
                capture_output=True, 
                text=True
            )
            if result.returncode == 0:
                return cmd
        except:
            continue
    
    return None

def launch_app():
    """Lança a aplicação Streamlit"""
    print("\n🚀 Iniciando CrisisComm...")
    
    streamlit_cmd = get_streamlit_command()
    
    if not streamlit_cmd:
        print("❌ Streamlit não encontrado. Instale com: pip install streamlit")
        return False
    
    # Comando para executar a aplicação
    cmd = f"{streamlit_cmd} run crisiscomm_app.py"
    
    print(f"📝 Executando: {cmd}")
    print("\n" + "="*60)
    print("🌐 A aplicação será aberta no navegador automaticamente")
    print("📍 URL: http://localhost:8501")
    print("⏹️  Para parar: Ctrl+C no terminal")
    print("="*60 + "\n")
    
    try:
        # Executar comando
        subprocess.run(cmd, shell=True)
    except KeyboardInterrupt:
        print("\n\n⏹️  Aplicação interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro ao executar aplicação: {e}")
        return False
    
    return True

def show_help():
    """Exibe ajuda"""
    help_text = """
🆘 AJUDA - CrisisComm

📋 Comandos disponíveis:
  python run_crisiscomm.py          - Executa a aplicação
  python run_crisiscomm.py --help   - Mostra esta ajuda
  python run_crisiscomm.py --check  - Apenas verifica pré-requisitos
  python run_crisiscomm.py --setup  - Executa configuração inicial

🔧 Solução de problemas:
  1. Dependências faltando:
     pip install -r requirements.txt
  
  2. Modelo não carrega:
     - Verifique conexão com internet
     - Configure HUGGING_FACE_TOKEN se necessário
  
  3. Erro de memória:
     - Feche outros programas
     - Use CPU em vez de GPU (DEVICE=cpu no .env)
  
  4. Streamlit não encontrado:
     pip install streamlit

📞 Suporte:
  - GitHub: https://github.com/seu-usuario/crisiscomm
  - Email: <EMAIL>
    """
    print(help_text)

def run_setup():
    """Executa configuração inicial"""
    print("⚙️  Executando configuração inicial...")
    
    try:
        import setup
        setup.main()
    except ImportError:
        print("❌ Arquivo setup.py não encontrado")
    except Exception as e:
        print(f"❌ Erro na configuração: {e}")

def main():
    """Função principal"""
    # Verificar argumentos
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ["--help", "-h", "help"]:
            show_help()
            return
        elif arg in ["--setup", "-s", "setup"]:
            run_setup()
            return
        elif arg in ["--check", "-c", "check"]:
            print_banner()
            check_dependencies()
            check_files()
            create_directories()
            check_system_resources()
            print("\n✅ Verificação concluída!")
            return
    
    # Execução normal
    print_banner()
    
    # Verificações pré-execução
    if not check_dependencies():
        print("\n❌ Instale as dependências antes de continuar.")
        return
    
    if not check_files():
        print("\n❌ Arquivos do projeto faltando.")
        return
    
    create_directories()
    check_system_resources()

    # Verificar configuração do Hugging Face
    if not check_huggingface_setup():
        print("\n⚠️  Configure o token do Hugging Face antes de continuar.")
        print("Execute: python download_model.py")
        return

    # Verificar se modelo foi baixado
    if not check_model_downloaded():
        print("\n⚠️  Baixe o modelo antes de continuar.")
        print("Execute: python download_model.py")
        return

    # Perguntar se deseja continuar
    print("\n" + "="*60)
    response = input("🚀 Deseja iniciar o CrisisComm? (s/N): ").lower()

    if response in ['s', 'sim', 'y', 'yes']:
        launch_app()
    else:
        print("👋 Até logo!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Até logo!")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        print("🆘 Para suporte, visite: https://github.com/seu-usuario/crisiscomm/issues")
