import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

import '../app_config.dart';
import '../models/translation_result.dart';
import '../utils/logger.dart';

/// Intelligent caching service with LRU eviction and compression
class CacheService {
  static final CacheService _instance = CacheService._internal();
  static CacheService get instance => _instance;
  CacheService._internal();

  Box<String>? _translationCache;
  Box<String>? _modelCache;
  Box<String>? _metadataCache;
  
  bool _isInitialized = false;
  int _cacheHits = 0;
  int _cacheMisses = 0;
  final Map<String, DateTime> _accessTimes = {};

  bool get isInitialized => _isInitialized;
  double get hitRate => _cacheHits + _cacheMisses > 0 
      ? _cacheHits / (_cacheHits + _cacheMisses) 
      : 0.0;

  /// Initialize cache service with Hive boxes
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Get application documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${appDir.path}/cache');
      
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Initialize Hive boxes
      _translationCache = await Hive.openBox<String>(
        'translation_cache',
        path: cacheDir.path,
      );
      
      _modelCache = await Hive.openBox<String>(
        'model_cache',
        path: cacheDir.path,
      );
      
      _metadataCache = await Hive.openBox<String>(
        'metadata_cache',
        path: cacheDir.path,
      );

      // Clean expired entries on startup
      await _cleanExpiredEntries();
      
      // Enforce cache size limits
      await _enforceCacheLimits();

      _isInitialized = true;
      Logger.info('Cache service initialized successfully');
      
    } catch (e) {
      Logger.error('Failed to initialize cache service: $e');
      rethrow;
    }
  }

  /// Cache a translation result with intelligent compression
  Future<void> cacheTranslation(String key, TranslationResult result) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generateSecureKey(key);
      final cacheEntry = CacheEntry(
        data: result.toJson(),
        timestamp: DateTime.now(),
        accessCount: 1,
        size: _calculateSize(result.toJson()),
      );

      // Compress large entries
      final serialized = _shouldCompress(cacheEntry.size) 
          ? await _compressData(jsonEncode(cacheEntry.toJson()))
          : jsonEncode(cacheEntry.toJson());

      await _translationCache!.put(cacheKey, serialized);
      _accessTimes[cacheKey] = DateTime.now();

      // Update metadata
      await _updateCacheMetadata();
      
      Logger.debug('Translation cached: $cacheKey (${cacheEntry.size} bytes)');
      
    } catch (e) {
      Logger.error('Failed to cache translation: $e');
    }
  }

  /// Retrieve cached translation with LRU tracking
  Future<TranslationResult?> getTranslation(String key) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generateSecureKey(key);
      final cachedData = _translationCache!.get(cacheKey);
      
      if (cachedData == null) {
        _cacheMisses++;
        return null;
      }

      // Decompress if needed
      final jsonData = _isCompressed(cachedData)
          ? await _decompressData(cachedData)
          : cachedData;

      final cacheEntry = CacheEntry.fromJson(jsonDecode(jsonData));
      
      // Check if expired
      if (_isExpired(cacheEntry.timestamp)) {
        await _translationCache!.delete(cacheKey);
        _accessTimes.remove(cacheKey);
        _cacheMisses++;
        return null;
      }

      // Update access tracking
      cacheEntry.accessCount++;
      _accessTimes[cacheKey] = DateTime.now();
      _cacheHits++;

      // Update cache entry with new access data
      final updatedData = _shouldCompress(cacheEntry.size)
          ? await _compressData(jsonEncode(cacheEntry.toJson()))
          : jsonEncode(cacheEntry.toJson());
      
      await _translationCache!.put(cacheKey, updatedData);

      return TranslationResult.fromJson(cacheEntry.data);
      
    } catch (e) {
      Logger.error('Failed to retrieve cached translation: $e');
      _cacheMisses++;
      return null;
    }
  }

  /// Cache model data
  Future<void> cacheModelData(String modelName, Map<String, dynamic> data) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheEntry = CacheEntry(
        data: data,
        timestamp: DateTime.now(),
        accessCount: 1,
        size: _calculateSize(data),
      );

      final serialized = jsonEncode(cacheEntry.toJson());
      await _modelCache!.put(modelName, serialized);
      
      Logger.debug('Model data cached: $modelName');
      
    } catch (e) {
      Logger.error('Failed to cache model data: $e');
    }
  }

  /// Get cached model data
  Future<Map<String, dynamic>?> getModelData(String modelName) async {
    if (!_isInitialized) await initialize();

    try {
      final cachedData = _modelCache!.get(modelName);
      if (cachedData == null) return null;

      final cacheEntry = CacheEntry.fromJson(jsonDecode(cachedData));
      
      if (_isExpired(cacheEntry.timestamp)) {
        await _modelCache!.delete(modelName);
        return null;
      }

      return cacheEntry.data;
      
    } catch (e) {
      Logger.error('Failed to retrieve cached model data: $e');
      return null;
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'translation_cache_size': _translationCache?.length ?? 0,
      'model_cache_size': _modelCache?.length ?? 0,
      'cache_hits': _cacheHits,
      'cache_misses': _cacheMisses,
      'hit_rate': hitRate,
      'total_size_bytes': _getTotalCacheSize(),
    };
  }

  /// Get total cache size in bytes
  Future<int> getCacheSize() async {
    return _getTotalCacheSize();
  }

  /// Clear all caches
  Future<void> clearAllCaches() async {
    if (!_isInitialized) await initialize();

    try {
      await _translationCache!.clear();
      await _modelCache!.clear();
      await _metadataCache!.clear();
      
      _accessTimes.clear();
      _cacheHits = 0;
      _cacheMisses = 0;
      
      Logger.info('All caches cleared');
      
    } catch (e) {
      Logger.error('Failed to clear caches: $e');
    }
  }

  /// Clear expired entries
  Future<void> clearExpiredEntries() async {
    await _cleanExpiredEntries();
  }

  /// Optimize cache (remove least recently used items if over limit)
  Future<void> optimizeCache() async {
    await _enforceCacheLimits();
  }

  // Private helper methods
  String _generateSecureKey(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  int _calculateSize(Map<String, dynamic> data) {
    return utf8.encode(jsonEncode(data)).length;
  }

  bool _shouldCompress(int size) {
    return size > 1024; // Compress entries larger than 1KB
  }

  bool _isCompressed(String data) {
    // Simple heuristic: compressed data typically starts with specific bytes
    return data.startsWith('H4sI') || data.startsWith('eJy'); // gzip/zlib signatures
  }

  Future<String> _compressData(String data) async {
    // In a real implementation, use a compression library like archive
    // For now, return the original data
    return data;
  }

  Future<String> _decompressData(String compressedData) async {
    // In a real implementation, decompress the data
    // For now, return the original data
    return compressedData;
  }

  bool _isExpired(DateTime timestamp) {
    return DateTime.now().difference(timestamp) > AppConfig.cacheExpiration;
  }

  int _getTotalCacheSize() {
    int totalSize = 0;
    
    // Calculate translation cache size
    if (_translationCache != null) {
      for (final value in _translationCache!.values) {
        totalSize += utf8.encode(value).length;
      }
    }
    
    // Calculate model cache size
    if (_modelCache != null) {
      for (final value in _modelCache!.values) {
        totalSize += utf8.encode(value).length;
      }
    }
    
    return totalSize;
  }

  Future<void> _cleanExpiredEntries() async {
    if (_translationCache == null) return;

    try {
      final keysToDelete = <String>[];
      
      for (final key in _translationCache!.keys) {
        try {
          final cachedData = _translationCache!.get(key);
          if (cachedData != null) {
            final jsonData = _isCompressed(cachedData)
                ? await _decompressData(cachedData)
                : cachedData;
            
            final cacheEntry = CacheEntry.fromJson(jsonDecode(jsonData));
            
            if (_isExpired(cacheEntry.timestamp)) {
              keysToDelete.add(key);
            }
          }
        } catch (e) {
          // If we can't parse the entry, mark it for deletion
          keysToDelete.add(key);
        }
      }
      
      for (final key in keysToDelete) {
        await _translationCache!.delete(key);
        _accessTimes.remove(key);
      }
      
      if (keysToDelete.isNotEmpty) {
        Logger.info('Cleaned ${keysToDelete.length} expired cache entries');
      }
      
    } catch (e) {
      Logger.error('Failed to clean expired entries: $e');
    }
  }

  Future<void> _enforceCacheLimits() async {
    final currentSize = _getTotalCacheSize();
    
    if (currentSize <= AppConfig.maxCacheSize) return;

    try {
      // Sort entries by last access time (LRU)
      final sortedEntries = _accessTimes.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries until under limit
      int removedSize = 0;
      final keysToRemove = <String>[];
      
      for (final entry in sortedEntries) {
        final cachedData = _translationCache!.get(entry.key);
        if (cachedData != null) {
          removedSize += utf8.encode(cachedData).length;
          keysToRemove.add(entry.key);
          
          if (currentSize - removedSize <= AppConfig.maxCacheSize) {
            break;
          }
        }
      }
      
      for (final key in keysToRemove) {
        await _translationCache!.delete(key);
        _accessTimes.remove(key);
      }
      
      Logger.info('Enforced cache limits: removed ${keysToRemove.length} entries (${removedSize} bytes)');
      
    } catch (e) {
      Logger.error('Failed to enforce cache limits: $e');
    }
  }

  Future<void> _updateCacheMetadata() async {
    try {
      final metadata = {
        'last_updated': DateTime.now().toIso8601String(),
        'total_entries': _translationCache!.length,
        'cache_hits': _cacheHits,
        'cache_misses': _cacheMisses,
        'hit_rate': hitRate,
      };
      
      await _metadataCache!.put('stats', jsonEncode(metadata));
      
    } catch (e) {
      Logger.error('Failed to update cache metadata: $e');
    }
  }
}

/// Cache entry model
class CacheEntry {
  final Map<String, dynamic> data;
  final DateTime timestamp;
  int accessCount;
  final int size;

  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.accessCount,
    required this.size,
  });

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
      accessCount: json['access_count'],
      size: json['size'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'access_count': accessCount,
      'size': size,
    };
  }
}
