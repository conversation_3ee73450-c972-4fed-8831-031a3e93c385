"""
Exemplos de demonstração para CrisisComm
Este arquivo contém exemplos práticos de uso do sistema
"""

import streamlit as st
from datetime import datetime
import json

class DemoExamples:
    """Classe com exemplos de demonstração"""
    
    def __init__(self):
        self.examples = {
            "translation": self.get_translation_examples(),
            "emergency_scenarios": self.get_emergency_scenarios(),
            "multilingual_messages": self.get_multilingual_messages(),
            "damage_assessment": self.get_damage_assessment_examples(),
            "audio_transcription": self.get_audio_examples(),
            "situation_reports": self.get_report_examples()
        }
    
    def get_translation_examples(self):
        """Exemplos de tradução de emergência"""
        return {
            "medical_emergency": {
                "en": "Medical emergency! Unconscious person at Main Street 123. Need ambulance immediately!",
                "es": "¡Emergencia médica! Persona inconsciente en Calle Principal 123. ¡Necesito ambulancia inmediatamente!",
                "fr": "Urgence médicale ! Personne inconsciente au 123 rue Principale. Besoin d'ambulance immédiatement !",
                "de": "Medizinischer Notfall! Bewusstlose Person in der Hauptstraße 123. Brauche sofort einen Krankenwagen!",
                "pt": "Emergência médica! Pessoa inconsciente na Rua Principal 123. Preciso de ambulância imediatamente!",
                "ar": "حالة طوارئ طبية! شخص فاقد للوعي في الشارع الرئيسي 123. أحتاج سيارة إسعاف فوراً!",
                "zh": "医疗紧急情况！主街123号有人昏迷。立即需要救护车！",
                "ja": "医療緊急事態！メインストリート123番地で意識不明の人がいます。すぐに救急車が必要です！"
            },
            "fire_emergency": {
                "en": "Fire at residential building! 15 people trapped on 3rd floor. Send fire department now!",
                "es": "¡Incendio en edificio residencial! 15 personas atrapadas en el 3er piso. ¡Envíen bomberos ahora!",
                "fr": "Incendie dans un immeuble résidentiel ! 15 personnes piégées au 3ème étage. Envoyez les pompiers maintenant !",
                "de": "Feuer in Wohngebäude! 15 Personen im 3. Stock eingeschlossen. Feuerwehr sofort schicken!",
                "pt": "Incêndio em prédio residencial! 15 pessoas presas no 3º andar. Mandem bombeiros agora!",
                "ar": "حريق في مبنى سكني! 15 شخصاً محاصرون في الطابق الثالث. أرسلوا الإطفاء الآن!",
                "zh": "住宅楼火灾！15人被困在3楼。立即派消防队！",
                "ja": "住宅ビルで火災！3階に15人が閉じ込められています。今すぐ消防署を派遣してください！"
            },
            "natural_disaster": {
                "en": "Earthquake magnitude 7.2! Multiple buildings collapsed. Need immediate rescue teams!",
                "es": "¡Terremoto magnitud 7.2! Múltiples edificios colapsados. ¡Necesitamos equipos de rescate inmediatos!",
                "fr": "Tremblement de terre magnitude 7.2 ! Plusieurs bâtiments effondrés. Besoin d'équipes de secours immédiates !",
                "de": "Erdbeben Stärke 7.2! Mehrere Gebäude eingestürzt. Brauchen sofortige Rettungsteams!",
                "pt": "Terremoto magnitude 7.2! Múltiplos prédios desabaram. Precisamos de equipes de resgate imediatas!",
                "ar": "زلزال بقوة 7.2! انهيار عدة مباني. نحتاج فرق إنقاذ فورية!",
                "zh": "7.2级地震！多栋建筑倒塌。需要立即派遣救援队！",
                "ja": "マグニチュード7.2の地震！複数の建物が倒壊。即座に救助チームが必要です！"
            }
        }
    
    def get_emergency_scenarios(self):
        """Cenários de emergência para demonstração"""
        return {
            "scenario_1": {
                "title": "Acidente de Trânsito Internacional",
                "description": "Turistas japoneses em acidente na Espanha",
                "location": "Madrid, Espanha",
                "languages": ["ja", "es", "en"],
                "situation": "Colisão entre dois veículos na autoestrada A-1. Três feridos japoneses, barreira linguística com equipe médica local.",
                "input_text": "交通事故です！高速道路で車が衝突しました。3人が怪我をしています。すぐに救急車を呼んでください！",
                "expected_output": "¡Accidente de tráfico! Los coches han chocado en la autopista. 3 personas están heridas. ¡Llamen a una ambulancia inmediatamente!"
            },
            "scenario_2": {
                "title": "Desastre Natural em Área Remota",
                "description": "Deslizamento de terra em comunidade multilíngue",
                "location": "Região montanhosa, Peru",
                "languages": ["es", "qu", "en"],
                "situation": "Deslizamento bloqueou estrada principal. Comunidade isolada com falantes de espanhol e quéchua.",
                "input_text": "¡Deslizamiento de tierra! La carretera está bloqueada. Hay familias atrapadas en el pueblo. Necesitamos helicópteros de rescate.",
                "expected_output": "Landslide! The road is blocked. There are families trapped in the village. We need rescue helicopters."
            },
            "scenario_3": {
                "title": "Emergência Médica em Zona de Conflito",
                "description": "Hospital bombardeado, equipe internacional",
                "location": "Zona de conflito",
                "languages": ["ar", "en", "fr"],
                "situation": "Hospital danificado, equipe médica internacional precisa coordenar evacuação de pacientes.",
                "input_text": "المستشفى تضرر من القصف. يوجد 50 مريضاً يحتاجون إخلاء فوري. نحتاج مساعدة دولية.",
                "expected_output": "The hospital was damaged by bombing. There are 50 patients who need immediate evacuation. We need international assistance."
            }
        }
    
    def get_multilingual_messages(self):
        """Mensagens em múltiplos idiomas para teste"""
        return {
            "help_request": {
                "en": "Help! We need immediate assistance!",
                "es": "¡Ayuda! ¡Necesitamos asistencia inmediata!",
                "fr": "Au secours ! Nous avons besoin d'aide immédiate !",
                "de": "Hilfe! Wir brauchen sofortige Unterstützung!",
                "pt": "Socorro! Precisamos de assistência imediata!",
                "ar": "النجدة! نحتاج مساعدة فورية!",
                "zh": "救命！我们需要立即援助！",
                "ja": "助けて！すぐに支援が必要です！",
                "ko": "도움! 즉시 지원이 필요합니다!",
                "hi": "मदद! हमें तत्काल सहायता चाहिए!",
                "ru": "Помощь! Нам нужна немедленная помощь!"
            },
            "location_info": {
                "en": "Location: Downtown area, near the central hospital",
                "es": "Ubicación: Zona céntrica, cerca del hospital central",
                "fr": "Localisation : Centre-ville, près de l'hôpital central",
                "de": "Standort: Innenstadt, in der Nähe des Zentralkrankenhauses",
                "pt": "Localização: Área central, perto do hospital central",
                "ar": "الموقع: منطقة وسط المدينة، بالقرب من المستشفى المركزي",
                "zh": "位置：市中心区域，中心医院附近",
                "ja": "場所：ダウンタウンエリア、中央病院の近く",
                "ko": "위치: 시내 중심가, 중앙병원 근처",
                "hi": "स्थान: शहर के केंद्रीय क्षेत्र में, केंद्रीय अस्पताल के पास",
                "ru": "Местоположение: Центр города, рядом с центральной больницей"
            }
        }
    
    def get_damage_assessment_examples(self):
        """Exemplos de avaliação de danos"""
        return {
            "structural_damage": {
                "description": "Análise de danos estruturais em edifício",
                "image_description": "Prédio de 5 andares com rachaduras visíveis na fachada, janelas quebradas no 2º andar",
                "expected_analysis": {
                    "damage_type": "Estrutural",
                    "severity": "Média",
                    "risks": ["Queda de detritos", "Instabilidade estrutural"],
                    "recommendations": ["Evacuação preventiva", "Avaliação por engenheiro estrutural", "Isolamento da área"]
                }
            },
            "fire_damage": {
                "description": "Avaliação de danos por incêndio",
                "image_description": "Residência com telhado parcialmente destruído, paredes enegrecidas, fumaça ainda visível",
                "expected_analysis": {
                    "damage_type": "Incêndio",
                    "severity": "Alta",
                    "risks": ["Reacendimento", "Colapso estrutural", "Inalação de fumaça"],
                    "recommendations": ["Manter equipe de bombeiros no local", "Verificar focos de calor", "Evacuar área adjacente"]
                }
            },
            "flood_damage": {
                "description": "Danos causados por inundação",
                "image_description": "Rua alagada com água até 1 metro de altura, carros submersos, postes de energia em risco",
                "expected_analysis": {
                    "damage_type": "Inundação",
                    "severity": "Alta",
                    "risks": ["Choque elétrico", "Contaminação da água", "Deslizamentos"],
                    "recommendations": ["Cortar energia elétrica", "Evacuar área baixa", "Monitorar nível da água"]
                }
            }
        }
    
    def get_audio_examples(self):
        """Exemplos de transcrição de áudio"""
        return {
            "emergency_call": {
                "description": "Chamada de emergência em espanhol",
                "transcription": "¡Emergencia! Hay un accidente en la carretera principal. Dos coches chocaron. Hay heridos. Por favor, envíen ambulancia y policía inmediatamente.",
                "translation_en": "Emergency! There's an accident on the main highway. Two cars crashed. There are injured people. Please send ambulance and police immediately.",
                "urgency": "Alta"
            },
            "witness_report": {
                "description": "Relato de testemunha em francês",
                "transcription": "J'ai vu l'explosion depuis ma fenêtre. Il y a beaucoup de fumée noire. Les gens courent dans tous les sens. Je pense qu'il faut évacuer le quartier.",
                "translation_en": "I saw the explosion from my window. There's a lot of black smoke. People are running everywhere. I think the neighborhood needs to be evacuated.",
                "urgency": "Crítica"
            }
        }
    
    def get_report_examples(self):
        """Exemplos de relatórios de situação"""
        return {
            "earthquake_report": {
                "emergency_type": "natural_disaster",
                "location": "São Paulo, SP - Região Central",
                "description": "Terremoto de magnitude 6.8 atingiu a região metropolitana às 14:30. Múltiplos edifícios com danos estruturais.",
                "urgency": "Crítica",
                "casualties": "15 feridos confirmados, 3 desaparecidos",
                "resources_needed": "Equipes de resgate urbano, cães farejadores, equipamento de remoção de escombros",
                "expected_report": """
RELATÓRIO DE SITUAÇÃO DE EMERGÊNCIA

1. RESUMO EXECUTIVO
Terremoto de magnitude 6.8 atingiu São Paulo às 14:30, causando danos estruturais significativos na região central.

2. SITUAÇÃO ATUAL
- 15 feridos confirmados em hospitais locais
- 3 pessoas desaparecidas sob escombros
- 12 edifícios com danos estruturais graves
- Interrupção parcial de energia e comunicações

3. RISCOS IDENTIFICADOS
- Colapso adicional de estruturas danificadas
- Réplicas sísmicas nas próximas 24-48 horas
- Ruptura de tubulações de gás e água

4. AÇÕES RECOMENDADAS
- Evacuação imediata de edifícios danificados
- Estabelecimento de perímetro de segurança
- Ativação de equipes de busca e resgate
- Monitoramento sísmico contínuo

5. RECURSOS NECESSÁRIOS
- 3 equipes de resgate urbano especializadas
- 2 cães farejadores treinados
- Equipamento pesado para remoção de escombros
- Geradores de energia de emergência
                """
            }
        }
    
    def display_examples_in_streamlit(self):
        """Exibe exemplos na interface Streamlit"""
        st.header("📚 Exemplos de Demonstração")
        
        tab1, tab2, tab3, tab4 = st.tabs([
            "🗣️ Tradução", 
            "🎬 Cenários", 
            "📸 Análise de Imagem", 
            "📋 Relatórios"
        ])
        
        with tab1:
            st.subheader("Exemplos de Tradução de Emergência")
            
            translation_type = st.selectbox(
                "Tipo de emergência:",
                options=list(self.examples["translation"].keys()),
                format_func=lambda x: x.replace("_", " ").title()
            )
            
            examples = self.examples["translation"][translation_type]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Idiomas disponíveis:**")
                for lang, text in examples.items():
                    st.text_area(f"{lang.upper()}:", value=text, height=80, disabled=True)
            
            with col2:
                st.write("**Use estes exemplos:**")
                st.info("Copie qualquer texto acima e cole na aba 'Tradutor de Emergência' para testar a tradução.")
        
        with tab2:
            st.subheader("Cenários de Emergência Realistas")
            
            scenario_key = st.selectbox(
                "Selecione um cenário:",
                options=list(self.examples["emergency_scenarios"].keys()),
                format_func=lambda x: self.examples["emergency_scenarios"][x]["title"]
            )
            
            scenario = self.examples["emergency_scenarios"][scenario_key]
            
            st.write(f"**📍 Local:** {scenario['location']}")
            st.write(f"**🌍 Idiomas:** {', '.join(scenario['languages'])}")
            st.write(f"**📝 Situação:** {scenario['situation']}")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.text_area("Texto de entrada:", value=scenario['input_text'], height=100, disabled=True)
            
            with col2:
                st.text_area("Tradução esperada:", value=scenario['expected_output'], height=100, disabled=True)
        
        with tab3:
            st.subheader("Exemplos de Análise de Imagem")
            
            damage_type = st.selectbox(
                "Tipo de dano:",
                options=list(self.examples["damage_assessment"].keys()),
                format_func=lambda x: x.replace("_", " ").title()
            )
            
            example = self.examples["damage_assessment"][damage_type]
            
            st.write(f"**📝 Descrição:** {example['description']}")
            st.write(f"**🖼️ Imagem:** {example['image_description']}")
            
            analysis = example['expected_analysis']
            st.write("**📊 Análise esperada:**")
            st.write(f"- **Tipo:** {analysis['damage_type']}")
            st.write(f"- **Severidade:** {analysis['severity']}")
            st.write(f"- **Riscos:** {', '.join(analysis['risks'])}")
            st.write(f"- **Recomendações:** {', '.join(analysis['recommendations'])}")
        
        with tab4:
            st.subheader("Exemplo de Relatório de Situação")
            
            report = self.examples["situation_reports"]["earthquake_report"]
            
            st.write("**📋 Dados de entrada:**")
            st.json({
                "Tipo": report["emergency_type"],
                "Local": report["location"],
                "Descrição": report["description"],
                "Urgência": report["urgency"],
                "Vítimas": report["casualties"],
                "Recursos": report["resources_needed"]
            })
            
            st.write("**📄 Relatório gerado:**")
            st.text_area(
                "Relatório completo:",
                value=report["expected_report"],
                height=400,
                disabled=True
            )

def show_demo_data():
    """Função para exibir dados de demonstração"""
    demo = DemoExamples()
    demo.display_examples_in_streamlit()
