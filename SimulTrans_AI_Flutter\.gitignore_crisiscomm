# CrisisComm - .gitignore específico

# Cache directories
cache/
.cache/
__pycache__/

# Temporary files
temp/
tmp/
*.tmp

# Log files
logs/
*.log
crisiscomm.log

# Model files (too large for git)
models/
*.bin
*.safetensors
*.onnx
*.pth
*.pt

# Hugging Face cache
.huggingface/
transformers_cache/

# Streamlit
.streamlit/

# Exports
exports/
output/

# Environment files
.env
.env.local

# Test output
test_output/
test_results/

# Large media files for testing
test_media/
*.mp4
*.avi
*.mov
*.wav
*.mp3
*.flac

# Backup files
*.bak
*.backup

# OS files
.DS_Store
Thumbs.db
