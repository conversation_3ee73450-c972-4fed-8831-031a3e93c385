import 'dart:async';
import 'dart:isolate';
import 'dart:collection';
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import '../models/background_task.dart';
import '../utils/logger.dart';
import 'performance_service.dart';

/// Background processor for heavy computational tasks
class BackgroundProcessor {
  static final BackgroundProcessor _instance = BackgroundProcessor._internal();
  static BackgroundProcessor get instance => _instance;
  BackgroundProcessor._internal();

  final Queue<BackgroundTask> _taskQueue = Queue<BackgroundTask>();
  final Map<String, Isolate> _activeIsolates = {};
  final Map<String, ReceivePort> _receivePorts = {};
  final Map<String, StreamController<BackgroundTaskUpdate>> _taskControllers = {};
  
  bool _isProcessing = false;
  int _maxConcurrentTasks = 2;
  int _activeTasks = 0;

  bool get isProcessing => _isProcessing;
  int get queueLength => _taskQueue.length;
  int get activeTasks => _activeTasks;

  /// Initialize background processor
  Future<void> initialize() async {
    try {
      // Determine optimal number of concurrent tasks based on device capabilities
      _maxConcurrentTasks = _calculateOptimalConcurrency();
      
      Logger.info('Background processor initialized with $_maxConcurrentTasks concurrent tasks');
      
    } catch (e) {
      Logger.error('Failed to initialize background processor: $e');
      rethrow;
    }
  }

  /// Submit a task for background processing
  Future<String> submitTask(BackgroundTask task) async {
    final taskId = _generateTaskId();
    task.id = taskId;
    task.status = BackgroundTaskStatus.queued;
    task.queuedAt = DateTime.now();

    // Create task controller for progress updates
    final controller = StreamController<BackgroundTaskUpdate>.broadcast();
    _taskControllers[taskId] = controller;

    // Add to queue
    _taskQueue.add(task);
    
    Logger.info('Task submitted to background queue: $taskId (${task.type})');
    
    // Start processing if not already running
    if (!_isProcessing) {
      _startProcessing();
    }

    return taskId;
  }

  /// Get task progress stream
  Stream<BackgroundTaskUpdate>? getTaskProgress(String taskId) {
    return _taskControllers[taskId]?.stream;
  }

  /// Cancel a task
  Future<bool> cancelTask(String taskId) async {
    try {
      // Remove from queue if not started
      _taskQueue.removeWhere((task) => task.id == taskId);
      
      // Kill isolate if running
      final isolate = _activeIsolates[taskId];
      if (isolate != null) {
        isolate.kill(priority: Isolate.immediate);
        _activeIsolates.remove(taskId);
        _receivePorts[taskId]?.close();
        _receivePorts.remove(taskId);
        _activeTasks--;
      }

      // Close controller
      _taskControllers[taskId]?.close();
      _taskControllers.remove(taskId);

      Logger.info('Task cancelled: $taskId');
      return true;

    } catch (e) {
      Logger.error('Failed to cancel task $taskId: $e');
      return false;
    }
  }

  /// Get task status
  BackgroundTaskStatus? getTaskStatus(String taskId) {
    // Check active tasks
    if (_activeIsolates.containsKey(taskId)) {
      return BackgroundTaskStatus.running;
    }
    
    // Check queue
    for (final task in _taskQueue) {
      if (task.id == taskId) {
        return task.status;
      }
    }
    
    return null;
  }

  /// Clear completed tasks
  void clearCompletedTasks() {
    final completedTasks = _taskControllers.keys
        .where((taskId) => !_activeIsolates.containsKey(taskId))
        .toList();
    
    for (final taskId in completedTasks) {
      _taskControllers[taskId]?.close();
      _taskControllers.remove(taskId);
    }
    
    Logger.info('Cleared ${completedTasks.length} completed tasks');
  }

  /// Get processor statistics
  Map<String, dynamic> getStats() {
    return {
      'queue_length': _taskQueue.length,
      'active_tasks': _activeTasks,
      'max_concurrent': _maxConcurrentTasks,
      'is_processing': _isProcessing,
      'total_controllers': _taskControllers.length,
      'task_types': _getTaskTypeDistribution(),
    };
  }

  // Private methods
  void _startProcessing() {
    if (_isProcessing) return;
    
    _isProcessing = true;
    _processQueue();
  }

  Future<void> _processQueue() async {
    while (_taskQueue.isNotEmpty || _activeTasks > 0) {
      // Start new tasks if we have capacity
      while (_taskQueue.isNotEmpty && _activeTasks < _maxConcurrentTasks) {
        final task = _taskQueue.removeFirst();
        await _executeTask(task);
      }
      
      // Wait a bit before checking again
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    _isProcessing = false;
    Logger.info('Background processing queue completed');
  }

  Future<void> _executeTask(BackgroundTask task) async {
    if (task.id == null) return;

    try {
      _activeTasks++;
      task.status = BackgroundTaskStatus.running;
      task.startedAt = DateTime.now();

      // Send initial update
      _sendTaskUpdate(task.id!, BackgroundTaskUpdate(
        taskId: task.id!,
        status: BackgroundTaskStatus.running,
        progress: 0.0,
        message: 'Starting task...',
      ));

      // Create receive port for communication
      final receivePort = ReceivePort();
      _receivePorts[task.id!] = receivePort;

      // Spawn isolate based on task type
      final isolate = await _spawnTaskIsolate(task, receivePort.sendPort);
      _activeIsolates[task.id!] = isolate;

      // Listen for updates from isolate
      receivePort.listen((message) {
        _handleIsolateMessage(task.id!, message);
      });

      Logger.info('Task started in isolate: ${task.id} (${task.type})');

    } catch (e) {
      Logger.error('Failed to execute task ${task.id}: $e');
      _handleTaskError(task.id!, e.toString());
    }
  }

  Future<Isolate> _spawnTaskIsolate(BackgroundTask task, SendPort sendPort) async {
    switch (task.type) {
      case BackgroundTaskType.translation:
        return await Isolate.spawn(_translationIsolateEntry, {
          'sendPort': sendPort,
          'task': task.toJson(),
        });
      
      case BackgroundTaskType.imageProcessing:
        return await Isolate.spawn(_imageProcessingIsolateEntry, {
          'sendPort': sendPort,
          'task': task.toJson(),
        });
      
      case BackgroundTaskType.audioProcessing:
        return await Isolate.spawn(_audioProcessingIsolateEntry, {
          'sendPort': sendPort,
          'task': task.toJson(),
        });
      
      case BackgroundTaskType.cacheOptimization:
        return await Isolate.spawn(_cacheOptimizationIsolateEntry, {
          'sendPort': sendPort,
          'task': task.toJson(),
        });
      
      default:
        throw UnsupportedError('Unsupported task type: ${task.type}');
    }
  }

  void _handleIsolateMessage(String taskId, dynamic message) {
    if (message is Map<String, dynamic>) {
      final type = message['type'] as String?;
      
      switch (type) {
        case 'progress':
          _sendTaskUpdate(taskId, BackgroundTaskUpdate(
            taskId: taskId,
            status: BackgroundTaskStatus.running,
            progress: message['progress'] as double? ?? 0.0,
            message: message['message'] as String?,
          ));
          break;
          
        case 'completed':
          _handleTaskCompletion(taskId, message['result']);
          break;
          
        case 'error':
          _handleTaskError(taskId, message['error'] as String? ?? 'Unknown error');
          break;
      }
    }
  }

  void _handleTaskCompletion(String taskId, dynamic result) {
    _sendTaskUpdate(taskId, BackgroundTaskUpdate(
      taskId: taskId,
      status: BackgroundTaskStatus.completed,
      progress: 1.0,
      message: 'Task completed successfully',
      result: result,
    ));

    _cleanupTask(taskId);
    
    // Record performance metrics
    PerformanceService.instance.recordCacheOperation(
      operation: 'background_task_completion',
      duration: const Duration(milliseconds: 100),
    );
  }

  void _handleTaskError(String taskId, String error) {
    _sendTaskUpdate(taskId, BackgroundTaskUpdate(
      taskId: taskId,
      status: BackgroundTaskStatus.failed,
      progress: 0.0,
      message: 'Task failed: $error',
      error: error,
    ));

    _cleanupTask(taskId);
  }

  void _cleanupTask(String taskId) {
    // Kill isolate
    _activeIsolates[taskId]?.kill();
    _activeIsolates.remove(taskId);
    
    // Close receive port
    _receivePorts[taskId]?.close();
    _receivePorts.remove(taskId);
    
    _activeTasks--;
    
    Logger.debug('Task cleaned up: $taskId');
  }

  void _sendTaskUpdate(String taskId, BackgroundTaskUpdate update) {
    _taskControllers[taskId]?.add(update);
  }

  String _generateTaskId() {
    return 'task_${DateTime.now().millisecondsSinceEpoch}_${_taskQueue.length}';
  }

  int _calculateOptimalConcurrency() {
    // Base on available processors, but cap at reasonable limits
    final processors = Platform.numberOfProcessors;
    return (processors / 2).clamp(1, 4).round();
  }

  Map<String, int> _getTaskTypeDistribution() {
    final distribution = <String, int>{};
    
    for (final task in _taskQueue) {
      final type = task.type.toString();
      distribution[type] = (distribution[type] ?? 0) + 1;
    }
    
    return distribution;
  }

  // Isolate entry points
  static void _translationIsolateEntry(Map<String, dynamic> params) {
    final sendPort = params['sendPort'] as SendPort;
    final taskData = params['task'] as Map<String, dynamic>;
    
    try {
      // Simulate translation processing
      for (int i = 0; i <= 100; i += 10) {
        sendPort.send({
          'type': 'progress',
          'progress': i / 100.0,
          'message': 'Processing translation... $i%',
        });
        
        // Simulate work
        Future.delayed(const Duration(milliseconds: 200));
      }
      
      sendPort.send({
        'type': 'completed',
        'result': {
          'translated_text': 'Simulated translation result',
          'confidence': 0.95,
        },
      });
      
    } catch (e) {
      sendPort.send({
        'type': 'error',
        'error': e.toString(),
      });
    }
  }

  static void _imageProcessingIsolateEntry(Map<String, dynamic> params) {
    final sendPort = params['sendPort'] as SendPort;
    
    try {
      // Simulate image processing
      for (int i = 0; i <= 100; i += 20) {
        sendPort.send({
          'type': 'progress',
          'progress': i / 100.0,
          'message': 'Processing image... $i%',
        });
      }
      
      sendPort.send({
        'type': 'completed',
        'result': {'processed': true},
      });
      
    } catch (e) {
      sendPort.send({
        'type': 'error',
        'error': e.toString(),
      });
    }
  }

  static void _audioProcessingIsolateEntry(Map<String, dynamic> params) {
    final sendPort = params['sendPort'] as SendPort;
    
    try {
      // Simulate audio processing
      sendPort.send({
        'type': 'completed',
        'result': {'transcribed_text': 'Simulated transcription'},
      });
      
    } catch (e) {
      sendPort.send({
        'type': 'error',
        'error': e.toString(),
      });
    }
  }

  static void _cacheOptimizationIsolateEntry(Map<String, dynamic> params) {
    final sendPort = params['sendPort'] as SendPort;
    
    try {
      // Simulate cache optimization
      sendPort.send({
        'type': 'completed',
        'result': {'optimized_entries': 150},
      });
      
    } catch (e) {
      sendPort.send({
        'type': 'error',
        'error': e.toString(),
      });
    }
  }
}
