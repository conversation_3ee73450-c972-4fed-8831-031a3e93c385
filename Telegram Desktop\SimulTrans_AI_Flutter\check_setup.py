#!/usr/bin/env python3
"""
Script de verificação rápida para CrisisComm
Verifica se tudo está configurado corretamente
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def print_status(check_name, status, message=""):
    """Imprime status de uma verificação"""
    icon = "✅" if status else "❌"
    print(f"{icon} {check_name}")
    if message:
        print(f"   {message}")

def check_python_version():
    """Verifica versão do Python"""
    version = sys.version_info
    is_ok = version >= (3, 8)
    message = f"Python {version.major}.{version.minor}.{version.micro}"
    if not is_ok:
        message += " (Requerido: 3.8+)"
    print_status("Versão do Python", is_ok, message)
    return is_ok

def check_dependencies():
    """Verifica dependências instaladas"""
    dependencies = [
        ("streamlit", "Streamlit"),
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("accelerate", "Accelerate"),
        ("huggingface_hub", "Hugging Face Hub"),
        ("PIL", "Pillow"),
        ("numpy", "NumPy"),
        ("dotenv", "Python-dotenv")
    ]
    
    all_ok = True
    for module, name in dependencies:
        try:
            __import__(module)
            print_status(f"Dependência: {name}", True)
        except ImportError:
            print_status(f"Dependência: {name}", False, "Não instalado")
            all_ok = False
    
    return all_ok

def check_env_file():
    """Verifica arquivo .env"""
    env_exists = Path(".env").exists()
    print_status("Arquivo .env", env_exists)
    
    if env_exists:
        load_dotenv()
        token = os.getenv('HUGGING_FACE_TOKEN') or os.getenv('HF_TOKEN')
        if token:
            print_status("Token Hugging Face", True, f"Configurado: {token[:8]}...")
            return True
        else:
            print_status("Token Hugging Face", False, "Não configurado no .env")
            return False
    else:
        print_status("Token Hugging Face", False, "Arquivo .env não encontrado")
        return False

def check_directories():
    """Verifica diretórios necessários"""
    directories = [
        ("cache", "Cache geral"),
        ("cache/huggingface", "Cache Hugging Face"),
        ("temp", "Temporário"),
        ("logs", "Logs")
    ]
    
    all_ok = True
    for dir_path, name in directories:
        exists = Path(dir_path).exists()
        print_status(f"Diretório: {name}", exists)
        if not exists:
            all_ok = False
    
    return all_ok

def check_model_downloaded():
    """Verifica se modelo foi baixado"""
    cache_dir = Path("./cache/huggingface")
    model_patterns = [
        "models--google--gemma-3n-e2b-it",
        "models--google--gemma-3n-E2B-it"
    ]
    
    for pattern in model_patterns:
        model_dir = cache_dir / pattern
        if model_dir.exists():
            print_status("Modelo Gemma 3n", True, f"Encontrado em {model_dir}")
            return True
    
    print_status("Modelo Gemma 3n", False, "Não encontrado no cache")
    return False

def check_gpu():
    """Verifica disponibilidade de GPU"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print_status("GPU", True, f"Disponível: {gpu_name}")
            return True
        else:
            print_status("GPU", False, "Não disponível (usará CPU)")
            return False
    except ImportError:
        print_status("GPU", False, "PyTorch não instalado")
        return False

def check_huggingface_access():
    """Verifica acesso ao Hugging Face"""
    try:
        from huggingface_hub import whoami
        load_dotenv()
        
        token = os.getenv('HUGGING_FACE_TOKEN') or os.getenv('HF_TOKEN')
        if not token:
            print_status("Acesso Hugging Face", False, "Token não configurado")
            return False
        
        # Tentar fazer login
        from huggingface_hub import login
        login(token=token, add_to_git_credential=False)
        
        user_info = whoami()
        print_status("Acesso Hugging Face", True, f"Logado como: {user_info['name']}")
        return True
        
    except Exception as e:
        print_status("Acesso Hugging Face", False, f"Erro: {str(e)}")
        return False

def check_model_access():
    """Verifica acesso ao modelo específico"""
    try:
        from huggingface_hub import model_info
        
        model_name = "google/gemma-3n-e2b-it"
        info = model_info(model_name)
        print_status("Acesso ao Modelo", True, f"Modelo acessível: {info.modelId}")
        return True
        
    except Exception as e:
        print_status("Acesso ao Modelo", False, f"Erro: {str(e)}")
        return False

def show_summary(checks_passed, total_checks):
    """Mostra resumo das verificações"""
    print("\n" + "="*60)
    print("📊 RESUMO DA VERIFICAÇÃO")
    print("="*60)
    
    percentage = (checks_passed / total_checks) * 100
    
    if percentage == 100:
        print("🎉 TUDO OK! Sistema pronto para uso.")
    elif percentage >= 80:
        print("⚠️  Quase pronto! Alguns itens precisam de atenção.")
    else:
        print("❌ Sistema não está pronto. Vários itens precisam ser corrigidos.")
    
    print(f"\n✅ Verificações passaram: {checks_passed}/{total_checks} ({percentage:.0f}%)")
    
    if percentage < 100:
        print("\n🔧 Próximos passos:")
        if not Path(".env").exists():
            print("1. Copie .env.example para .env e configure")
        if not os.getenv('HUGGING_FACE_TOKEN'):
            print("2. Configure HUGGING_FACE_TOKEN no .env")
        print("3. Execute: python download_model.py")
        print("4. Execute: pip install -r requirements.txt")

def main():
    """Função principal"""
    print("🔍 CrisisComm - Verificação de Configuração")
    print("="*60)
    
    checks = [
        ("Python", check_python_version),
        ("Dependências", check_dependencies),
        ("Arquivo .env", check_env_file),
        ("Diretórios", check_directories),
        ("GPU", check_gpu),
        ("Acesso HF", check_huggingface_access),
        ("Acesso Modelo", check_model_access),
        ("Modelo Baixado", check_model_downloaded)
    ]
    
    checks_passed = 0
    total_checks = len(checks)
    
    for name, check_func in checks:
        print(f"\n🔍 Verificando {name}...")
        try:
            if check_func():
                checks_passed += 1
        except Exception as e:
            print_status(name, False, f"Erro: {str(e)}")
    
    show_summary(checks_passed, total_checks)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Verificação cancelada.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
