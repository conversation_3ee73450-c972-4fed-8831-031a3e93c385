<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application>
	<name>CreativeCloud</name>
	<platform>win64</platform>
	<lbsurl>http://ccmdl.adobe.com/AdobeProducts/KCCC/1/win32/CreativeCloudSet-Up.exe</lbsurl>
	<packageSets>
		<packageSet>
			<name>AAM</name>
			<installPath>[AAM_PATH]</installPath>
			<sequenceNumber>4</sequenceNumber>
			<packages>
				<package>
					<name>IPC</name>
					<sequenceNumber>10</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\AAM\IPC\IPC.pimx</pimxPath>
				</package>
			</packages>
			<filters>
				<filter type="operatingSystem">
					<config>10.0.0</config>
				</filter>
			</filters>
			<additionalInfo>
				<skipProperties>
					<property>skipAIM</property>
				</skipProperties>
				<unzipRetryCount>5</unzipRetryCount>
				<uninstallPackages>
					<uninstallPackage>
						<name>AdobeGCClient</name>
						<sequenceNumber>0</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCM</name>
						<sequenceNumber>10</sequenceNumber>
					</uninstallPackage>
				</uninstallPackages>
			</additionalInfo>
		</packageSet>
		<packageSet>
			<name>ADC</name>
			<installPath>[ADC_PATH]</installPath>
			<sequenceNumber>5</sequenceNumber>
			<packages>
				<package>
					<name>Runtime</name>
					<sequenceNumber>0</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\Runtime\Runtime.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipRuntime</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
				<package>
					<name>IPCBox</name>
					<sequenceNumber>5</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\IPCBox\IPCBox.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipIPCBox</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<!-- <package>
					<name>ADS</name>
					<sequenceNumber>10</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\ADS\ADS.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipADS</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>Core</name>
					<sequenceNumber>20</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\Core\Core.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCorePackage</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CoreExt</name>
					<sequenceNumber>50</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\CoreExt\CoreExt.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCoreExt</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>ElevationManager</name>
					<sequenceNumber>60</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\ElevationManager\ElevationManager.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipElevationManager</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>TCC</name>
					<sequenceNumber>70</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\TCC\TCC.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipTCC</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>Notifications</name>
					<sequenceNumber>80</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\Notifications\Notifications.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipNotifications</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package> -->
				<package>
					<name>HDBox</name>
					<sequenceNumber>100</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\HDBox\HDBox.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipHDBox</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
				<!-- <package>
					<name>DEBox</name>
					<sequenceNumber>110</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\DEBox\DEBox.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipDEBox</property>
							<property>skipAIM</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
				<package>
					<name>NHEX</name>
					<sequenceNumber>130</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\NHEX\NHEX.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipNHEX</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package> -->
				<package>
					<name>LCC</name>
					<sequenceNumber>150</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\LCC\LCC.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipLCC</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<!-- <package>
					<name>AdobeGenuineClient</name>
					<sequenceNumber>160</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\AdobeGenuineClient\AdobeGenuineClient.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipAdobeGenuineClient</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
				<package>
					<name>FilesPanel</name>
					<sequenceNumber>180</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\FilesPanel\FilesPanel.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipFilesPanel</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>FontsPanel</name>
					<sequenceNumber>190</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\FontsPanel\FontsPanel.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipFontsPanel</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>AppsPanel</name>
					<sequenceNumber>200</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC\AppsPanel\AppsPanel.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipAppsPanel</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package> -->
			</packages>
			<filters>
				<filter type="operatingSystem">
					<config>10.0.0</config>
				</filter>
			</filters>
			<additionalInfo>
				<skipProperties>
					<property>skipADC</property>
				</skipProperties>
				<launchExecutables/>
				<uninstallPackages>
					<uninstallPackage>
						<name>CEF</name>
						<sequenceNumber>0</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>HEX</name>
						<sequenceNumber>10</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SignInApp</name>
						<sequenceNumber>20</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCExchangePlugin</name>
						<sequenceNumber>30</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>UPI</name>
						<sequenceNumber>40</sequenceNumber>
					</uninstallPackage>
				</uninstallPackages>
				<unzipRetryCount>5</unzipRetryCount>
			</additionalInfo>
		</packageSet>
		<!-- <packageSet>
			<name>ADC64</name>
			<installPath>[ADC_PATH]</installPath>
			<sequenceNumber>6</sequenceNumber>
			<packages>
				<package>
					<name>Core64</name>
					<sequenceNumber>20</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\Core64\Core64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCore64Package</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>HEX64</name>
					<sequenceNumber>30</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\HEX64\HEX64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipHEX64</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CEF64</name>
					<sequenceNumber>40</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\CEF64\CEF64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCEF64</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CoreExt64</name>
					<sequenceNumber>50</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\CoreExt64\CoreExt64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCoreExt64</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>TCC64</name>
					<sequenceNumber>70</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\TCC64\TCC64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipTCC64</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>RemoteCoreExts</name>
					<sequenceNumber>80</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\RemoteCoreExts\RemoteCoreExts.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipRemoteCoreExts</property>
							<property>skipAIM</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>UPI64</name>
					<sequenceNumber>90</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\UPI64\UPI64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipUPI</property>
							<property>skipAIM</property>
						</skipProperties>
						<type>remote</type>
					</additionalInfo>
				</package>
				<package>
					<name>NVP</name>
					<sequenceNumber>100</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\NVP\NVP.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipNVP</property>
							<property>skipAIM</property>
						</skipProperties>
						<type>remote</type>
					</additionalInfo>
				</package>
				<package>
					<name>ADBox</name>
					<sequenceNumber>110</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\ADBox\ADBox.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipADBox</property>
							<property>skipAIM</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
				<package>
					<name>NGL</name>
					<sequenceNumber>120</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ADC64\NGL\NGL.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipNGL</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
			</packages>
			<filters>
				<filter type="operatingSystem">
					<config>10.0.0</config>
				</filter>
			</filters>
			<additionalInfo>
				<skipProperties>
					<property>skipADC64</property>
				</skipProperties>
				<launchExecutables/>
				<uninstallPackages/>
				<unzipRetryCount>5</unzipRetryCount>
			</additionalInfo>
		</packageSet>
		<packageSet>
			<name>ACC</name>
			<installPath>[ACC_PATH]</installPath>
			<sequenceNumber>7</sequenceNumber>
			<packages>
				<package>
					<name>Utils</name>
					<sequenceNumber>20</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC\Utils\Utils.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipUtils</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>ACCC</name>
					<sequenceNumber>10</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC\ACCC\ACCC.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipACCPackage</property>
						</skipProperties>
						<type>external</type>
					</additionalInfo>
				</package>
			</packages>
			<filters>
				<filter type="operatingSystem">
					<config>10.0.0</config>
				</filter>
			</filters>
			<additionalInfo>
				<skipProperties>
					<property>skipACC</property>
					<property>skipAIM</property>
				</skipProperties>
				<launchExecutables/>
				<uninstallPackages>
					<uninstallPackage>
						<name>ServiceManager</name>
						<sequenceNumber>0</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ANSClientV1</name>
						<sequenceNumber>10</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>LiveTypeV1</name>
						<sequenceNumber>20</sequenceNumber>
						<invokeModifier>false</invokeModifier>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSyncV1</name>
						<sequenceNumber>30</sequenceNumber>
						<invokeModifier>false</invokeModifier>
					</uninstallPackage>
					<uninstallPackage>
						<name>ANSClient</name>
						<sequenceNumber>40</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>Runtime</name>
						<sequenceNumber>50</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>NEX</name>
						<sequenceNumber>60</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CEF</name>
						<sequenceNumber>70</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>HEX</name>
						<sequenceNumber>80</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>Core</name>
						<sequenceNumber>90</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreExt</name>
						<sequenceNumber>100</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ElevationManager</name>
						<sequenceNumber>110</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SignInApp</name>
						<sequenceNumber>120</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>StockPanel</name>
						<sequenceNumber>130</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>Redirection</name>
						<sequenceNumber>140</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>AssetsPanel</name>
						<sequenceNumber>150</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCLibrary</name>
						<sequenceNumber>160</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSync</name>
						<sequenceNumber>170</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCXProcess</name>
						<sequenceNumber>180</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSyncExtension</name>
						<sequenceNumber>190</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>MarketPanel</name>
						<sequenceNumber>200</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SPanel</name>
						<sequenceNumber>210</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCSyncPlugin</name>
						<sequenceNumber>220</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>DesignLibraryPlugin</name>
						<sequenceNumber>230</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ExchangePlugin</name>
						<sequenceNumber>240</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>LiveType</name>
						<sequenceNumber>250</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SynKit</name>
						<sequenceNumber>260</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>BehancePanel</name>
						<sequenceNumber>270</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>HomePanel</name>
						<sequenceNumber>290</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>FontsPanel</name>
						<sequenceNumber>300</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>FilesPanel</name>
						<sequenceNumber>310</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>AppsPanel</name>
						<sequenceNumber>320</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>DevAssist</name>
						<sequenceNumber>330</sequenceNumber>
					</uninstallPackage>
				</uninstallPackages>
				<unzipRetryCount>5</unzipRetryCount>
			</additionalInfo>
		</packageSet>
		<packageSet>
			<name>ACC64</name>
			<installPath>[ACC_PATH]</installPath>
			<sequenceNumber>8</sequenceNumber>
			<packages>
				<package>
					<name>ACCC64</name>
					<sequenceNumber>10</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\ACCC64\ACCC64.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipACCPackage</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CCDContainer</name>
					<sequenceNumber>220</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CCDContainer\CCDContainer.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CCDContainerJS</name>
					<sequenceNumber>225</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CCDContainerJS\CCDContainerJS.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Apps</name>
					<sequenceNumber>230</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Apps\Apps.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Assets</name>
					<sequenceNumber>240</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Assets\Assets.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Search</name>
					<sequenceNumber>250</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Search\Search.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>CloudSync</name>
					<sequenceNumber>260</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CloudSync\CloudSync.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>Fonts</name>
					<sequenceNumber>270</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Fonts\Fonts.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Discover</name>
					<sequenceNumber>280</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Discover\Discover.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>SAM</name>
					<sequenceNumber>290</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\SAM\SAM.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>WebFS</name>
					<sequenceNumber>310</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\WebFS\WebFS.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>true</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>Plugins</name>
					<sequenceNumber>320</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Plugins\Plugins.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
							<property>skipPlugins</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>StarterLibs</name>
					<sequenceNumber>330</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\StarterLibs\StarterLibs.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
							<property>skipStarterLibs</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Stock</name>
					<sequenceNumber>340</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Stock\Stock.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
							<property>skipStock</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>CloudDocs</name>
					<sequenceNumber>350</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CloudDocs\CloudDocs.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>CCLibs</name>
					<sequenceNumber>360</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CCLibs\CCLibs.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>ShareSheet</name>
					<sequenceNumber>370</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\ShareSheet\ShareSheet.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Home</name>
					<sequenceNumber>390</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Home\Home.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>Spaces</name>
					<sequenceNumber>400</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\Spaces\Spaces.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>3DI</name>
					<sequenceNumber>410</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\3DI\3DI.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>OneUp</name>
					<sequenceNumber>420</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\OneUp\OneUp.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
					</additionalInfo>
				</package>
				<package>
					<name>CCExpress</name>
					<sequenceNumber>430</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\CCExpress\CCExpress.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
				<package>
					<name>UAB</name>
					<sequenceNumber>440</sequenceNumber>
					<optional>false</optional>
					<pimxPath>\ACC64\UAB\UAB.pimx</pimxPath>
					<additionalInfo>
						<restartRequired>false</restartRequired>
						<skipProperties>
							<property>skipCCD</property>
						</skipProperties>
						<type>ui</type>
					</additionalInfo>
				</package>
			</packages>
			<filters>
				<filter type="operatingSystem">
					<config>10.0.0</config>
				</filter>
			</filters>
			<additionalInfo>
				<skipProperties>
					<property>skipACC</property>
					<property>skipAIM</property>
				</skipProperties>
				<launchExecutables/>
				<uninstallPackages>
					<uninstallPackage>
						<name>ServiceManager</name>
						<sequenceNumber>0</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ANSClientV1</name>
						<sequenceNumber>10</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>LiveTypeV1</name>
						<sequenceNumber>20</sequenceNumber>
						<invokeModifier>false</invokeModifier>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSyncV1</name>
						<sequenceNumber>30</sequenceNumber>
						<invokeModifier>false</invokeModifier>
					</uninstallPackage>
					<uninstallPackage>
						<name>ANSClient</name>
						<sequenceNumber>40</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>NEX</name>
						<sequenceNumber>60</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>HEX</name>
						<sequenceNumber>80</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>Core</name>
						<sequenceNumber>90</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreExt</name>
						<sequenceNumber>100</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ElevationManager</name>
						<sequenceNumber>110</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SignInApp</name>
						<sequenceNumber>120</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>StockPanel</name>
						<sequenceNumber>130</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCD</name>
						<sequenceNumber>140</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>AssetsPanel</name>
						<sequenceNumber>150</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCLibrary</name>
						<sequenceNumber>160</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSync</name>
						<sequenceNumber>170</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCXProcess</name>
						<sequenceNumber>180</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CoreSyncExtension</name>
						<sequenceNumber>190</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>MarketPanel</name>
						<sequenceNumber>200</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SPanel</name>
						<sequenceNumber>210</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCSyncPlugin</name>
						<sequenceNumber>220</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>DesignLibraryPlugin</name>
						<sequenceNumber>230</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>ExchangePlugin</name>
						<sequenceNumber>240</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>LiveType</name>
						<sequenceNumber>250</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>SynKit</name>
						<sequenceNumber>260</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>Redirection</name>
						<sequenceNumber>300</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>BehancePanel</name>
						<sequenceNumber>320</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>HomePanel</name>
						<sequenceNumber>330</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>FontsPanel</name>
						<sequenceNumber>340</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>FilesPanel</name>
						<sequenceNumber>350</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>AppsPanel</name>
						<sequenceNumber>360</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDDiscover</name>
						<sequenceNumber>370</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDFonts</name>
						<sequenceNumber>380</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDCloudSync</name>
						<sequenceNumber>390</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDSearch</name>
						<sequenceNumber>400</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDPlugins</name>
						<sequenceNumber>410</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDStarterLibs</name>
						<sequenceNumber>420</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDAssets</name>
						<sequenceNumber>430</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>CCDApps</name>
						<sequenceNumber>440</sequenceNumber>
					</uninstallPackage>
					<uninstallPackage>
						<name>DevAssist</name>
						<sequenceNumber>450</sequenceNumber>
					</uninstallPackage>
				</uninstallPackages>
				<unzipRetryCount>5</unzipRetryCount>
			</additionalInfo>
		</packageSet> -->
	</packageSets>
	<version>5.9.0.372</version>
</application>