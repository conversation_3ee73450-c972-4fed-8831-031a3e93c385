#!/usr/bin/env python3
"""
🏥 DermatoGemma Multi-Detection System v2.0
Automated Installation Script

This script automatically installs all required dependencies and sets up the environment.
"""

import sys
import subprocess
import os
from pathlib import Path
import platform

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        print("   Please upgrade Python and try again")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_requirements():
    """Install requirements from requirements.txt"""
    print("\n📦 Installing required packages...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        # Upgrade pip first
        print("   🔄 Upgrading pip...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        
        # Install requirements
        print("   📥 Installing dependencies...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All dependencies installed successfully")
            return True
        else:
            print(f"❌ Installation failed: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating project directories...")
    
    directories = [
        "models",
        "data/test_images",
        "results/analyses",
        "results/reports",
        "logs",
        "cache/temp"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}")
    
    print("✅ All directories created")

def check_system_requirements():
    """Check system requirements"""
    print("\n🖥️ Checking system requirements...")
    
    # Check OS
    os_name = platform.system()
    print(f"   OS: {os_name} {platform.release()}")
    
    # Check available memory (if psutil is available)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"   RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 8:
            print("   ⚠️ Warning: Less than 8GB RAM detected. Performance may be limited.")
        else:
            print("   ✅ Sufficient RAM available")
            
    except ImportError:
        print("   ℹ️ Memory check skipped (psutil not available)")
    
    # Check disk space
    try:
        disk_usage = os.statvfs('.') if hasattr(os, 'statvfs') else None
        if disk_usage:
            free_gb = (disk_usage.f_bavail * disk_usage.f_frsize) / (1024**3)
            print(f"   Storage: {free_gb:.1f} GB free")
            
            if free_gb < 2:
                print("   ⚠️ Warning: Less than 2GB free space. Consider freeing up space.")
            else:
                print("   ✅ Sufficient storage available")
    except:
        print("   ℹ️ Storage check skipped")

def test_installation():
    """Test if installation was successful"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test core imports
        import numpy
        print("   ✅ NumPy")
        
        import cv2
        print("   ✅ OpenCV")
        
        import PIL
        print("   ✅ Pillow")
        
        import customtkinter
        print("   ✅ CustomTkinter")
        
        import requests
        print("   ✅ Requests")
        
        print("✅ All core dependencies working")
        return True
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main installation function"""
    print("🏥 DermatoGemma Multi-Detection System v2.0")
    print("=" * 50)
    print("🚀 Starting automated installation...\n")
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check system requirements
    check_system_requirements()
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Installation failed!")
        print("Please check the error messages above and try again.")
        return 1
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed!")
        print("Some dependencies may not be working correctly.")
        return 1
    
    # Success message
    print("\n🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Run the application: python main.py --modern")
    print("   2. Load a dermatological image")
    print("   3. Choose analysis mode (Comprehensive or Targeted)")
    print("   4. Start analysis")
    print("\n💡 For help: python main.py --help")
    print("\n🏥 Ready for dermatological AI analysis!")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
