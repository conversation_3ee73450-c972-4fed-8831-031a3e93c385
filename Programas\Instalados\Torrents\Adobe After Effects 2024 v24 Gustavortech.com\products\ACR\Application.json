{"Name": "Camera Raw", "SAPCode": "ACR", "CodexVersion": "16.0", "AssetGuid": "0309cb1a-20eb-4dfe-9b1b-aeb8000a2bc3", "ProductVersion": "**********", "BaseVersion": "9.6", "LbsUrl": "/AdobeProducts/ACR/16/win32/AAMmetadataLS20/CreativeCloudSet-Up.exe", "Platform": "win32", "SupportedLanguages": {"Language": [{"value": "", "locale": "cs_CZ"}, {"value": "", "locale": "da_DK"}, {"value": "", "locale": "de_DE"}, {"value": "", "locale": "en_AE"}, {"value": "", "locale": "en_GB"}, {"value": "", "locale": "en_IL"}, {"value": "", "locale": "en_US"}, {"value": "", "locale": "es_ES"}, {"value": "", "locale": "es_MX"}, {"value": "", "locale": "fi_FI"}, {"value": "", "locale": "fr_CA"}, {"value": "", "locale": "fr_FR"}, {"value": "", "locale": "fr_<PERSON>"}, {"value": "", "locale": "hu_HU"}, {"value": "", "locale": "it_IT"}, {"value": "", "locale": "ja_<PERSON>"}, {"value": "", "locale": "ko_KR"}, {"value": "", "locale": "nb_NO"}, {"value": "", "locale": "nl_NL"}, {"value": "", "locale": "pl_PL"}, {"value": "", "locale": "pt_BR"}, {"value": "", "locale": "ru_RU"}, {"value": "", "locale": "sv_SE"}, {"value": "", "locale": "tr_TR"}, {"value": "", "locale": "uk_UA"}, {"value": "", "locale": "zh_CN"}, {"value": "", "locale": "zh_TW"}]}, "LanguageSet": "LS20", "ConflictingProcesses": {"ConflictingProcess": [{"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2017", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2017\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2017 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2017 (32 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate CC 2019", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate CC 2019\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate Prerelease", "Reason": "Loads Camera Raw plugin", "RelativePath": "[InstallDir]\\Adobe Animate Prerelease\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2017", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2017\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop (Prerelease)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop (Prerelease)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2019", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2019\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2015", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2015\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2014", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2014\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects 2021", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects 2021\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2015.5", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2015.5\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge (Prerelease)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge (Prerelease)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge 2021", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge 2021\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2019 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2019 (32 Bit)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC (64 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC (64 Bit)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2018", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2018\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2022", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2022\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2015", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2015\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate CC 2017", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate CC 2017\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge 2020", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge 2020\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2018 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2018 (32 Bit)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2019", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2019\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2015.5 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2015.5 (32 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2015", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2015\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge 2022", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge 2022\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Dd][Oo][Bb][Ee] [Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge 2024", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge 2024\\Adobe Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2014 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2014 (32 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop (Beta)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop (Beta)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2024", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2024\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate CC 2015.2", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate CC 2015.2\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2014", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2014\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2015 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2015 (32 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate 2024", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate 2024\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2020", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2020\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2015 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2015 (32 Bit)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2018", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2018\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2017 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2017 (32 Bit)\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2023", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2023\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects 2020", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects 2020\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2018", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2018\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate CC 2018", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate CC 2018\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC (64 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC (64 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge CC 2019", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge CC 2019\\Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2018 (32 Bit)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2018 (32 Bit)\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx] [(][Bb][Ee][Tt][Aa][)]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects (Beta)", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects (Beta)\\Support Files\\AfterFX (Beta).exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects CC 2015.3", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects CC 2015.3\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Dd][Oo][Bb][Ee] [Bb][Rr][Ii][Dd][Gg][Ee].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Bridge 2023", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Bridge 2023\\Adobe Bridge.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2019", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2019\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate 2020", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate 2020\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop CC 2017", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop CC 2017\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate 2021", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate 2021\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate 2022", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate 2022\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects 2024", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects 2024\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects 2022", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects 2022\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Pp][Hh][Oo][Tt][Oo][Ss][Hh][Oo][Pp].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Photoshop 2021", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Photoshop 2021\\Photoshop.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Nn][Ii][Mm][Aa][Tt][Ee]\\.[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe Animate 2023", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe Animate 2023\\Animate.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}, {"RegularExpression": "^[Aa][Ff][Tt][Ee][Rr][Ff][Xx].[Ee][Xx][Ee]$", "ProcessDisplayName": "Adobe After Effects 2023", "Reason": "Loads Camera Raw plugin", "ParentRegularExpression": "", "ParentDisplayName": "", "RelativePath": "[InstallDir]\\Adobe After Effects 2023\\Support Files\\AfterFX.exe", "headless": false, "forceKillAllowed": false, "adobeOwned": true}]}, "AMTConfig": {"appID": "{ACR-16.0-32-ADBEADBEADBEADBEADBEADBE}"}, "Packages": {"Package": [{"Type": "core", "PackageName": "CameraRawRIBSCoExistPackage", "PackageVersion": "*********", "DownloadSize": 46077, "ExtractSize": 380928, "Path": "/AdobeProducts/ACR/*********/win32/c1d120a1-ab54-4b3b-af33-c00295c2515b/CameraRawRIBSCoExistPackage.zip", "Features": {"Feature": []}, "Format": "zip", "ValidationURL": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/27870", "packageHashKey": "72d8e43ee0d796905269eb0ae530d4f6", "DeltaPackages": [], "ValidationURLs": {"TYPE2": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/27870?algorithm=TYPE2", "TYPE1": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/27870?algorithm=TYPE1"}, "ProcessorFamily": "32-bit", "InstallSequenceNumber": 1001, "fullPackageName": "CameraRawRIBSCoExistPackage.zip", "PackageValidation": "21KG+f3ioVRGIRLG7inABtGrV6z6AC6nWBcoMC30dxi0Lv/kpOdAmBg6eMR66fupDNorYPUMKY+Ys1zByyMvCN83KXGuscP9PZo+JN2WWfKZdz0t4Jt5ivMtrAXCMzULf2WOkM0bsD90rIGuunw9pBnhS8UQvtuFucLaiehFFHdCxqSQ3sn0z+EJLs+gH0nJUEq5j+B0XmQ91k8Mli9VEm40gxA7v09P/++t0pXzqGdXvh7H8QY4lpSt9piA22zYXKxjFemzPEJGJJBLFOXb1a/OqHlAX9a9LpGKdVpt5IdQA+cd1KlyD0HOXoE3ilar14MSpeltgmIcj8y891BF0HFv5ZxOMrVZTF60tm2xi3ynRpCEMD/sTnwA3i6i7JguPInfeKRny1GYxcSSai70lcwANacjhwyqqcy0JybBNguTNqSoqLnxLN1Z3l2fAHqbfOfC8BjoB6Ge4QeLKC4vpRzk7eOQNZrLqJ3dUaMgrGI6SJ5jwF43sQvKBAlJLMCI", "AliasPackageName": "1", "PackageScheme": "hd-standard"}, {"Type": "core", "PackageName": "AdobeCameraRaw8.0All-x64", "PackageVersion": "**********", "DownloadSize": 143047455, "ExtractSize": 220140544, "Path": "/AdobeProducts/ACR/**********/win32/0309cb1a-20eb-4dfe-9b1b-aeb8000a2bc3/AdobeCameraRaw8.0All-x64.zip", "Features": {"Feature": []}, "Format": "zip", "ValidationURL": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70546", "packageHashKey": "e23d20885a827217e556dbd1288c22e4", "DeltaPackages": [], "ValidationURLs": {"TYPE2": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70546?algorithm=TYPE2", "TYPE1": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70546?algorithm=TYPE1"}, "Condition": "[OSProcessorFamily]==64-bit &&[OSVersion]>=10.0", "ProcessorFamily": "64-bit", "InstallSequenceNumber": 1002, "fullPackageName": "AdobeCameraRaw8.0All-x64.zip", "PackageValidation": "Hocs65wf7Zb+fP+9RE3pjY4oKnPtPQTHfxkXnGCsgAp+glBcGxppd6Jkgq7DC/FK8izgWTQN+heP2UDJ0Cgc+0/Je/dhSEt7oxQi/Ii8QRt2jI4rCFqQFF9G0e+dzFDOtuQ8PLiC+QhMopfmMcA9zUqm/OgSLYVdgl17NfFjPU/lk3i2sz8S34ntSBpL0UGAA9vKcQ5eti3iEYii16iNdqqLp8IpkOGObKvi5tEccFi8cguwjAR4O0uJftyg7rXUr/SiJJehvTl4oxEMpQONDDfpkV/J8Y2IDesTiN61PDveHkGjc3iFCXT0/+Ca/gueIVJXzig9jSmhl09q/vStP+IIWhStghqqXU1isWdMPoVxEAoiY0Fvzc6NINnOK4jBz4BOKDu/DSQ8OX6YV6Aj3L1CjxGG/1xcJunwRQcglq5frehFnjaxmMT/QBeA0FUvEf+NVhOBRza2qbRPdRnIPZUTVRy8mw7qf81qVXHYlUiOHq5pP99APJSqabnMPYHa", "AliasPackageName": "2", "RIBSCoexistenceCode": "{5CEA575D-1E1C-4A92-BEC1-3CADD429E4EA}", "PackageScheme": "hd-standard"}, {"Type": "core", "PackageName": "AdobeCameraRaw8.0All-x64-support", "PackageVersion": "*********", "DownloadSize": 84062606, "ExtractSize": 121006656, "Path": "/AdobeProducts/ACR/*********/win32/8ac0f615-2649-468a-9380-635c6ba6459b/AdobeCameraRaw8.0All-x64-support.zip", "Features": {"Feature": []}, "Format": "zip", "ValidationURL": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/54392", "packageHashKey": "5d1720de668ca7d0205e08e32144b58e", "DeltaPackages": [], "ValidationURLs": {"TYPE2": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/54392?algorithm=TYPE2", "TYPE1": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/54392?algorithm=TYPE1"}, "Condition": "[OSProcessorFamily]==64-bit &&[OSVersion]<10.0", "ProcessorFamily": "64-bit", "InstallSequenceNumber": 1003, "fullPackageName": "AdobeCameraRaw8.0All-x64-support.zip", "PackageValidation": "mIAVUqmWOtO9G1pdXXAjm7/15L8VzDU/HUz5vHyEZAplk0kQ73JPB/I8DrgWs/WovJQG2RODY5LuVL002qI/eoXwv2HaZcdHHVOK/4Id9so1yQkwSU4C69UMVEgpHVS1D6JEhZBpdkPmyXZpkIovx9Vg2SRAoTSi9sc7HrCwDf2UXi/hvZtrZKxV+PHuqlV6DXclQUiLJDAj8VPm1cv+7cxhSmXvGlLTt7V0Pax9TaH6+SF2p6O7AiMJU1TM/ODKXDhXBIJ3ssbaHnJtiRiXP1LXHgCfbPBrgmBeOe/422BMqNyuNUCV4Vw4IJgLrcH+WX3kgFzn2wAaWTXEtNJ+dWyJ6MHlCYuPbgugzGtrLNqB4QZ1e0XYq1N9P0qVcfLHrwhQyGTYtBtpA7mJ6cRc885IHnqpSHv7+aixyNiUgwMMEF5NIsr+1h9LeGDFGqTuK5CPeQ2dvv8uVrXDlCS2tN/HPl41E2srDNWr/cjTUrQVTb/N+4hVj0A6m29So3ws", "AliasPackageName": "3", "RIBSCoexistenceCode": "{5CEA575D-1E1C-4A92-BEC1-3CADD429E4EA}", "PackageScheme": "hd-standard"}, {"Type": "core", "PackageName": "AdobeCameraRawProfile8.0All", "PackageVersion": "**********", "DownloadSize": 900582700, "ExtractSize": 1659435106, "Path": "/AdobeProducts/ACR/**********/win32/0309cb1a-20eb-4dfe-9b1b-aeb8000a2bc3/AdobeCameraRawProfile8.0All.zip", "Features": {"Feature": []}, "Format": "zip", "ValidationURL": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70547", "packageHashKey": "b73445882b25661f1430def769589591", "DeltaPackages": [], "ValidationURLs": {"TYPE2": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70547?algorithm=TYPE2", "TYPE1": "https://cdn-ffc.oobesaas.adobe.com/core/v2/validation/70547?algorithm=TYPE1"}, "ProcessorFamily": "32-bit", "InstallSequenceNumber": 1004, "fullPackageName": "AdobeCameraRawProfile8.0All.zip", "PackageValidation": "lllhuIVIvbAdPx/eLx+2RqVUf0oUc3w0WkPcMFerlwrN36pwO0YGgVJYSA8oOPuQwQS9wp3ReTYv3S6bm6JC8NuWjBLs6zuIGaE35caevkjQGa97wIYcogpoub2yCOYDJ33Ji7zThY98yltEu1fq5WOptju8I8tnyLfkOd8Zv3j9sccone25JY+lCn2Q/lWzLf3GJWvql5NN0MdmXXf8gkwSpyi/dDBWafwSmkS/aTpXcVkqHJIEu+XkYtyCJSSbPIr1nSe1xdtFdwcUc/95evWl1PGNZ0AFYPwj7BaPKg2VZzIGdVd8eUZAzORpWbo43ya/kSR0LtbxyWs+VOUjAZFXHvVJitrxYPjvfTeBp5B4LAAJ+dBnm7RlPwiZEHhX7816Pv/vM+PXnCFInbssZDPd7A5VmnV0FR8A6ttZFPKARcvLq4TU+xLJELhYNiu1WghDvQHqhoWNAJ6e9uyvn6xlOyJScZZy/C2lrUjlnfLAPX7y46jCSqxTC1WAH6YW", "AliasPackageName": "4", "RIBSCoexistenceCode": "{56DCC6F4-322E-42DA-A5DA-26BAD28C1E10}", "PackageScheme": "hd-standard"}]}, "SystemRequirement": {"OsComponent": "api-ms-win-crt-runtime-l1-1-0.dll", "SupportedOsVersionRange": [{"min": "6.1.7601", "max": "10.0.10239"}, {"min": "10.0.10241"}], "CheckCompatibility": {"Content": "var winChecks={minOSVersion:\"6.1.1.7601\",osArchitecture:\"64\",excludedOSVersion:\"10.0.0.10240\"};var macChecks={minOSVersion:\"10.11.0\"};var localizationStringMap={cs_CZ:{OperatingSystemWin:\"Windows 7 Service Pack 1 nebo novější (Poznámka: Windows 8 a Windows 10 verze 1507 nejsou podporovány)\",OperatingSystemMac:\"macOS 10.11 nebo novější\"},da_DK:{OperatingSystemWin:\"Windows 7 Service Pack 1 eller nyere (Bemærk! Windows 8 og Windows 10 version 1507 understøttes ikke)\",OperatingSystemMac:\"macOS 10.11 eller nyere\"},de_DE:{OperatingSystemWin:\"Windows 7 Service Pack 1 oder höher (Hinweis: Windows 8 und Windows 10, Version 1507 werden nicht unterstützt)\",OperatingSystemMac:\"macOS 10.11 oder höher\"},en_AE:{OperatingSystemWin:\"Windows 7 Service Pack 1 or later (Note: Windows 8 and Windows 10 version 1507 are not supported)\",OperatingSystemMac:\"macOS 10.11 or later\"},en_GB:{OperatingSystemWin:\"Windows 7 Service Pack 1 or later (Note: Windows 8 and Windows 10 version 1507 are not supported)\",OperatingSystemMac:\"macOS 10.11 or later\"},en_IL:{OperatingSystemWin:\"Windows 7 Service Pack 1 or later (Note: Windows 8 and Windows 10 version 1507 are not supported)\",OperatingSystemMac:\"macOS 10.11 or later\"},en_US:{OperatingSystemWin:\"Windows 7 Service Pack 1 or later (Note: Windows 8 and Windows 10 version 1507 are not supported)\",OperatingSystemMac:\"macOS 10.11 or later\"},es_ES:{OperatingSystemWin:\"Windows 7 Service Pack 1 o versiones posteriores. (Nota: Windows 8 y la versión 1507 de Windows 10 no son compatibles).\",OperatingSystemMac:\"macOS 10.11 o versiones posteriores\"},es_MX:{OperatingSystemWin:\"Windows 7 Service Pack 1 o versiones posteriores. (Nota: Windows 8 y la versión 1507 de Windows 10 no son compatibles).\",OperatingSystemMac:\"macOS 10.11 o versiones posteriores\"},fi_FI:{OperatingSystemWin:\"Windows 7 Service Pack 1 tai uudempi (huomaa: Windows 8:aa ja Windows 10 versio 1507:ää ei tueta)\",OperatingSystemMac:\"macOS 10.11 tai uudempi\"},fr_CA:{OperatingSystemWin:\"Windows 7 Service Pack 1 ou version ultérieure (Remarque : Windows 8 et Windows 10 version 1507 ne sont pas pris en charge)\",OperatingSystemMac:\"macOS 10.11 ou version ultérieure\"},fr_FR:{OperatingSystemWin:\"Windows 7 Service Pack 1 ou version ultérieure (Remarque : Windows 8 et Windows 10 version 1507 ne sont pas pris en charge)\",OperatingSystemMac:\"macOS 10.11 ou version ultérieure\"},fr_MA:{OperatingSystemWin:\"Windows 7 Service Pack 1 ou version ultérieure (Remarque : Windows 8 et Windows 10 version 1507 ne sont pas pris en charge)\",OperatingSystemMac:\"macOS 10.11 ou version ultérieure\"},hu_HU:{OperatingSystemWin:\"Windows 7 Service Pack 1 vagy újabb (Megjegyzés: a Windows 8 és a Windows 10 1507-es verziója nem támogatott)\",OperatingSystemMac:\"macOS 10.11 vagy újabb\"},it_IT:{OperatingSystemWin:\"Windows 7 Service Pack 1 o versione successiva (nota: Windows 8 e Windows 10 versione 1507 non sono supportati)\",OperatingSystemMac:\"macOS 10.11 o versione successiva\"},ja_JP:{OperatingSystemWin:\"Windows 7 Service Pack 1 以降 (注 : Windows 8 および Windows 10 バージョン 1507 はサポートされません)\",OperatingSystemMac:\"macOS 10.11 以降\"},ko_KR:{OperatingSystemWin:\"Windows 7 서비스 팩 1 이상(참고: Windows 8 및 Windows 10 버전 1507은 지원되지 않음)\",OperatingSystemMac:\"macOS 10.11 이상\"},nb_NO:{OperatingSystemWin:\"Windows 7 Service Pack 1 eller senere (merk at Windows 8 og Windows 10 versjon 1507 ikke støttes)\",OperatingSystemMac:\"macOS 10.11 eller senere\"},nl_NL:{OperatingSystemWin:\"Windows 7 Service Pack 1 of later (Opmerking: Windows 8 en Windows 10 versie 1507 worden niet ondersteund)\",OperatingSystemMac:\"macOS 10.11 of later\"},pl_PL:{OperatingSystemWin:\"System operacyjny Windows 7 z dodatkiem Service Pack 1 lub nowszy (Uwaga: system Windows 8 oraz system Windows 10 w wersji 1507 nie są obsługiwane)\",OperatingSystemMac:\"System operacyjny macOS w wersji 10.11 lub nowszy\"},pt_BR:{OperatingSystemWin:\"Windows 7 Service Pack 1 ou posterior (Observação: não há suporte para Windows 8 e para a versão 1507 do Windows 10)\",OperatingSystemMac:\"macOS 10.11 ou posterior\"},ru_RU:{OperatingSystemWin:\"Windows 7 Service Pack 1 или более поздней версии (Примечание: Windows 8 и Windows 10 версии 1507 не поддерживаются)\",OperatingSystemMac:\"macOS 10.11 или более поздней версии\"},sv_SE:{OperatingSystemWin:\"Windows 7 Service Pack 1 eller senare (Obs! Windows 8 och Windows 10 version 1507 stöds inte.)\",OperatingSystemMac:\"macOS 10.11 eller senare\"},tr_TR:{OperatingSystemWin:\"Windows 7 Service Pack 1 veya üzeri (Not: Windows 8 ve Windows 10 sürüm 1507desteklenmemektedir)\",OperatingSystemMac:\"macOS 10.11 veya üzeri\"},uk_UA:{OperatingSystemWin:\"Windows 7 із пакетом оновлень SP 1 aбо пізнішим (Примітка: Windows 8 і Windows 10 версії 1507 не підтримуються)\",OperatingSystemMac:\"macOS 10.11 або пізніша версія\"},zh_CN:{OperatingSystemWin:\"Windows 7 Service Pack 1或更高版本（注：Windows 8 和 Windows 10 版本 1507 不受支持）\",OperatingSystemMac:\"macOS 10.11 或更高版本\"},zh_TW:{OperatingSystemWin:\"Windows 7 Service Pack 1或更新版本 (注意: 不支援 Windows 8 和 Windows 10 版本 1507)\",OperatingSystemMac:\"macOS 10.11 或更新版本\"}};var locale=\"en_US\";function getLocalizedString(a,f,d){var c;try{c=localizationStringMap[locale][a]}catch(b){c=\"undefined\"}if(f){c=c.replace(\"{0}\",f)}if(d){c=c.replace(\"{1}\",d)}return c}function compareVersion(f,d){if(f===d){return 0}var j=f.split(\".\");var e=d.split(\".\");var c=Math.max(j.length,e.length);for(var h=0;h<c;h++){var k=0;var g=0;if(h<j.length){k=parseInt(j[h])}if(h<e.length){g=parseInt(e[h])}if(k>g){return 1}if(k<g){return -1}}return 0}function checkCompatibility(g,f){var m={};m.checkResult=\"success\";m.failingList=[];var b,j;try{b=JSON.parse(g);j=JSON.parse(f)}catch(h){m.failingList.push({type:\"generic\",message:\"Error parsing JSON\"});return JSON.stringify(m)}var a=b.context;locale=b.locale;if(!locale||!localizationStringMap[locale]){locale=\"en_US\"}var i=j.os;var l=true;var c;if(i&&i.type&&i.type.indexOf(\"win\")!=-1){c=winChecks;var d=i.version+\".\"+i.buildNumber;var k=i.architecture;if(d&&(compareVersion(d,winChecks.minOSVersion)<0||d==winChecks.excludedOSVersion)){m.failingList.push({type:\"OS\",message:getLocalizedString(\"OperatingSystemWin\")});m.checkResult=\"fail\"}}else{if(i){l=false;c=macChecks;var d=i.version;var k=i.architecture;if(d&&compareVersion(d,macChecks.minOSVersion)<0){m.failingList.push({type:\"OS\",message:getLocalizedString(\"OperatingSystemMac\")});m.checkResult=\"fail\"}}}return JSON.stringify(m)};"}}, "version": "16.0", "AppLineage": "Camera Raw", "FamilyName": "Camera Raw", "BuildGuid": "f916c8c2-e2d5-4a5f-9274-3b6420642c3f", "selfServeBuild": true, "HDBuilderVersion": "5.0.17", "IsSTI": true, "AutoUpdate": "true", "ProductDescription": {"Tagline": {"Language": [{"value": "Upravte a vylepšete své snímky RAW v aplikacích Photoshop a Bridge", "locale": "cs_CZ"}, {"value": "Edit and enhance your raw images inside Photoshop and Bridge", "locale": "en_US"}, {"value": "Edit and enhance your raw images inside Photoshop and Bridge", "locale": "en_GB"}, {"value": "Edit and enhance your raw images inside Photoshop and Bridge", "locale": "en_AE"}, {"value": "Edit and enhance your raw images inside Photoshop and Bridge", "locale": "en_IL"}, {"value": "Edite y mejore sus imágenes RAW en Photoshop y Bridge", "locale": "es_ES"}, {"value": "Edite y mejore sus imágenes RAW en Photoshop y Bridge", "locale": "es_MX"}, {"value": "Rediger og forbedr dine raw-billeder inden i Photoshop og Bridge", "locale": "da_DK"}, {"value": "Bearbeiten und Verbessern Ihrer Raw-Bilder in Photoshop und Bridge", "locale": "de_DE"}, {"value": "Muok<PERSON>a ja parantele raw-kuvia Photoshopissa ja <PERSON>ä", "locale": "fi_FI"}, {"value": "Modifiez et améliorez vos images Raw au sein de Photoshop et de Bridge", "locale": "fr_FR"}, {"value": "Modifiez et améliorez vos images Raw au sein de Photoshop et de Bridge", "locale": "fr_CA"}, {"value": "Modifiez et améliorez vos images Raw au sein de Photoshop et de Bridge", "locale": "fr_<PERSON>"}, {"value": "Szerkessze és tökéletesítse feldolgozatlan képeit a Photoshop és a Bridge programban", "locale": "hu_HU"}, {"value": "Modificate e migliorate le immagini raw in Photoshop e Bridge", "locale": "it_IT"}, {"value": "Photoshop および Bridge で RAW 画像を編集または強調", "locale": "ja_<PERSON>"}, {"value": "Photoshop 및 Bridge 내에서 Raw 이미지를 편집하고 개선해 보십시오", "locale": "ko_KR"}, {"value": "Rediger og forbedre RAW-bildene dine i Photoshop og Bridge", "locale": "nb_NO"}, {"value": "Bewerk en verbeter uw RAW-afbeeldingen in Photoshop en Bridge", "locale": "nl_NL"}, {"value": "Edytuj i ulepszaj surowe obrazy w programach Photoshop i Bridge", "locale": "pl_PL"}, {"value": "Edite e aprimore suas imagens raw dentro do Photoshop e do Bridge", "locale": "pt_BR"}, {"value": "Редактируйте и ретушируйте изображения Raw прямо в Photoshop и Bridge", "locale": "ru_RU"}, {"value": "Redigera och förbättra dina Raw-bilder i Photoshop och Bridge", "locale": "sv_SE"}, {"value": "Photoshop'un ve Bridge'in içinde ham görüntülerinizi düzenleyin ve iyileştirin", "locale": "tr_TR"}, {"value": "Редагуйте та вдосконалюйте зображення RAW у програмах Photoshop і Bridge", "locale": "uk_UA"}, {"value": "在 Photoshop 和 Bridge 中编辑和增强您的原始图像", "locale": "zh_CN"}, {"value": "編輯與加強 Photoshop 及 Bridge 內的 Raw 影像", "locale": "zh_TW"}]}}, "Cdn": {"Secure": "https://ccmdls.adobe.com", "NonSecure": "http://ccmdl.adobe.com"}, "IsNonCCProduct": false, "CompressionType": "Zip-Lzma2", "TutorialUrl": {"Stage": {"Language": [{"value": "https://adobe.com/go/acr_tutorials_ru/", "locale": "ru_RU"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_CA"}, {"value": "https://adobe.com/go/acr_tutorials_hu/", "locale": "hu_HU"}, {"value": "https://adobe.com/go/acr_tutorials_tr/", "locale": "tr_TR"}, {"value": "https://adobe.com/go/acr_tutorials_tw/", "locale": "zh_TW"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_FR"}, {"value": "https://adobe.com/go/acr_tutorials_pl/", "locale": "pl_PL"}, {"value": "https://adobe.com/go/acr_tutorials_dk/", "locale": "da_DK"}, {"value": "https://adobe.com/go/acr_tutorials_es/", "locale": "es_ES"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_US"}, {"value": "https://adobe.com/go/acr_tutorials_cn/", "locale": "zh_CN"}, {"value": "https://adobe.com/go/acr_tutorials_nl/", "locale": "nl_NL"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_GB"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_<PERSON>"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_AE"}, {"value": "https://adobe.com/go/acr_tutorials_no/", "locale": "nb_NO"}, {"value": "https://adobe.com/go/acr_tutorials_fi/", "locale": "fi_FI"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_IL"}, {"value": "https://adobe.com/go/acr_tutorials_br/", "locale": "pt_BR"}, {"value": "https://adobe.com/go/acr_tutorials_jp/", "locale": "ja_<PERSON>"}, {"value": "https://adobe.com/go/acr_tutorials_es/", "locale": "es_MX"}, {"value": "https://adobe.com/go/acr_tutorials_se/", "locale": "sv_SE"}, {"value": "https://adobe.com/go/acr_tutorials_in", "locale": "in_IN"}, {"value": "https://adobe.com/go/acr_tutorials_cz/", "locale": "cs_CZ"}, {"value": "https://adobe.com/go/acr_tutorials_it/", "locale": "it_IT"}, {"value": "https://adobe.com/go/acr_tutorials_de/", "locale": "de_DE"}, {"value": "https://adobe.com/go/acr_tutorials_kr/", "locale": "ko_KR"}, {"value": "https://adobe.com/go/acr_tutorials_ua/", "locale": "uk_UA"}]}, "Prod": {"Language": [{"value": "https://adobe.com/go/acr_tutorials_ru/", "locale": "ru_RU"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_CA"}, {"value": "https://adobe.com/go/acr_tutorials_hu/", "locale": "hu_HU"}, {"value": "https://adobe.com/go/acr_tutorials_tr/", "locale": "tr_TR"}, {"value": "https://adobe.com/go/acr_tutorials_tw/", "locale": "zh_TW"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_FR"}, {"value": "https://adobe.com/go/acr_tutorials_pl/", "locale": "pl_PL"}, {"value": "https://adobe.com/go/acr_tutorials_dk/", "locale": "da_DK"}, {"value": "https://adobe.com/go/acr_tutorials_es/", "locale": "es_ES"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_US"}, {"value": "https://adobe.com/go/acr_tutorials_cn/", "locale": "zh_CN"}, {"value": "https://adobe.com/go/acr_tutorials_nl/", "locale": "nl_NL"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_GB"}, {"value": "https://adobe.com/go/acr_tutorials_fr/", "locale": "fr_<PERSON>"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_AE"}, {"value": "https://adobe.com/go/acr_tutorials_no/", "locale": "nb_NO"}, {"value": "https://adobe.com/go/acr_tutorials_fi/", "locale": "fi_FI"}, {"value": "https://adobe.com/go/acr_tutorials/", "locale": "en_IL"}, {"value": "https://adobe.com/go/acr_tutorials_br/", "locale": "pt_BR"}, {"value": "https://adobe.com/go/acr_tutorials_jp/", "locale": "ja_<PERSON>"}, {"value": "https://adobe.com/go/acr_tutorials_es/", "locale": "es_MX"}, {"value": "https://adobe.com/go/acr_tutorials_se/", "locale": "sv_SE"}, {"value": "https://adobe.com/go/acr_tutorials_in", "locale": "in_IN"}, {"value": "https://adobe.com/go/acr_tutorials_cz/", "locale": "cs_CZ"}, {"value": "https://adobe.com/go/acr_tutorials_it/", "locale": "it_IT"}, {"value": "https://adobe.com/go/acr_tutorials_de/", "locale": "de_DE"}, {"value": "https://adobe.com/go/acr_tutorials_kr/", "locale": "ko_KR"}, {"value": "https://adobe.com/go/acr_tutorials_ua/", "locale": "uk_UA"}]}}, "MinimumSupportedClientVersion": "4.5.0.300", "InstallDir": {"value": "[AdobeCommon]"}}