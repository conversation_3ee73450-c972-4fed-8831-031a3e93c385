#!/usr/bin/env python3
"""
🧪 Test Script for New DermatoGemma Sidebar Interface
Tests the improved disease selection interface without redundancy
"""

import sys
import tkinter as tk
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Apply warning suppressions
try:
    from fix_warnings import apply_runtime_fixes
    apply_runtime_fixes()
except ImportError:
    import warnings
    import os
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

def test_new_sidebar():
    """Test the new sidebar interface"""
    try:
        print("🧪 Testing New DermatoGemma Sidebar Interface...")
        print("=" * 60)
        
        # Import the modern UI
        from ui_modern import ModernDermatoGemmaUI
        
        print("✅ UI module imported successfully")
        
        # Create the application
        app = ModernDermatoGemmaUI()
        print("✅ Application created successfully")
        
        # Test disease categories
        print("\n🏥 Testing Disease Categories:")
        for category, diseases in app.disease_categories.items():
            print(f"   {category}:")
            for disease_key, disease_name in diseases:
                print(f"      - {disease_name} ({disease_key})")
        
        print(f"\n📊 Total diseases available: {sum(len(diseases) for diseases in app.disease_categories.values())}")
        
        # Test analysis mode functionality
        print("\n🔬 Testing Analysis Mode Functionality:")
        print(f"   - Default mode: {app.analysis_mode.get()}")
        print(f"   - Default disease: {app.selected_disease.get()}")
        
        # Test mode switching
        print("\n🔄 Testing Mode Switching:")
        
        # Switch to single mode
        app.analysis_mode.set("single")
        app.on_analysis_mode_change()
        print("   ✅ Switched to targeted analysis mode")
        
        # Switch back to multi mode
        app.analysis_mode.set("multi")
        app.on_analysis_mode_change()
        print("   ✅ Switched to comprehensive analysis mode")
        
        print("\n🎯 Interface Features:")
        print("   ✅ Clean disease categorization (High/Medium/Low priority)")
        print("   ✅ Direct disease selection without redundancy")
        print("   ✅ Intuitive mode switching")
        print("   ✅ Scrollable disease selection")
        print("   ✅ Visual priority indicators")
        
        print("\n🚀 Starting Interactive Test...")
        print("   - Use the interface to test disease selection")
        print("   - Try switching between analysis modes")
        print("   - Select different diseases from categories")
        print("   - Close the window when done testing")
        
        # Run the application
        app.root.mainloop()
        
        print("\n✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🏥 DermatoGemma New Sidebar Interface Test")
    print("=" * 50)
    
    success = test_new_sidebar()
    
    if success:
        print("\n🎉 All tests passed!")
        print("✨ New sidebar interface is working correctly!")
        print("\n📋 Key Improvements:")
        print("   • Eliminated redundant disease selection options")
        print("   • Organized diseases by medical priority")
        print("   • Cleaner, more intuitive interface")
        print("   • Direct selection without confusion")
        print("   • Better visual organization")
    else:
        print("\n❌ Some tests failed!")
        print("Please check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
