import 'package:json_annotation/json_annotation.dart';

part 'community_feedback.g.dart';

/// Community feedback model for translation improvements
@JsonSerializable()
class CommunityFeedback {
  final String id;
  final String userId;
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final FeedbackType feedbackType;
  final String? improvedTranslation;
  final String? comment;
  final int? rating; // 1-5 stars
  final DateTime timestamp;
  final bool isVerified;
  final int upvotes;
  final int downvotes;
  final List<String> tags;

  CommunityFeedback({
    required this.id,
    required this.userId,
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.feedbackType,
    this.improvedTranslation,
    this.comment,
    this.rating,
    required this.timestamp,
    this.isVerified = false,
    this.upvotes = 0,
    this.downvotes = 0,
    this.tags = const [],
  });

  /// Create from JSON
  factory CommunityFeedback.fromJson(Map<String, dynamic> json) =>
      _$CommunityFeedbackFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$CommunityFeedbackToJson(this);

  /// Create a copy with modified fields
  CommunityFeedback copyWith({
    String? id,
    String? userId,
    String? originalText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    FeedbackType? feedbackType,
    String? improvedTranslation,
    String? comment,
    int? rating,
    DateTime? timestamp,
    bool? isVerified,
    int? upvotes,
    int? downvotes,
    List<String>? tags,
  }) {
    return CommunityFeedback(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      feedbackType: feedbackType ?? this.feedbackType,
      improvedTranslation: improvedTranslation ?? this.improvedTranslation,
      comment: comment ?? this.comment,
      rating: rating ?? this.rating,
      timestamp: timestamp ?? this.timestamp,
      isVerified: isVerified ?? this.isVerified,
      upvotes: upvotes ?? this.upvotes,
      downvotes: downvotes ?? this.downvotes,
      tags: tags ?? this.tags,
    );
  }

  /// Get feedback type display name
  String get feedbackTypeDisplay {
    switch (feedbackType) {
      case FeedbackType.improvement:
        return 'Melhoria';
      case FeedbackType.correction:
        return 'Correção';
      case FeedbackType.alternative:
        return 'Alternativa';
      case FeedbackType.context:
        return 'Contexto';
      case FeedbackType.cultural:
        return 'Cultural';
      case FeedbackType.technical:
        return 'Técnico';
      case FeedbackType.rating:
        return 'Avaliação';
    }
  }

  /// Get rating display
  String? get ratingDisplay {
    if (rating == null) return null;
    return '$rating/5 ⭐';
  }

  /// Get vote score
  int get voteScore => upvotes - downvotes;

  /// Get vote ratio
  double get voteRatio {
    final total = upvotes + downvotes;
    if (total == 0) return 0.0;
    return upvotes / total;
  }

  /// Check if feedback has improvement suggestion
  bool get hasImprovement => improvedTranslation != null && improvedTranslation!.isNotEmpty;

  /// Check if feedback has comment
  bool get hasComment => comment != null && comment!.isNotEmpty;

  /// Check if feedback has rating
  bool get hasRating => rating != null;

  /// Get quality score based on various factors
  double get qualityScore {
    double score = 0.0;
    
    // Base score for verified feedback
    if (isVerified) score += 0.3;
    
    // Vote ratio bonus
    score += voteRatio * 0.4;
    
    // Content quality bonus
    if (hasImprovement) score += 0.2;
    if (hasComment && comment!.length > 20) score += 0.1;
    
    return score.clamp(0.0, 1.0);
  }

  /// Get time since feedback was submitted
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} dias atrás';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} horas atrás';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutos atrás';
    } else {
      return 'Agora mesmo';
    }
  }

  @override
  String toString() {
    return 'CommunityFeedback(id: $id, type: $feedbackType, score: $voteScore)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityFeedback && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Types of community feedback
enum FeedbackType {
  improvement,  // Suggestion for better translation
  correction,   // Correction of error
  alternative,  // Alternative translation
  context,      // Additional context needed
  cultural,     // Cultural adaptation suggestion
  technical,    // Technical terminology feedback
  rating,       // Simple rating without text
}

/// Feedback priority levels
enum FeedbackPriority {
  low,
  medium,
  high,
  critical,
}

/// Feedback status in moderation queue
enum FeedbackStatus {
  pending,
  approved,
  rejected,
  flagged,
}
